{"index_patterns": ["*-application-logs"], "template": {"settings": {"index": {"routing": {"allocation": {"include": {"_tier_preference": "data_content"}}}}}, "mappings": {"dynamic": "true", "dynamic_date_formats": ["strict_date_optional_time", "yyyy/MM/dd HH:mm:ss Z||yyyy/MM/dd Z"], "dynamic_templates": [{"strings_as_keywords": {"match_mapping_type": "string", "mapping": {"type": "keyword"}}}, {"catch_all_objects": {"match_mapping_type": "object", "mapping": {"type": "object", "enabled": false}}}], "date_detection": true, "numeric_detection": true, "properties": {"request": {"properties": {"body": {"type": "flattened"}, "query": {"type": "flattened"}}}, "reply": {"properties": {"payload": {"type": "flattened"}}}, "error": {"properties": {"code": {"type": "keyword"}}}}}}, "priority": 90}
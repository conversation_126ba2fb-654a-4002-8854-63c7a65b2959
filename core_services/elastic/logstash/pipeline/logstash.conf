input {
  beats {
    port => "${LOGSTASH_PORT}"
  }
}


filter {
  # Add tag for different log types
  if [fields][log_type] == "application_logs" {
    mutate {
      add_tag => ["application_logs"]
    }
  }
  else if [fields][log_type] == "postgres_logs" {
    mutate {
      add_tag => ["postgres_logs"]
    }
  }

  # Escape and clean malformed JSON in the message field
  mutate {
    gsub => [
      "message", "\\n", " ",   # Replace newlines with spaces
      "message", "\\\"", "\"" # Escape double quotes
    ]
  }

  json {
    source => "message"
  }

  ruby {
    code => '
      error = event.get("error")
      if error.is_a?(Hash)
        ["parameters", "original", "parent"].each do |key|
          if error[key].is_a?(Hash) && error[key]["parameters"].is_a?(Array)
            error[key]["parameters"] = error[key]["parameters"].map(&:to_s).join(",")
          end
        end
        if error["parameters"].is_a?(Array)
          error["parameters"] = error["parameters"].map(&:to_s).join(",")
        end
        event.set("error", error)
      end
    '
  }

  date {
    match => [ "time", "ISO8601" ]
    target => "@timestamp"
  }

  # Remove unnecessary Filebeat fields
  mutate {
    uppercase => ["level-label"]
    remove_field => [
      "agent",
      "ecs",
      "input",
      "log",
      "prospector",
      "beat",
      "source",
      "offset"
    ]
  }
}

output {
  if "application_logs" in [tags] {
    elasticsearch {
      index => "%{service}-application-logs"
      hosts => "${ELASTIC_HOSTS}"
      user => "${ELASTIC_USER}"
      password => "${ELASTIC_PASSWORD}"
      ssl_certificate_authorities => "certs/ca/ca.crt"
      template => "/usr/share/logstash/config/elasticsearch/application-logs-template.json"
      template_name => "application-logs-template"
      template_overwrite => true
      manage_template => true
    }
  }
  else if "postgres_logs" in [tags] {
    elasticsearch {
      index => "%{service}-postgres-logs"
      hosts => "${ELASTIC_HOSTS}"
      user => "${ELASTIC_USER}"
      password => "${ELASTIC_PASSWORD}"
      ssl_certificate_authorities => "certs/ca/ca.crt"
    }
  }
}

#!/bin/bash

# Wait for the config server replica set to elect a primary
echo "Waiting for config server replica set to elect a primary..."
until mongosh --authenticationDatabase "admin" -u ${MONGO_ROOT_USERNAME} -p ${MONGO_ROOT_PASSWORD} --host rs-config-server/mongo-configsvr:27017 --eval 'rs.status().members.some(m => m.stateStr === "PRIMARY")' | grep -q 'true'; do
  sleep 5
done

# Wait for each shard's replica set to elect a primary
for shard in 1 2; do
  replica_set="rs-shard-${shard}"
  host_prefix="mongo-shard${shard}"
  echo "Waiting for ${replica_set} to elect a primary..."
  until mongosh --authenticationDatabase "admin" -u ${MONGO_ROOT_USERNAME} -p ${MONGO_ROOT_PASSWORD} --host ${replica_set}/${host_prefix}:27017 --eval 'rs.status().members.some(m => m.stateStr === "PRIMARY")' | grep -q 'true'; do
    sleep 5
  done
done

# Start mongos in the background
echo "Starting mongos..."
mongos --port 27017 --configdb rs-config-server/mongo-configsvr:27017 --bind_ip_all --keyFile /data/mongodb-keyfile &

# Wait for mongos to become available
echo "Waiting for mongos to start..."
until mongosh --port 27017 --eval 'db.adminCommand({ping: 1})' &> /dev/null; do
  sleep 5
done

# Add the shards using the provided commands
echo "Adding shards..."
mongosh --port 27017 <<EOF
use admin
db.auth("${MONGO_ROOT_USERNAME}", "${MONGO_ROOT_PASSWORD}")
sh.addShard("rs-shard-1/mongo-shard1:27017")
sh.addShard("rs-shard-2/mongo-shard2:27017")
sh.enableSharding("${MONGO_DB}");
EOF

# Keep the mongos process running in the foreground
wait
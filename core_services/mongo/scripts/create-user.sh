#!/bin/bash
set -e

echo "Create admin user";

mongosh --port 27017 --eval "
  db.getSiblingDB('admin').createUser({
    user: '${MONGO_ROOT_USERNAME}',
    pwd: '${MONGO_ROOT_PASSWORD}',
    roles: [ { role: 'root', db: 'admin' } ]
  });
" || { echo "Failed to create admin user"; }

mongosh --port 27017 -u "${MONGO_ROOT_USERNAME}" -p "${MONGO_ROOT_PASSWORD}" --authenticationDatabase "admin" --eval "
  db.runCommand({ connectionStatus: 1 })
" || { echo "Failed to authenticate as admin user"; exit 1; }

echo "Create app user";
mongosh --port 27017 -u "${MONGO_ROOT_USERNAME}" -p "${MONGO_ROOT_PASSWORD}" --authenticationDatabase "admin" --eval "
  db.getSiblingDB('qply-db').createUser({
    user: '${MONGO_USER_USERNAME}',
    pwd: '${MONGO_USER_PASSWORD}',
    roles: [
      { role: 'readWrite', db: 'qply-db' },
      { role: 'enableSharding', db: 'admin' }
    ]
  });
" || { echo "Failed to create app user"; }

mongosh --port 27017 -u "${MONGO_USER_USERNAME}" -p "${MONGO_USER_PASSWORD}" --authenticationDatabase "qply-db" --eval "
  db.runCommand({ connectionStatus: 1 })
" || { echo "Failed to authenticate as app user"; exit 1; }

echo "Users created successfully";
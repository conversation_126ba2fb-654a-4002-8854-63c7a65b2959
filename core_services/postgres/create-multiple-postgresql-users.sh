#!/bin/bash

set -e
set -u

# Create a user, if they don't already exist
# Arguments:
#   $1 - user name
function create_user_from_env() {
	local user=$1
	local password=$(get_password_from_env $user)
	if user_exists $user; then
		echo "User '$user' already exists, skipping"
	else
		echo "Creating user '$user' ${password:+(with password)}"
		create_user $user $password
	fi
}

# Create a PostgreSQL user
# Arguments:
#   $1 - User name
#   $2 - User password (optional)
function create_user() {
	local user=$1
	local password=$2
	psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
		CREATE USER "$user" ${password:+WITH PASSWORD '$password'};
	EOSQL

	# TODO: custom privilege
	psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
		GRANT ALL PRIVILEGES ON DATABASE "$POSTGRES_DB" TO "$user";
		GRA<PERSON> ALL ON SCHEMA public TO "$user";
	EOSQL
}

# Check if a PostgreSQL user exists
# Arguments:
#   $1 - User name
# Returns:
#   0 if user exists, 1 otherwise
function user_exists() {
	local user=$1
	local result
	result=$(psql -t -v ON_ERROR_STOP=1 \
		--username "$POSTGRES_USER" \
		--dbname "$POSTGRES_DB" \
		-c "SELECT 1 FROM pg_roles WHERE rolname = '$user';")
	if [[ "$result" =~ 1 ]]; then
		return 0
	fi
	return 1
}

# Retrieve the password for a given user
# Arguments:
#   $1 - user name
# Returns:
#   Password derived from environment variables or empty if not set
function get_password_from_env() {
    local user=$1
    local password_var="POSTGRES_PASSWORD_${user^^}"
    echo "${!password_var:-}"
}

function grant_assign_default_privileges() {
	local user1=$1
	local user2=$2

	echo "GRANT $user1 privilege to $user2"
	# TODO: custom privilege
	psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
		ALTER DEFAULT PRIVILEGES FOR ROLE "$user1" GRANT ALL PRIVILEGES ON TABLES TO "$user2";
	EOSQL
}

# Main script logic: Create multiple user if specified in the environment variable
if [ -n "$POSTGRES_MULTIPLE_USERS" ]; then
	echo "Multiple user creation requested: $POSTGRES_MULTIPLE_USERS"
	for user in $(echo $POSTGRES_MULTIPLE_USERS | tr ',' ' '); do
		create_user_from_env $user
	done

	for user1 in $(echo $POSTGRES_MULTIPLE_USERS | tr ',' ' '); do
		echo "Grant default permission: $user1"
		for user2 in $(echo $POSTGRES_MULTIPLE_USERS | tr ',' ' '); do
			if [ "$user1" = "$user2" ]; then 
				echo "skip same user: $user2"
				continue
			fi
			grant_assign_default_privileges $user1 $user2
		done
	done
	echo "Multiple user created"
fi
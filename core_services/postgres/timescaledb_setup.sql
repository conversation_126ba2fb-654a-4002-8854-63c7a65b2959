DO $$
BEGIN
    -- Attempt to create the TimescaleDB extension
    CREATE EXTENSION IF NOT EXISTS timescaledb;

    -- Check if the extension was successfully created
    IF EXISTS (SELECT 1 FROM pg_extension WHERE extname = 'timescaledb') THEN
        RAISE NOTICE 'TimescaleDB extension has been successfully created or already exists.';
    ELSE
        RAISE EXCEPTION 'Failed to create TimescaleDB extension. Please check your installation and permissions.';
    END IF;
END $$;

## PostgreSQL
POSTGRES_DB=qply-db
POSTGRES_PASSWORD=s5l2t9o49
POSTGRES_PORT=5432
POSTGRES_USER=postgres # root

## PostgreSQL - WLSAPI user
POSTGRES_PASSWORD_WLSAPI=s5l2t9o49
POSTGRES_USER_WLSAPI=wlsapi

## PostgreSQL - GAME AGGREGATOR user
POSTGRES_PASSWORD_GAME_AGGREGATOR=s5l2t9o49
POSTGRES_USER_GAME_AGGREGATOR=game_aggregator

## MongoDB
MONGO_DB=qply-db
MONGO_PORT=27017
MONGO_ROOT_PASSWORD=s5l2t9o49
MONGO_ROOT_USERNAME=root
MONGO_USER_PASSWORD=s5l2t9o49
MONGO_USER_USERNAME=qply

# ELASTIC STACK CONFIGURATION
ELASTIC_STACK_ENCRYPTION_KEY=c34d38b3a14956121ff2170e5030b471551370178f43e5626eec58b04a30fae2
ELASTIC_STACK_LICENSE=basic
ELASTIC_STACK_VERSION=8.16.1

## Elasticsearch
ELASTICSEARCH_CLUSTER_NAME=docker-cluster
ELASTICSEARCH_HOST=https://es01
ELASTICSEARCH_MEM_LIMIT=**********
ELASTICSEARCH_PASSWORD=s5l2t9o49
ELASTICSEARCH_PORT=9200
ELASTICSEARCH_USERNAME=elastic

## Kibana
KIBANA_HOST=http://kibana
KIBANA_MEM_LIMIT=**********
KIBANA_PASSWORD=s5l2t9o49
KIBANA_PORT=5601

## Logstash
LOGSTASH_HOST=http://logstash
LOGSTASH_MEM_LIMIT=**********
LOGSTASH_PORT=5044

## REDIS CONFIGURATION
REDIS_HOST=host.docker.internal
REDIS_PASSWORD=s5l2t9o49
REDIS_PORT=6379

## TYPESENSE
TYPESENSE_API_KEY=72d6f083-e74d-4921-8bfe-eede33ffebbb
TYPESENSE_PORT=8108
import i18n from '@/providers/i18n/i18n';
import {
  DataGridPremium,
  Toolbar,
  type GridLocaleText,
  type GridPremiumSlotsComponent,
} from '@mui/x-data-grid-premium';
import type { StoryContext } from '@storybook/react';
import React from 'react';
import { I18nextProvider, useTranslation } from 'react-i18next';
import { useLocaleText } from '../src/components/base/data-display/datagrid/DataGrid.util';
import { AuthProvider } from '../src/contexts/auth/auth.context';

/**
 * Decorator that wraps components with AuthProvider
 * This is necessary for components that use the useAuth hook
 */
export const withAuthProvider = (Story: React.ComponentType) => (
  <AuthProvider>
    <Story />
  </AuthProvider>
);

/**
 * Decorator that provides internationalization support for Storybook stories
 */
export const withI18next = (Story: React.ComponentType, context: StoryContext) => {
  const I18nWrapper = () => {
    const { locale } = context.globals;

    // When the locale global changes, set the new locale in i18n
    React.useEffect(() => {
      i18n.changeLanguage(locale);
    }, [locale]);

    return (
      <I18nextProvider i18n={i18n}>
        <Story />
      </I18nextProvider>
    );
  };

  return <I18nWrapper />;
};

/**
 * Mock implementation of GuestGuard for Storybook
 * This simply renders children without any authentication checks
 */
export const MockGuestGuard = ({ children }: { children: React.ReactNode }) => {
  return <>{children}</>;
};

/**
 * Mock implementation of useRouter for Storybook
 */
export const mockRouter = {
  push: () => {},
  replace: () => {},
  refresh: () => {},
  back: () => {},
  forward: () => {},
  prefetch: () => Promise.resolve(),
  pathname: '/',
  query: {},
};

/**
 * Decorator that wraps a component in a DataGridPremium with toolbar
 *
 * @param Story The story component to wrap
 * @returns The decorated story component
 */
export const withDataGrid = (Story: React.ComponentType) => {
  const CustomToolbarComponent = () => (
    <Toolbar>
      <Story />
    </Toolbar>
  );

  const DataGridWrapper = () => {
    const defaultSlotProps: Partial<GridPremiumSlotsComponent> = {
      toolbar: CustomToolbarComponent,
    };

    const { i18n, t } = useTranslation();
    const localeText: Partial<GridLocaleText> = useLocaleText(i18n.language as Language, t);

    return (
      <DataGridPremium
        columns={[
          { field: 'id', headerName: 'ID' },
          { field: 'username', headerName: 'Username' },
        ]}
        rows={[
          { id: 1, username: 'John Doe' },
          { id: 2, username: 'Jane Doe' },
          { id: 3, username: 'Richard Smith' },
        ]}
        disableRowSelectionOnClick
        disableAggregation
        showToolbar
        disablePivoting
        localeText={localeText}
        slots={defaultSlotProps}
      />
    );
  };

  return <DataGridWrapper />;
};

import { createTheme } from '@/theme';
import { CssBaseline, ThemeProvider, type Theme } from '@mui/material';
import { LicenseInfo } from '@mui/x-license';
import { withThemeFromJSXProvider } from '@storybook/addon-themes';
import type { Preview } from '@storybook/nextjs-vite';
import { withAuthProvider, withI18next } from './decorators';
import '@fontsource/inter/400.css';
import '@fontsource/inter/500.css';
import '@fontsource/inter/600.css';
import '@fontsource/inter/700.css';
import 'simplebar-react/dist/simplebar.min.css';

LicenseInfo.setLicenseKey(process.env.NEXT_PUBLIC_MUI_X_LICENSE_KEY || '');

const lightTheme: Theme = createTheme({
  colorPreset: 'ultraViolet',
  direction: 'ltr',
  layout: 'vertical-shells-light',
  paletteMode: 'light',
});

const darkTheme: Theme = createTheme({
  colorPreset: 'ultraViolet',
  direction: 'ltr',
  layout: 'vertical-shells-light',
  paletteMode: 'dark',
});

const preview: Preview = {
  parameters: {
    controls: {
      expanded: true, // Adds the description and default columns
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    docs: {
      toc: {
        disable: false,
        title: 'Table of Contents',
        headingSelector: 'h2, h3',
      },
    },
    nextjs: {
      appDirectory: true,
    },
  },

  decorators: [
    withThemeFromJSXProvider({
      GlobalStyles: CssBaseline,
      Provider: ThemeProvider,
      themes: {
        light: lightTheme,
        dark: darkTheme,
      },
      defaultTheme: 'light',
    }),
    withAuthProvider,
    withI18next,
  ],

  globalTypes: {
    locale: {
      description: 'Internationalization locale',
      toolbar: {
        icon: 'globe',
        items: [
          { value: 'en', right: '🇺🇸', title: 'English' },
          { value: 'zh', right: '🇨🇳', title: '中文' },
        ],
      },
      default: 'en',
    },
  },
  initialGlobals: {
    locale: 'en',
  },

  tags: ['autodocs'],
};

export default preview;

{"name": "log-consumer", "version": "0.0.0", "description": "Consumer of log", "main": "index.js", "type": "module", "scripts": {"test": "npm run tu", "tu": "npx vitest --config ./config/vitest.config.js ./test/unit --coverage --watch=false --silent=false"}, "author": "", "license": "ISC", "devDependencies": {"@vitest/coverage-v8": "^3.1.1", "vitest": "^3.1.1"}, "dependencies": {"ajv": "^8.17.1", "kafkajs": "^2.2.4", "mongodb": "6.15"}}
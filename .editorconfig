# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Default settings for all files
[*]
charset = utf-8
end_of_line = lf
indent_style = space
indent_size = 2
insert_final_newline = true
trim_trailing_whitespace = true
max_line_length = 100

# TypeScript/JavaScript files
[*.{ts,tsx,js,jsx,mjs,cjs}]
quote_type = single
curly_bracket_next_line = false
spaces_around_operators = true
spaces_around_brackets = inside
indent_brace_style = K&R

# JSON files
[*.{json,jsonc}]
insert_final_newline = false

# YAML files
[*.{yml,yaml}]
quote_type = single

# Markdown files
[*.{md,mdx}]
trim_trailing_whitespace = false
max_line_length = off

# Shell scripts
[*.{sh,bash,zsh}]
indent_size = 4
switch_case_indent = true

# Configuration files
[{package.json,.travis.yml}]
indent_style = space
indent_size = 2

# CSS/SCSS/LESS files
[*.{css,scss,less,sass}]
quote_type = double
indent_size = 2

# HTML files
[*.{htm,html}]
indent_size = 2
quote_type = double

# GraphQL files
[*.{graphql,gql}]
indent_size = 2

# Documentation and text files
[*.txt]
indent_size = 4

# Ignore minified files
[**.min.*]
indent_style = ignore
insert_final_newline = ignore
trim_trailing_whitespace = false

# Makefiles require tabs
[Makefile]
indent_style = tab

# Git commit messages
[COMMIT_EDITMSG]
max_line_length = 72

# Batch files
[*.{cmd,bat}]
end_of_line = crlf

# Ensure we're using the correct Node version if nvm is available
if [ -f ".nvmrc" ] && command -v nvm >/dev/null 2>&1; then
  nvm use
fi

# Add color to output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo "${GREEN}Validating commit message...${NC}"

# Run commitlint with proper error handling
if ! npx --no -- commitlint --config ./config/commitlint.config.ts --edit "$1"; then
  echo "${RED}Commit message validation failed. Please fix your commit message according to the conventional commit format.${NC}"
  echo "${GREEN}Example: feat(scope): add new feature${NC}"
  exit 1
fi

echo "${GREEN}Commit message is valid!${NC}"

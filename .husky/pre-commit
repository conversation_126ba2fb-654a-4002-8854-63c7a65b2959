# Ensure we're using the correct Node version if nvm is available
if [ -f ".nvmrc" ] && command -v nvm >/dev/null 2>&1; then
  nvm use || exit 1
fi

# Add color to output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo "${GREEN}Running pre-commit checks...${NC}"

# Run lint-staged with error handling
if ! npx lint-staged; then
  echo "${RED}Pre-commit checks failed. Please fix the issues and try committing again.${NC}"
  exit 1
fi

echo "${GREEN}Pre-commit checks passed!${NC}"

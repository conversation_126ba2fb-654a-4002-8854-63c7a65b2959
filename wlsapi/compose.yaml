# include:
#   - ../core_services/compose.yaml  # Include core services

services:
  # Runs the WLSAPP application which is pulling image from AWS ECR
  wls-api:
    image: "060830304044.dkr.ecr.ap-southeast-1.amazonaws.com/boilerplate/fastify-app:${IMAGE_TAG}"
    container_name: wls-api
    # depends_on:
    #   postgres:
    #     condition: service_healthy
    #   mongo:
    #     condition: service_healthy
    #   cache:
    #     condition: service_healthy
    env_file:
      - .env
    ports:
      - "${DOCKER_PORT}:${FASTIFY_PORT}"
    labels:
      - "com.centurylinklabs.watchtower.enable=true" # enable watchtower auto update
    extra_hosts:
      - "host.docker.internal:host-gateway"

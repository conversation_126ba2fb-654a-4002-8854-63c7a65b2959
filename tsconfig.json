{
	"compilerOptions": {
		"allowJs": true,
		"allowSyntheticDefaultImports": true,
		"baseUrl": ".",
		"esModuleInterop": true,
		"forceConsistentCasingInFileNames": true,
		"incremental": true,
		"isolatedModules": true,
		"jsx": "preserve",
		"lib": ["dom", "dom.iterable", "esnext"],
		"module": "esnext",
		"moduleResolution": "bundler",
		"noEmit": true,
		"noFallthroughCasesInSwitch": true,
		"paths": {
			"@/*": ["./src/*"]
		},
		"plugins": [
			{
				"name": "next"
			}
		],
		"resolveJsonModule": true,
		"skipLibCheck": true,
		"strict": true,
		"target": "es2022",
		"tsBuildInfoFile": "./node_modules/.cache/tsconfig.tsbuildinfo",
		"useUnknownInCatchVariables": true,
		"verbatimModuleSyntax": true,
		"types": ["d3-color", "hast", "unist", "node", "vitest/globals"],

		// Additional type checking
		"noImplicitAny": true,
		"noImplicitThis": true,
		"noImplicitReturns": true,
		"noUncheckedIndexedAccess": true,
		"noUnusedLocals": true,
		"noUnusedParameters": true,
		"strictBindCallApply": true,
		"strictFunctionTypes": true,
		"strictNullChecks": true,
		"strictPropertyInitialization": true
	},
	"include": [
		"**/*.ts",
		"**/*.tsx",
		"src/types/**/*.ts",
		"next.d.ts",
		"next-env.d.ts",
		".next/types/**/*.ts",
		"vitest.d.ts"
	],
	"exclude": ["node_modules", "build", ".next", "coverage"]
}

@baseUrl = http://localhost:9000/2015-03-31/functions/function/invocations

### Local environment
# run this if APP_ENV is "local"
# "local" will subscribe to kafka and poll the data from the data stream for 1 seconds
# no parameter is required
POST {{baseUrl}}
Content-Type: application/json

{}

### Simulate production environment (MSK)
# "production" will expect to received data from the invocator, like how MSK event source will invoke lambda function when received new data 
# the data will be placed inside the "records" object
# the first layer of the object refer to the kafka channel name and their respective partition, and it hold the data in array
# the log string need to be placed inside the "value" key and must be base64 encoded
POST {{baseUrl}}
Content-Type: application/json

{
  "eventSource": "aws:kafka",
  "eventSourceArn": "arn:aws:kafka:us-east-1:123456789012:cluster/vpc-2priv-2pub/751d2973-a626-431c-9d4e-d7975eb44dd7-2",
  "bootstrapServers": "b-2.demo-cluster-1.a1bcde.c1.kafka.us-east-1.amazonaws.com:9092,b-1.demo-cluster-1.a1bcde.c1.kafka.us-east-1.amazonaws.com:9092",
  "records": {
    "audit-trails-0": [{
      "topic": "audit-trails",
      "partition": 0,
      "offset": 15,
      "timestamp": 1545084650987,
      "timestampType": "CREATE_TIME",
      "key": "abcDEFghiJKLmnoPQRstuVWXyz1234==",
      "value": "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
      "headers": []
    }]
  }
}

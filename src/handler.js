import { Kafka } from "kafkajs";
import { processLog, saveLog } from "./util.js";


/**
 * Handles local Kafka message consumption, processing logs from a Kafka topic
 * and saving them to a MongoDB database. Connects to Kafka, subscribes to a topic,
 * processes each message batch, and organizes logs by month before saving.
 *
 * @param {Object} context - The execution context object containing request information.
 * @param {Object} context.awsRequestId - The unique identifier used as Kafka clientId.
 * @param {Object} client - The MongoDB client used for database operations.
 * @param {Object} [config=process.env] - Configuration object with Kafka connection details.
 * @param {string} config.KAFKA_BROKERS - Comma-separated list of Kafka broker addresses.
 * @param {string} config.KAFKA_GROUP_ID - Consumer group ID for the Kafka consumer.
 * @param {string} config.KAFKA_TOPIC - The Kafka topic to subscribe to.
 * @returns {Promise<void>} - Resolves when the handler completes processing or encounters an error.
 */
export const localHandler = async (context, client, config = process.env) => {
  const kafka = new Kafka({
    clientId: context.awsRequestId,
    brokers: config.KAFKA_BROKERS?.split(","),
  });

  const consumer = kafka.consumer({ groupId: config.KAFKA_GROUP_ID });

  try {
    await consumer.connect();
    await consumer.subscribe({ topic: config.KAFKA_TOPIC, fromBeginning: true });

    await consumer.run({
      eachBatchAutoResolve: true,
      eachBatch: async ({ batch, heartbeat }) => {
        // Process each record in the partition
        let auditTrailsByMonth = {};
        for (let message of batch.messages) {
          // process log
          const logString = message.value.toString();

          // Handle empty string
          if (logString === '') continue;

          const processedLog = processLog(logString);
          if (!processedLog?.key || !processedLog?.value) continue;

          if (!auditTrailsByMonth[processedLog.key]) {
            auditTrailsByMonth[processedLog.key] = [];
          }
          auditTrailsByMonth[processedLog.key].push(processedLog.value);

          // heartbeat to inform alive
          await heartbeat();
        }

        await saveLog(auditTrailsByMonth, client);
      },
    });

    // Keep the consumer running, or add a timeout if needed.
    await new Promise((resolve) => setTimeout(resolve, 1000));
  } catch (error) {
    console.error("Error:", error);
  } finally {
    await consumer.disconnect();
  }
}

/**
 * Handles AWS Lambda MSK events by processing messages from an event source,
 * decoding each message, and saving the processed logs to a database.
 *
 * @param {Object} event - The MSK event object containing records to be processed.
 * @param {Object} event.records - The records organized by topic to be processed.
 * @param {Object} client - The MongoDB client used for saving logs.
 * @returns {Promise<void>} A promise that resolves when all logs have been processed and saved.
 */
export const lambdaHandler = async (event, client) => {
  for (const [topic, topicRecords] of Object.entries(event.records)) {
    // Process each record in the partition
    let auditTrailsByMonth = {};
    for (const record of topicRecords) {
      // Decode the message value from base64
      const logString = Buffer.from(record.value, "base64").toString();

      const processedLog = processLog(logString);
      if (!processedLog?.key || !processedLog?.value) continue;

      if (!auditTrailsByMonth[processedLog.key]) {
        auditTrailsByMonth[processedLog.key] = [];
      }
      auditTrailsByMonth[processedLog.key].push(processedLog.value);
    }

    await saveLog(auditTrailsByMonth, client);
  }
}
// errors/accessControlErrors.js
import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { createModuleErrors } from '#src/utils/error.util.js';

const ACCESS_CONTROL_ERROR_DEF = {
  duplicate: ['53409', 'Access Control with IP address %s already exists ', 409],
  notFound: ['53404', 'Access Control not found with ID: %s', 404],
};

export const accessControlError = createModuleErrors(
  MODULE_NAMES.ACCESS_CONTROL,
  ACCESS_CONTROL_ERROR_DEF,
);

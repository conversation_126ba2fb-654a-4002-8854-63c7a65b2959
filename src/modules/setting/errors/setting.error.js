import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { createModuleErrors } from '#src/utils/error.util.js';

const SETTING_ERROR_DEF = {
  invalidData: ['52400', '%s', 400],
  accessDenied: ['52403', 'error.settings.sentence.accessDenied', 403],
  notFound: ['52404', 'error.settings.sentence.notFound', 404],
  unsupportedInfo: ['52422', 'error.settings.sentence.unsupportedInfo', 422],
};

export const settingError = createModuleErrors(MODULE_NAMES.SETTING, SETTING_ERROR_DEF);

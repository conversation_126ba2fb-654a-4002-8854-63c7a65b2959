import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { createModuleErrors } from '#src/utils/error.util.js';

const DEVELOPER_HUB_ERROR_DEF = {
  apiKeyExists: ['54409', 'Developer Hub with API key already exists ', 409],
  notFound: ['54404', 'Developer Hub not found with ID: %s', 404],
  invalidData: ['54422', '%s', 422],
};

export const developerHubError = createModuleErrors(
  MODULE_NAMES.DEVELOPER_HUB,
  DEVELOPER_HUB_ERROR_DEF,
);

import { AccessControlError } from '#src/modules/setting/errors/index.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { IpAccessControlRepository } from '#src/modules/setting/repository/index.js';
import { RemarkRepository } from '#src/modules/core/repository/index.js';
import { withTransaction } from '#src/utils/db-transaction.util.js';

const {
  REMARKABLE_TYPE: { IP_ACCESS_CONTROL },
  REMARK_TYPE: { NOTE },
} = CoreConstant;

/**
 * Retrieves all IP access control entries.
 * @param {Object} request - The request object.
 * @returns {Promise<Object>} A promise that resolves to the paginated list of IP access control entries.
 */
export const index = async (request) => {
  const {
    entity: { id: entityId },
    server,
  } = request;

  const query = {
    ...request.query,
    filter_parentId_eq: entityId,
  };

  return await IpAccessControlRepository.findAll(server, query);
};

/**
 * Retrieves a specific IP access control entry by ID.
 * @param {Object} request - The request object containing params and body.
 * @returns {Promise<Object>} A promise that resolves to the IP access control entry.
 * @throws {AccessControlError} If the entry is not found or there's a version conflict.
 */
export const view = async (request) => {
  const {
    params: { id },
    server,
  } = request;

  const ipAccessControl = await IpAccessControlRepository.findById(server, id);
  if (!ipAccessControl) {
    throw AccessControlError.notFound(id);
  }

  return ipAccessControl;
};

/**
 * Creates a new IP access control entry.
 *
 * @param {Object} request - The request object containing the necessary data.
 * @param {Object} request.body - The body of the request containing the access control data.
 * @param {Object} request.entity - The entity object associated with the request.
 * @param {string} request.entity.id - The ID of the entity.
 * @param {Object} request.server - The server object.
 * @param {Object} request.authInfo - The authInfo object associated with the request.
 * @param {Object} [options={}] - Additional options for the transaction.
 * @returns {Promise<Object>} A promise that resolves to the created IP access control entry.
 */
export const create = async (request, options = {}) => {
  const {
    body,
    entity: { id: entityId },
    server,
    authInfo,
  } = request;
  const { parentId, remark, ...restBody } = body;
  const accessControlData = {
    ...restBody,
    parentId: parentId || entityId,
  };

  return withTransaction(server, options, async (transaction) => {
    const createdIpAccessControl = await IpAccessControlRepository.create(
      server,
      accessControlData,
      { transaction, authInfoId: authInfo.id },
    );

    if (remark && createdIpAccessControl) {
      await RemarkRepository.create(
        server,
        {
          remarkableId: createdIpAccessControl.id,
          remarkableType: IP_ACCESS_CONTROL,
          type: NOTE,
          content: remark,
        },
        { transaction, authInfoId: authInfo.id },
      );

      await createdIpAccessControl.reload({
        include: [
          {
            model: server.psql.Remark,
            as: 'activeRemark',
          },
        ],
        transaction,
      });
    }

    return createdIpAccessControl;
  });
};

/**
 * Updates an existing IP access control entry.
 * @param {Object} request - The request object containing params, body, and entity information.
 * @returns {Promise<Object>} A promise that resolves to the updated IP access control entry.
 * @throws {AccessControlError} If an entry with the same IP already exists for a different ID.
 */
export const update = async (request, options = {}) => {
  const { body, server, authInfo } = request;
  const { remark, validityDate, ...restBody } = body;

  const accessControl = await view(request);
  return withTransaction(server, options, async (transaction) => {
    const updatedBody = {
      ...restBody,
      validityDate: !validityDate ? null : validityDate,
    };

    const updatedAccessControl = await IpAccessControlRepository.update(
      accessControl,
      updatedBody,
      {
        transaction,
        authInfoId: authInfo.id,
      },
    );

    if (remark) {
      if (!accessControl.activeRemark || accessControl.activeRemark.content !== remark) {
        await RemarkRepository.create(
          server,
          {
            remarkableId: accessControl.id,
            remarkableType: IP_ACCESS_CONTROL,
            type: NOTE,
            content: remark,
          },
          { transaction, authInfoId: authInfo.id },
        );
      }
    } else {
      // If no remark is provided, check if an active remark exists and archive it
      const activeRemark = await RemarkRepository.findActiveByRemarkable(
        request.server,
        accessControl.id,
        CoreConstant.REMARKABLE_TYPE.IP_ACCESS_CONTROL,
        { transaction },
      );
      if (activeRemark) {
        await RemarkRepository.archive(activeRemark, { transaction });
      }
    }

    return updatedAccessControl;
  });
};

/**
 * Updates the status of an IP access control entry.
 * @param {Object} request - The request object containing params and body.
 * @returns {Promise<Object>} A promise that resolves to the updated IP access control entry.
 */
export const updateStatus = async (request) => {
  const { body, authInfo } = request;
  const accessControl = await view(request);
  const updatedAccessControl = await IpAccessControlRepository.update(accessControl, body, {
    authInfoId: authInfo.id,
  });
  return updatedAccessControl;
};

/**
 * Manages bulk operations for IP access controls, including creating, updating, and removing entries.
 *
 * @param {Object} request - The request object containing necessary data for bulk management.
 * @param {Object} request.body - The body of the request.
 * @param {string} request.body.parentId - The parent ID for the IP access controls.
 * @param {string} request.body.ruleType - The rule type for the IP access controls.
 * @param {Array} request.body.ipAccessControls - An array of IP access control objects to manage.
 * @param {Object} request.server - The server object.
 * @param {Object} request.user - The user object associated with the request.
 * @param {Object} request.entity - The entity object associated with the request.
 * @param {Object} [options={}] - Additional options for the transaction.
 * @returns {Promise<void>} A promise that resolves when all bulk operations are completed.
 */
export const bulkManage = async (request, options = {}) => {
  const { body, server, authInfo, entity } = request;
  const { parentId, ruleType, ipAccessControls } = body;

  // Delete all existing IP addresses under the parentId
  await IpAccessControlRepository.removeAll(server, parentId);

  // Track of unique IP addresses
  const uniqueIpAddresses = new Set();
  const uniqueIpAccessControls = [];

  for (const ipAccessControl of ipAccessControls) {
    const { ipAddress } = ipAccessControl;

    if (!uniqueIpAddresses.has(ipAddress)) {
      uniqueIpAddresses.add(ipAddress);
      uniqueIpAccessControls.push(ipAccessControl);
    }
  }

  for (const ipAccessControl of uniqueIpAccessControls) {
    const { ipAddress, remark } = ipAccessControl;
    const createRequest = {
      body: { parentId, ruleType, ipAddress, remark },
      server,
      authInfo,
      entity,
    };
    await create(createRequest);
  }
};

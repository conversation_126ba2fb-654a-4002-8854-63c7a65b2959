import { ApiRightRepository } from '#src/modules/setting/repository/index.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';

const { PERMISSION_FIELDS } = CoreConstant;

/**
 * Processes API rights by converting them into a standardized format.
 *
 * @param {Array|Object} apiRights - The API rights to process. Can be an array of rights or a single right object.
 * @param {string|number} parentId - The ID of the parent entity associated with these rights.
 * @returns {Array} An array of processed API rights objects, each containing:
 *                  - parentId: The ID of the parent entity.
 *                  - moduleId: The ID of the module associated with the right.
 *                  - Processed permission fields: Boolean values for each permission in PERMISSION_FIELDS.
 */
const processApiRights = (apiRights, parentId) => {
  const apiRightArray = Array.isArray(apiRights) ? apiRights : [apiRights];
  return apiRightArray.map(({ moduleId, permissions }) => {
    // Initialize all permissions to false
    const processedPermissions = PERMISSION_FIELDS.reduce((acc, field) => {
      acc[field] = false;
      return acc;
    }, {});

    // Set provided permissions to true
    permissions.forEach((permission) => {
      if (PERMISSION_FIELDS.includes(permission)) {
        processedPermissions[permission] = true;
      }
    });

    return {
      parentId,
      moduleId,
      ...processedPermissions,
    };
  });
};

/**
 * Creates new API rights based on the provided request data.
 *
 * @async
 * @param {Object} request - The request object containing the creation information.
 * @param {Object} request.body - The body of the request.
 * @param {Array|Object} request.body.apiRights - The API rights to be created.
 * @param {string|number} request.body.parentId - The ID of the parent entity.
 * @param {Object} request.server - The server object.
 * @param {Object} request.authInfo - Authentication information.
 * @param {string|number} request.authInfo.id - The ID of the authenticated user.
 * @param {Object} [options={}] - Additional options for the create operation.
 * @param {Object} [options.transaction] - The database transaction to use.
 * @returns {Promise<Object[]>} A promise that resolves to an array of created API rights.
 */
export const create = async (request, options = {}) => {
  const {
    body: { apiRights, parentId },
    server,
    authInfo: { id: authInfoId },
  } = request;
  const { transaction } = options;

  const createData = processApiRights(apiRights, parentId);
  return await ApiRightRepository.upsert(server, createData, { transaction, authInfoId });
};

/**
 * Updates API rights for a given parent and removes any rights not included in the update.
 *
 * @async
 * @param {Object} request - The request object containing the update information.
 * @param {Object} request.body - The body of the request.
 * @param {Array|Object} request.body.apiRights - The API rights to be updated.
 * @param {string|number} request.body.parentId - The ID of the parent entity.
 * @param {Object} request.server - The server object.
 * @param {Object} request.authInfo - Authentication information.
 * @param {string|number} request.authInfo.id - The ID of the authenticated user.
 * @param {Object} [options={}] - Additional options for the update operation.
 * @param {Object} [options.transaction] - The database transaction to use.
 * @returns {Promise<Object[]>} A promise that resolves to an array of created/updated API rights.
 */
export const update = async (request, options = {}) => {
  const {
    body: { apiRights, parentId },
    server,
    authInfo: { id: authInfoId },
  } = request;
  const { transaction } = options;

  const createData = processApiRights(apiRights, parentId);
  const createdRights = await ApiRightRepository.upsert(server, createData, {
    transaction,
    authInfoId,
  });

  const moduleIds = createData.map(({ moduleId }) => moduleId).filter(Boolean);
  await ApiRightRepository.remove(server, parentId, moduleIds, { transaction });

  return createdRights;
};

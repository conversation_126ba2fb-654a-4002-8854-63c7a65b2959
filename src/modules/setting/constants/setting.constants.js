/**
 * Categories for application settings.
 */
export const SETTING_CATEGORIES = {
  PERSONAL: 'personal',
  SAFETY: 'safety',
  THEMES: 'themes',
};

/**
 * Positions for toast notifications.
 */
export const TOAST_POSITIONS = {
  TOP_LEFT: {
    label: 'Top Left',
    value: 'top left',
  },
  TOP_RIGHT: {
    label: 'Top Right',
    value: 'top right',
  },
  BOTTOM_LEFT: {
    label: 'Bottom Left',
    value: 'bottom left',
  },
  BOTTOM_RIGHT: {
    label: 'Bottom Right',
    value: 'bottom right',
  },
};

/**
 * Positions for alert bars.
 */
export const ALERT_POSITIONS = {
  TOP: {
    label: 'Top',
    value: 'top',
  },
  BOTTOM: {
    label: 'Bottom',
    value: 'bottom',
  },
};

/**
 * Appearance modes for the UI.
 */
export const APPEARANCE_MODES = {
  SYSTEM: {
    label: 'System',
    value: 'system',
  },
  LIGHT: {
    label: 'Light',
    value: 'light',
  },
  DARK: {
    label: 'Dark',
    value: 'dark',
  },
};

/**
 * Time format options.
 */
export const TIME_FORMATS = {
  TWELVE_HOUR: {
    label: '12-hour',
    value: '12',
  },
  TWENTY_FOUR_HOUR: {
    label: '24-hour',
    value: '24',
  },
};

/**
 * Page size options for pagination.
 */
export const RECORDS_PER_PAGE = [
  { label: '25', value: '25' },
  { label: '100', value: '100' },
  { label: '150', value: '150' },
  { label: '200', value: '200' },
];

/**
 * Supported date format options.
 */
export const DATE_FORMATS = {
  ISO_DASH: {
    label: 'YYYY-MM-DD',
    value: 'yyyy-mm-dd',
  },
  ISO_SLASH: {
    label: 'YYYY/MM/DD',
    value: 'yyyy/mm/dd',
  },
  ISO_DOT: {
    label: 'YYYY.MM.DD',
    value: 'yyyy.mm.dd',
  },
  EURO_DASH: {
    label: 'DD-MM-YYYY',
    value: 'dd-mm-yyyy',
  },
  EURO_SLASH: {
    label: 'DD/MM/YYYY',
    value: 'dd/mm/yyyy',
  },
  EURO_DOT: {
    label: 'DD.MM.YYYY',
    value: 'dd.mm.yyyy',
  },
};

/**
 * Defines the available options for session lifetime duration in hours.
 * This constant array provides a list of predefined session duration choices
 * that can be used in user interface dropdowns or configuration settings.
 *
 * @type {Array<{label: string, value: number}>}
 * @property {string} label - A human-readable description of the duration.
 * @property {number} value - The corresponding number of hours for the session lifetime.
 *
 * @returns {Array<{label: string, value: number}>} An array of objects, each representing
 * a session lifetime option with a label and its corresponding value in hours.
 */
export const SESSION_LIFETIME_HOURS = [
  { label: '1 hour', value: '1' },
  { label: '2 hours', value: '2' },
  { label: '4 hours', value: '4' },
  { label: '6 hours', value: '6' },
  { label: '8 hours', value: '8' },
  { label: '10 hours', value: '10' },
  { label: '12 hours', value: '12' },
  { label: '24 hours', value: '24' },
];

/**
 * Defines the available options for password expiry in days.
 * This constant array provides a list of predefined password expiry durations
 * that can be used in user interface dropdowns or configuration settings.
 *
 * @type {Array<{label: string, value: number}>}
 * @property {string} label - A human-readable description of the duration in days.
 * @property {number} value - The corresponding number of days for password expiry.
 *
 * @returns {Array<{label: string, value: number}>} An array of objects, each representing
 * a password expiry option with a label and its corresponding value in days.
 */
export const PASSWORD_EXPIRY_DAYS = [
  { label: '30 day', value: '30' },
  { label: '45 days', value: '45' },
  { label: '60 days', value: '60' },
  { label: '75 days', value: '75' },
  { label: '90 days', value: '90' },
  { label: '120 days', value: '120' },
  { label: '180 days', value: '180' },
  { label: '365 days', value: '365' },
];

/**
 * Defines the available options for password reuse count.
 * This constant array provides a list of predefined password reuse count options
 * that can be used in user interface dropdowns or configuration settings.
 *
 * @type {Array<{label: string, value: number}>}
 * @property {string} label - A human-readable representation of the reuse count.
 * @property {number} value - The corresponding numeric value for the reuse count.
 *
 * @returns {Array<{label: string, value: number}>} An array of objects, each representing
 * a password reuse count option with a label and its corresponding numeric value.
 */
export const PASSWORD_REUSE_COUNT = [
  { label: '1', value: '1' },
  { label: '3', value: '3' },
  { label: '5', value: '5' },
  { label: '7', value: '7' },
  { label: '10', value: '10' },
];

/**
 * Defines the available options for maximum password attempt limits.
 * This constant array provides a list of predefined maximum password attempt options
 * that can be used in user interface dropdowns or configuration settings.
 *
 * @type {Array<{label: string, value: number}>}
 * @property {string} label - A human-readable representation of the attempt count.
 * @property {number} value - The corresponding numeric value for the maximum attempt count.
 *
 * @returns {Array<{label: string, value: number}>} An array of objects, each representing
 * a maximum password attempt option with a label and its corresponding numeric value.
 */
export const PASSWORD_MAXIMUM_ATTEMPTS = [
  { label: '3', value: '3' },
  { label: '4', value: '4' },
  { label: '5', value: '5' },
  { label: '6', value: '6' },
  { label: '7', value: '7' },
  { label: '8', value: '8' },
  { label: '9', value: '9' },
];

/**
 * Defines the available options for two-factor authentication session timeout in days.
 * This constant array provides a list of predefined two-factor session timeout durations
 * that can be used in user interface dropdowns or configuration settings.
 *
 * @type {Array<{label: string, value: number}>}
 * @property {string} label - A human-readable description of the duration in days.
 * @property {number} value - The corresponding number of days for the two-factor session timeout.
 *
 * @returns {Array<{label: string, value: number}>} An array of objects, each representing
 * a two-factor session timeout option with a label and its corresponding value in days.
 */
export const TWO_FACTOR_SESSION_TIMEOUT_DAYS = [
  { label: '1 day', value: '1' },
  { label: '3 days', value: '3' },
  { label: '7 days', value: '7' },
  { label: '14 days', value: '14' },
  { label: '30 days', value: '30' },
  { label: '45 days', value: '45' },
  { label: '60 days', value: '60' },
  { label: '90 days', value: '90' },
  { label: '180 days', value: '180' },
];

import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { AccessControlService } from '#src/modules/setting/services/index.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

const {
  CACHE_SECOND: { SHORT },
  MODULE_NAMES: { ACCESS_CONTROL },
  MODULE_METHODS: { CREATE, INDEX, VIEW, UPDATE, UPDATE_STATUS },
} = CoreConstant;

const MODULE = ACCESS_CONTROL;

/**
 * Handles the index request for access control settings.
 * This function generates a cache key, fetches data from cache or service,
 * and processes the response using the handleServiceResponse utility.
 *
 * @async
 * @param {Object} request - The incoming request object.
 * @param {Object} reply - The reply object used to send the response.
 * @returns {Promise<Object>} A promise that resolves with the handled service response.
 */
export const index = async (request, reply) => {
  const cacheKey = generateCacheKey(`${MODULE}_${INDEX}`, request);
  const cachedServiceFn = () =>
    fetchFromCache(
      request.server.redis,
      cacheKey,
      () => AccessControlService.index(request),
      SHORT,
    );

  return handleServiceResponse({
    request,
    reply,
    serviceFn: cachedServiceFn,
    module: MODULE,
    method: INDEX,
  });
};

/**
 * Handles the view request for a specific access control setting.
 * This function generates a cache key, fetches data from cache or service,
 * and processes the response using the handleServiceResponse utility.
 *
 * @async
 * @param {Object} request - The incoming request object containing details of the access control setting to view.
 * @param {Object} reply - The reply object used to send the response.
 * @returns {Promise<Object>} A promise that resolves with the handled service response containing the viewed access control setting.
 */
export const view = async (request, reply) => {
  const cacheKey = generateCacheKey(`${MODULE}_${VIEW}`, request);
  const cachedServiceFn = () =>
    fetchFromCache(request.server.redis, cacheKey, () => AccessControlService.view(request), SHORT);

  return handleServiceResponse({
    request,
    reply,
    serviceFn: cachedServiceFn,
    module: MODULE,
    method: VIEW,
  });
};

/**
 * Handles the creation of a new access control setting.
 * This function processes the create request and uses the handleServiceResponse utility
 * to manage the service call and response handling.
 *
 * @async
 * @param {Object} request - The incoming request object containing details for creating a new access control setting.
 * @param {Object} reply - The reply object used to send the response.
 * @returns {Promise<Object>} A promise that resolves with the handled service response containing the newly created access control setting.
 */
export const create = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: AccessControlService.create,
    module: MODULE,
    method: CREATE,
  });

/**
 * Handles the update request for an existing access control setting.
 * This function processes the update request and uses the handleServiceResponse utility
 * to manage the service call and response handling.
 *
 * @async
 * @param {Object} request - The incoming request object containing details for updating an existing access control setting.
 * @param {Object} reply - The reply object used to send the response.
 * @returns {Promise<Object>} A promise that resolves with the handled service response containing the updated access control setting.
 */
export const update = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: AccessControlService.update,
    module: MODULE,
    method: UPDATE,
  });

/**
 * Handles the status update request for an existing access control setting.
 * This function processes the status update request and uses the handleServiceResponse utility
 * to manage the service call and response handling.
 *
 * @async
 * @param {Object} request - The incoming request object containing details for updating the status of an existing access control setting.
 * @param {Object} reply - The reply object used to send the response.
 * @returns {Promise<Object>} A promise that resolves with the handled service response containing the access control setting with updated status.
 */
export const updateStatus = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: AccessControlService.updateStatus,
    module: MODULE,
    method: UPDATE_STATUS,
  });

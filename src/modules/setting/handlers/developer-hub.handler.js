import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { DeveloperHubService } from '#src/modules/setting/services/index.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

const {
  CACHE_SECOND: { SHORT },
  MODULE_NAMES: { DEVELOPER_HUB },
  MODULE_METHODS: {
    CREATE,
    INDEX,
    OPTION,
    VIEW,
    UPDATE_BASIC_INFORMATION,
    UPDATE_ACCESS_CONTROL,
    UPDATE_PERMISSION,
    UPDATE_STATUS,
  },
} = CoreConstant;

const MODULE = DEVELOPER_HUB;

/**
 * Handles the index request for the Developer Hub module.
 * This function retrieves the index data from cache if available, or fetches it from the service and caches the result.
 *
 * @async
 * @param {Object} request - The incoming request object.
 * @param {Object} request.server - The server object containing Redis instance.
 * @param {Object} reply - The reply object used to send the response.
 * @returns {Promise<Object>} A promise that resolves with the handled service response.
 */
export const index = async (request, reply) => {
  const cacheKey = generateCacheKey(`${MODULE}_${INDEX}`, request);
  const cachedServiceFn = () =>
    fetchFromCache(request.server.redis, cacheKey, () => DeveloperHubService.index(request), SHORT);

  return handleServiceResponse({
    request,
    reply,
    serviceFn: cachedServiceFn,
    module: MODULE,
    method: INDEX,
  });
};

/**
 * Handles the view request for the Developer Hub module.
 * This function retrieves the view data from cache if available, or fetches it from the service and caches the result.
 *
 * @async
 * @param {Object} request - The incoming request object.
 * @param {Object} request.server - The server object containing Redis instance.
 * @param {Object} reply - The reply object used to send the response.
 * @returns {Promise<Object>} A promise that resolves with the handled service response.
 */
export const view = async (request, reply) => {
  const cacheKey = generateCacheKey(`${MODULE}_${VIEW}`, request);
  const cachedServiceFn = () =>
    fetchFromCache(request.server.redis, cacheKey, () => DeveloperHubService.view(request), SHORT);

  return handleServiceResponse({
    request,
    reply,
    serviceFn: cachedServiceFn,
    module: MODULE,
    method: VIEW,
  });
};

/**
 * Handles the create request for the Developer Hub module.
 * This function processes the creation of a new developer hub entry by calling the appropriate service function.
 *
 * @async
 * @param {Object} request - The incoming request object containing the data for creating a new developer hub entry.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} A promise that resolves with the handled service response, typically containing the newly created developer hub entry or an error message.
 */
export const create = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: DeveloperHubService.create,
    module: MODULE,
    method: CREATE,
  });

/**
 * Handles the update of basic information for a Developer Hub entry.
 * This function processes the request to update the basic information by calling the appropriate service function.
 *
 * @async
 * @param {Object} request - The incoming request object containing the data for updating the basic information.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} A promise that resolves with the handled service response, typically containing the updated Developer Hub entry or an error message.
 */
export const updateBasicInformation = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: DeveloperHubService.updateBasicInformation,
    module: MODULE,
    method: UPDATE_BASIC_INFORMATION,
  });

/**
 * Handles the update of permissions for a Developer Hub entry.
 * This function processes the request to update permissions by calling the appropriate service function.
 *
 * @async
 * @param {Object} request - The incoming request object containing the data for updating permissions.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} A promise that resolves with the handled service response, typically containing the updated Developer Hub permissions or an error message.
 */
export const updatePermissions = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: DeveloperHubService.updatePermissions,
    module: MODULE,
    method: UPDATE_PERMISSION,
  });

/**
 * Handles the update of access controls for a Developer Hub entry.
 * This function processes the request to update access controls by calling the appropriate service function.
 *
 * @async
 * @param {Object} request - The incoming request object containing the data for updating access controls.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} A promise that resolves with the handled service response, typically containing the updated Developer Hub access controls or an error message.
 */
export const updateAccessControls = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: DeveloperHubService.updateAccessControls,
    module: MODULE,
    method: UPDATE_ACCESS_CONTROL,
  });

/**
 * Handles the update of status for a Developer Hub entry.
 * This function processes the request to update the status by calling the appropriate service function.
 *
 * @async
 * @param {Object} request - The incoming request object containing the data for updating the status.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} A promise that resolves with the handled service response, typically containing the updated Developer Hub status or an error message.
 */
export const updateStatus = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: DeveloperHubService.updateBasicInformation,
    module: MODULE,
    method: UPDATE_STATUS,
  });

/**
 * Handles the options request for the Developer Hub module.
 *
 * @async
 * @param {Object} request - The incoming request object.
 * @param {Object} reply - The reply object used to send the response.
 * @returns {Promise<Object>} A promise that resolves with the handled service response containing module permissions.
 */
export const options = async (request, reply) => {
  // SC-01
  // skeleton code: get from modules table (type = webhook and visibility = private) and cache it from dropdown
  const modulePermissions = [
    {
      id: 'a7f95e10-0f85-11f0-af17-f33274f90ad1',
      name: 'Member',
      availablePermissions: [
        'canRead',
        'canCreate',
        'canEdit',
        'canManage',
        'canImport',
        'canExport',
      ],
    },
    {
      id: 'a7f95e10-0f85-11f0-af17-f33274f90ad2',
      name: 'Transaction',
      availablePermissions: ['canRead', 'canCreate', 'canEdit', 'canManage', 'canExport'],
    },
  ];

  return handleServiceResponse({
    request,
    reply,
    serviceFn: () => {
      return { modulePermissions };
    },
    module: MODULE,
    method: OPTION,
  });
};

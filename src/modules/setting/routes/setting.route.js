import { Setting<PERSON>and<PERSON> } from '#src/modules/setting/handlers/index.js';
import { SettingSchema } from '#src/modules/setting/schemas/index.js';

/**
 * Setting API routes.
 * @param {import('fastify').FastifyInstance} fastify - The Fastify instance.
 * @param {Object} opts - Optional route options.
 */
const SettingRoute = async (fastify, opts) => {
  const PREFIX = '/:category';

  // Define the common access configuration for all routes
  const commonAccessConfig = {
    user: true,
    member: false,
    webhook: true,
    public: false,
    ipWhitelist: ['127.0.0.1'],
  };

  // List all settings
  fastify.get(PREFIX, {
    schema: SettingSchema.index,
    config: { name: 'setting.view', access: commonAccessConfig },
    handler: SettingHandler.index,
  });

  // Update a personal entry
  fastify.put('/personal', {
    schema: SettingSchema.updatePersonal,
    config: { name: 'setting.updatePersonal', access: commonAccessConfig },
    handler: SettingHandler.updatePersonal,
  });

  // Update a safety entry
  fastify.put('/safety', {
    schema: SettingSchema.updateSafety,
    config: { name: 'setting.updateSafety', access: commonAccessConfig },
    handler: SettingHandler.updateSafety,
  });

  // Update a themes entry
  fastify.put('/themes', {
    schema: SettingSchema.updateThemes,
    config: { name: 'setting.updateThemes', access: commonAccessConfig },
    handler: SettingHandler.updateThemes,
  });

  // Get additional info options for setting category (e.g., timezone, language)
  fastify.get(`${PREFIX}/options`, {
    schema: SettingSchema.options,
    config: { name: 'setting.options', access: commonAccessConfig },
    handler: SettingHandler.options,
  });
};

export default SettingRoute;

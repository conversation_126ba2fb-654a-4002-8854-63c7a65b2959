import { Op, literal } from 'sequelize';
import ipaddr from 'ipaddr.js';

import { applyOffsetPagination } from '#src/utils/pagination.util.js';
import { buildWhereFromFilters } from '#src/utils/query.util.js';

/**
 * Retrieves all IP access control entries based on the provided query parameters.
 * @param {Object} fastify - The Fastify instance containing the PostgreSQL models.
 * @param {Object} query - The query parameters for filtering and pagination.
 * @returns {Promise<Object>} A promise that resolves to an object containing the paginated IP access control entries and metadata.
 */
export const findAll = async (fastify, query) => {
  const { IpAccessControl, Remark } = fastify.psql;

  const includes = [
    {
      model: Remark,
      association: 'activeRemark',
      required: false,
    },
  ];
  const { where: whereFilter, include: includeFilter } = buildWhereFromFilters(
    query,
    IpAccessControl,
    includes,
  );

  return await applyOffsetPagination(fastify, IpAccessControl, query, whereFilter, includeFilter);
};

/**
 * Finds an IP access control entry by its ID.
 * @param {Object} fastify - The Fastify instance.
 * @param {number|string} id - The ID of the IP access control entry.
 * @returns {Promise<Object|null>} A promise that resolves to the IP access control entry or null if not found.
 */
export const findById = async (fastify, id) =>
  await fastify.psql.IpAccessControl.findByPk(id, {
    include: [
      {
        model: fastify.psql.Remark,
        as: 'activeRemark',
        required: false,
      },
    ],
  });

/**
 * Checks if the given IP or range overlaps with existing entries using a raw SQL query
 * @param {Object} server - The server object containing the PostgreSQL models
 * @param {string} parentId - The parent ID to check against
 * @param {string} ipOrRange - The IP address or range to check
 * @returns {Promise<boolean>} True if there's an overlap, false otherwise
 */
export const checkOverlap = async (server, parentId, ipOrRange, id = null) => {
  const parsedIp = ipaddr.parse(ipOrRange.split('/')[0]);
  const kind = parsedIp.kind();
  const familyFilter = kind === 'ipv4' ? '0.0.0.0/0' : '::/0';

  const where = {
    parent_id: parentId,
    status: 'active',
    [Op.and]: [
      literal(`ip_address <<= '${familyFilter}'::inet`),
      literal(`ip_address && '${ipOrRange}'::inet`),
    ],
  };

  if (id !== null) {
    where.id = { [Op.ne]: id };
  }

  const result = await server.psql.IpAccessControl.findOne({ where });
  return !!result;
};

/**
 * Creates a new IP access control entry.
 * @param {Object} server - The server object containing the PostgreSQL models.
 * @param {Object} data - The data for creating the new IP access control entry.
 * @param {Object} [options={}] - Additional options for the create operation.
 * @returns {Promise<Object>} A promise that resolves to the newly created IP access control entry.
 */
export const create = async (server, data, options = {}) =>
  await server.psql.IpAccessControl.create(data, options);

/**
 * Updates an existing IP access control entry.
 * @param {Object} modelData - The IP access control entry model instance to update.
 * @param {Object} updateData - The data to update the entry with.
 * @returns {Promise<void>} A promise that resolves when the update is complete.
 */
export const update = async (modelData, updateData, options = {}) =>
  await modelData.update(updateData, options);

/**
 * Removes all IP access control entries associated with a specific parent ID.
 * @param {Object} server - The server object containing the PostgreSQL models.
 * @param {string|number} parentId - The ID of the parent entity whose associated IP access control entries should be removed.
 * @param {Object} [options={}] - Additional options to be passed to the destroy method.
 * @returns {Promise<number>} A promise that resolves to the number of destroyed rows.
 */
export const removeAll = async (server, parentId, options = {}) => {
  return await server.psql.IpAccessControl.destroy({
    where: {
      parentId,
    },
    individualHooks: true,
    ...options,
  });
};

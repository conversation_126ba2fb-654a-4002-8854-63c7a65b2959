import { CoreSchema } from '#src/modules/core/schemas/index.js';
import { SettingConstant } from '#src/modules/setting/constants/index.js';

const { COMMON_PROPERTIES, ERROR_RESPONSE, UPDATE_RESPONSE } = CoreSchema;
const {
  ALERT_POSITIONS,
  APPEARANCE_MODES,
  DATE_FORMATS,
  RECORDS_PER_PAGE,
  SETTING_CATEGORIES,
  TIME_FORMATS,
  TOAST_POSITIONS,
  SESSION_LIFETIME_HOURS,
  PASSWORD_EXPIRY_DAYS,
  PASSWORD_REUSE_COUNT,
  PASSWORD_MAXIMUM_ATTEMPTS,
  TWO_FACTOR_SESSION_TIMEOUT_DAYS,
} = SettingConstant;

const TAGS = ['BO / Settings / Personal, Safety, Themes'];

/**
 * Common request param schema for category-based routes.
 */
const REQ_PARAM_CATEGORY = {
  type: 'object',
  properties: {
    category: {
      type: 'string',
      enum: Object.values(SETTING_CATEGORIES),
      description: 'Category of the setting to operate on',
    },
  },
  required: ['category'],
};

/**
 * List endpoint schema for settings.
 */
export const index = {
  tags: TAGS,
  summary: 'Get a setting by category',
  params: REQ_PARAM_CATEGORY,
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'array',
          items: {
            id: { type: 'string', format: 'uuid' },
            category: { type: 'string' },
            field: { type: 'string' },
            accessLevels: {
              type: 'array',
              items: { type: 'string' },
            },
            customSetting: {
              type: 'array',
              items: {
                ...COMMON_PROPERTIES,
                value: { type: 'string' },
                version: { type: 'number' },
              },
            },
          },
          additionalProperties: true,
        },
      },
    },
    ...ERROR_RESPONSE,
  },
};

/**
 * Update endpoint schema for themes settings.
 */
export const updateThemes = {
  tags: TAGS,
  summary: 'Update a themes setting',
  body: {
    type: 'object',
    properties: {
      version: { type: 'number', default: 1 },
      toastMessagePosition: {
        type: 'string',
        enum: Object.values(TOAST_POSITIONS).map((position) => position.value),
      },
      alertBarPosition: {
        type: 'string',
        enum: Object.values(ALERT_POSITIONS).map((position) => position.value),
      },
    },
    required: ['version', 'toastMessagePosition', 'alertBarPosition'],
    additionalProperties: false,
  },
  response: UPDATE_RESPONSE,
};

/**
 * Update endpoint schema for personal settings.
 */
export const updatePersonal = {
  tags: TAGS,
  summary: 'Update a personal setting',
  body: {
    type: 'object',
    properties: {
      version: { type: 'number', default: 1 },
      appearance: {
        type: 'string',
        enum: Object.values(APPEARANCE_MODES).map((mode) => mode.value),
      },
      recordPerPage: {
        type: 'string',
        enum: RECORDS_PER_PAGE.map((option) => option.value),
      },
      dateFormat: {
        type: 'string',
        enum: Object.values(DATE_FORMATS).map((format) => format.value),
      },
      timeFormat: {
        type: 'string',
        enum: Object.values(TIME_FORMATS).map((format) => format.value),
      },
      defaultLanguage: {
        type: 'string',
        format: 'uuid',
      },
      defaultTimezone: {
        type: 'string',
        format: 'uuid',
      },
    },
    required: [
      'version',
      'appearance',
      'recordPerPage',
      'dateFormat',
      'timeFormat',
      'defaultLanguage',
      'defaultTimezone',
    ],
    additionalProperties: false,
  },
  response: UPDATE_RESPONSE,
};

/**
 * Update endpoint schema for safety settings.
 */
export const updateSafety = {
  tags: TAGS,
  summary: 'Update a safety setting',
  body: {
    type: 'object',
    properties: {
      version: { type: 'number', default: 1 },
      sessionLifetimeHours: {
        type: 'string',
        enum: SESSION_LIFETIME_HOURS.map((option) => option.value),
      },
      passwordExpiryDays: {
        type: 'string',
        enum: PASSWORD_EXPIRY_DAYS.map((option) => option.value),
      },
      passwordReuseCount: {
        type: 'string',
        enum: PASSWORD_REUSE_COUNT.map((option) => option.value),
      },
      passwordMaximumAttempts: {
        type: 'string',
        enum: PASSWORD_MAXIMUM_ATTEMPTS.map((option) => option.value),
      },
      twoFactorSessionTimeoutDays: {
        type: 'string',
        enum: TWO_FACTOR_SESSION_TIMEOUT_DAYS.map((option) => option.value),
      },
    },
    required: [
      'version',
      'sessionLifetimeHours',
      'passwordExpiryDays',
      'passwordReuseCount',
      'passwordMaximumAttempts',
      'twoFactorSessionTimeoutDays',
    ],
    additionalProperties: false,
  },
  response: UPDATE_RESPONSE,
};

/**
 * Get options endpoint for settings.
 */
export const options = {
  tags: TAGS,
  summary: 'Get available setting options',
  params: REQ_PARAM_CATEGORY,
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string' },
        data: {
          type: 'object',
          additionalProperties: true,
        },
      },
    },
    ...ERROR_RESPONSE,
  },
};

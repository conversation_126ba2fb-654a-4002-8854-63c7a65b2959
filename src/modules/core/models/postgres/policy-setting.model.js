import { DataTypes, Model, Sequelize } from 'sequelize';
import { auditableMixin } from '#src/mixins/index.js';

export default function (fastify, instance) {
  class PolicySetting extends Model {}

  PolicySetting.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      parentId: {
        type: DataTypes.UUID,
        allowNull: false,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        comment: 'Reference to the parents (e.g., modules, department_module_policies)',
      },
      canView: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: 'The view policy',
      },
      canCreate: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: 'The create policy',
      },
      canEdit: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: 'The edit policy',
      },
      canImport: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: 'The import policy',
      },
      canExport: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: 'The export policy',
      },
      canManage: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: 'The manage policy',
      },
      canMasking: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: 'The masking policy',
      },
      canOverwrite: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: 'The overwrite policy',
      },
      canVerify: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: 'The verify policy',
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      modelName: 'PolicySetting',
      tableName: 'policy_settings',
      underscored: true,
      timestamps: true,
      sequelize: fastify.psql.connection,
      indexes: [
        {
          unique: true,
          fields: ['parent_id'],
        },
      ],
      scopes: {},
    },
  );

  auditableMixin.applyAuditFields(PolicySetting);

  return PolicySetting;
}

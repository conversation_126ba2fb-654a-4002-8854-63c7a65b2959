import { DataTypes, Model, Sequelize } from 'sequelize';
import { auditableMixin } from '#src/mixins/index.js';

export default function (fastify, instance) {
  class DepartmentModule extends Model {
    static associate(models) {
      this.belongsTo(models.Department, {
        foreignKey: 'departmentId',
        as: 'department',
      });

      this.belongsTo(models.Module, {
        foreignKey: 'moduleId',
        as: 'module',
      });

      this.hasOne(models.PolicySetting, {
        foreignKey: 'parentId',
        sourceKey: 'id',
        as: 'policySetting',
      });
    }
  }

  DepartmentModule.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      departmentId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      moduleId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      modelName: 'DepartmentModule',
      tableName: 'department_modules',
      underscored: true,
      timestamps: true,
      sequelize: fastify.psql.connection,
      indexes: [
        {
          unique: true,
          fields: ['department_id', 'module_id'],
        },
      ],
      scopes: {},
    },
  );

  auditableMixin.applyAuditFields(DepartmentModule);

  return DepartmentModule;
}

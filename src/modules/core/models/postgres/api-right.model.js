import { DataTypes, Model, Sequelize } from 'sequelize';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import { DeveloperHubError } from '#src/modules/setting/errors/index.js';
import { auditableMixin } from '#src/mixins/index.js';

const { PERMISSION_FIELDS } = CoreConstant;

function validatePermissions(instance) {
  const hasAny = PERMISSION_FIELDS.some((f) => instance[f] === true);
  if (!hasAny) {
    throw DeveloperHubError.invalidData('At least one permission must be granted.');
  }
}

export default function (fastify, instance) {
  const MODULE_NAME_MAP = {
    'a7f95e10-0f85-11f0-af17-f33274f90ad1': 'Member',
    'a7f95e10-0f85-11f0-af17-f33274f90ad2': 'Transaction',
    // Add more module IDs as needed
  };

  class ApiRight extends Model {
    static associate(models) {
      ApiRight.belongsTo(models.DeveloperHub, {
        foreignKey: 'parentId',
        as: 'developerHub',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      });
    }
  }

  ApiRight.init(
    {
      id: {
        type: DataTypes.UUID,
        defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
        primaryKey: true,
      },
      parentId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      moduleId: {
        type: DataTypes.UUID,
        allowNull: false,
      },
      moduleName: {
        type: DataTypes.VIRTUAL,
        get() {
          return MODULE_NAME_MAP[this.moduleId] || 'Unknown';
        },
      },
      permissions: {
        type: DataTypes.VIRTUAL,
        get() {
          return PERMISSION_FIELDS.filter((field) => this[field] === true).map((field) => field);
        },
      },
      canRead: {
        type: DataTypes.BOOLEAN(),
        defaultValue: false,
      },
      canCreate: {
        type: DataTypes.BOOLEAN(),
        defaultValue: false,
      },
      canEdit: {
        type: DataTypes.BOOLEAN(),
        defaultValue: false,
      },
      canManage: {
        type: DataTypes.BOOLEAN(),
        defaultValue: false,
      },
      canImport: {
        type: DataTypes.BOOLEAN(),
        defaultValue: false,
      },
      canExport: {
        type: DataTypes.BOOLEAN(),
        defaultValue: false,
      },
      createdBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.UUID,
        allowNull: true,
      },
    },
    {
      modelName: 'ApiRight',
      tableName: 'api_rights',
      underscored: true,
      timestamps: true,
      sequelize: fastify.psql.connection,
      indexes: [
        {
          unique: true,
          fields: ['parent_id', 'module_id'],
        },
      ],
      hooks: {
        beforeCreate: async (instance, options) => {
          validatePermissions(instance);
        },
        beforeUpdate: async (instance, options) => {
          validatePermissions(instance);
        },
      },
    },
  );

  auditableMixin.applyAuditFields(ApiRight);

  return ApiRight;
}

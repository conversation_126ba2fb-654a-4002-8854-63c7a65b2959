import mongoose from 'mongoose';

const getAuditTrailModel = (fastify, date) => {
  const collectionName = getCollectionName(date);

  if (fastify.mongo.connection.models[collectionName]) {
    return fastify.mongo.connection.models[collectionName];
  }

  const mongo = new mongoose.Schema(
    {
      timestamp: { type: Date, required: true },
      entityAccessId: { type: String, required: true },
      hierarchyLevel: { type: String, required: true },
      module: { type: String, required: true },
      event: { type: String, required: true },
      action: {
        translationKey: { type: String, required: true },
        translationParams: { type: Object, required: true },
      },
      actor: {
        type: { type: String, required: true },
        id: { type: String, required: true },
        username: { type: String, required: true },
        role: { type: String },
        department: { type: String },
        organization: {
          id: { type: String, required: true },
          code: { type: String, required: true },
          prefix: { type: String, required: true },
          name: { type: String, required: true },
        },
        merchant: {
          id: { type: String, required: true },
          code: { type: String, required: true },
          prefix: { type: String, required: true },
          name: { type: String, required: true },
        },
      },
      target: {
        referenceId: { type: String },
        referenceDetails: { type: Object },
        model: { type: String },
        changes: {
          beforeState: { type: Object },
          afterState: { type: Object },
        },
      },
      details: {
        request: {
          requestId: { type: String, required: true },
          statusCode: { type: Number, required: true },
          path: { type: String, required: true },
          method: { type: String, required: true },
          parameters: { type: Object },
          payload: { type: Object },
        },
        error: { type: Object },
        metrics: { type: Object },
      },
      context: {
        ip: { type: String },
        location: { type: String },
        userAgent: { type: String },
        device: { type: String },
        fingerprintId: { type: String },
        host: { type: String },
        origin: { type: String },
      },
      status: { type: String, default: null },
      description: {
        translationKey: { type: String, default: null },
        translationParams: { type: Object },
      },
    },
    { timestamps: false, collection: collectionName },
  );
  return fastify.mongo.connection.model(collectionName, mongo);
};

export default getAuditTrailModel;

export const getCollectionName = (date) => {
  if (!date) {
    date = new Date();
  }
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  return `audit_trails_${year}_${month}`;
};

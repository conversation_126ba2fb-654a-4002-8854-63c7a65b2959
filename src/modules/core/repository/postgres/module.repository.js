import { Op } from 'sequelize';

/**
 * Retrieves a module by its ID from the database.
 *
 * @param {Object} server - The server object containing the Sequelize models.
 * @param {number|string} id - The unique identifier of the module to retrieve.
 * @param {Object} [options={}] - Additional options to pass to the Sequelize findByPk method.
 * @returns {Promise<Object|null>} A promise that resolves to the found module object, or null if not found.
 */
export const findById = async (server, id, options = {}) => {
  return await server.psql.Module.findByPk(id, options);
};

/**
 * Retrieves all top-level modules with their associated policy settings and child modules.
 *
 * @param {Object} server - The server object containing the Sequelize models.
 * @param {Array} hierarchies - An array of hierarchy values to filter the modules.
 * @returns {Promise<Array>} A promise that resolves to an array of Module instances,
 *                           including their associated PolicySettings and child Modules,
 *                           ordered by navigationPosition.
 */
export const findModulePolicies = async (server, hierarchies) => {
  const { Module, PolicySetting } = server.psql;

  return await Module.findAll({
    include: [
      {
        model: PolicySetting,
        as: 'policySetting',
        required: false,
      },
      {
        model: Module,
        as: 'children',
        include: [
          {
            model: PolicySetting,
            as: 'policySetting',
            required: false,
          },
          {
            model: Module,
            as: 'children',
            include: [
              {
                model: PolicySetting,
                as: 'policySetting',
                required: false,
              },
            ],
          },
        ],
      },
    ],
    order: [
      ['navigationPosition', 'ASC'],
      [{ model: Module, as: 'children' }, 'navigationPosition', 'ASC'],
      [
        { model: Module, as: 'children' },
        { model: Module, as: 'children' },
        'navigationPosition',
        'ASC',
      ],
    ],
    where: {
      parentId: null, // Get only top-level modules
      hierarchy: {
        [Op.in]: hierarchies,
      },
    },
  });
};

/**
 * Retrieves all modules from the database.
 *
 * @param {Object} server - The server object containing the Sequelize models.
 * @param {Object} [options={}] - Additional options to pass to the Sequelize findAll method.
 * @returns {Promise<Array>} A promise that resolves to an array of all module objects.
 */
export const findAll = async (server, options = {}) => {
  return await server.psql.Module.findAll({
    ...options,
    include: [
      {
        model: server.psql.PolicySetting,
        as: 'policySetting',
        required: false,
      },
    ],
    order: [['navigationPosition', 'ASC']],
  });
};

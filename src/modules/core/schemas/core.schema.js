import { BulkJobConstant } from '#src/modules/bulk-job/constants/index.js';

/**
 * Common entity properties used across many schemas.
 * @constant
 * @type {object}
 */
export const COMMON_PROPERTIES = {
  id: { type: 'string', format: 'uuid' },
  createdAt: { type: 'string', format: 'date-time' },
  createdBy: { type: 'string', format: 'uuid' },
  updatedAt: { type: 'string', format: 'date-time' },
  updatedBy: { type: 'string', format: 'uuid' },
};

/**
 * Common MongoDB properties used across many schemas.
 *
 * @constant
 * @type {object}
 */
export const COMMON_MONGO_PROPERTIES = {
  _id: { type: 'string' },
  timestamp: { type: 'string', format: 'date-time' },
};

/**
 * Defines the common properties used in export responses across various schemas.
 * This object is a combination of {@link COMMON_PROPERTIES} and additional properties specific to export responses.
 *
 * @constant
 * @type {object}
 */
export const COMMON_EXPORT_RES_PROPERTIES = {
  ...COMMON_PROPERTIES,
  entity_id: { type: 'string' },
  type: { type: 'string', enum: Object.values(BulkJobConstant.BULK_JOB_TYPES) },
  title: { type: 'string' },
  description: { type: 'string' },
  parameters: { type: 'object' },
  model: { type: 'string' },
  file_url: { type: 'string' },
  error_message: { type: 'string' },
  status: { type: 'string', enum: Object.values(BulkJobConstant.BULK_JOB_STATUSES) },
  started_at: { type: 'string', format: 'date-time' },
  completed_at: { type: 'string', format: 'date-time' },
};

/**
 * Standard error response reference for OpenAPI/Swagger.
 * @constant
 * @type {object}
 */
export const ERROR_RESPONSE = {
  '4xx': { $ref: 'ErrorResponse' },
  '5xx': { $ref: 'ErrorResponse' },
};

/**
 * Standard pagination metadata returned in paginated responses.
 * @constant
 * @type {object}
 */
export const OFFSET_PAGINATION_META_PROPERTIES = {
  totalCount: { type: 'integer' },
  totalPages: { type: 'integer' },
  currentPage: { type: 'integer' },
  limit: { type: 'integer' },
};

/**
 * Standard pagination query parameters for offset-based pagination.
 * @constant
 * @type {object}
 */
export const OFFSET_PAGINATION_QUERY_PARAMS = {
  page: { type: 'string', description: 'Current page number', default: 1 },
  limit: { type: 'string', description: 'Items per page', default: 25 },
  sortBy: {
    oneOf: [{ type: 'string' }, { type: 'array' }],
    description: 'Sorting field in the format (field:order) (e.g., name:asc)',
  },
};

/**
 * Standard cursor pagination metadata returned in responses.
 * @constant
 * @type {object}
 */
export const CURSOR_PAGINATION_META_PROPERTIES = {
  nextCursor: { type: 'string' },
  limit: { type: 'integer' },
};

/**
 * Standard pagination query parameters for cursor-based pagination.
 *
 * @constant
 * @type {object}
 */
export const CURSOR_PAGINATION_QUERY_PARAMS = {
  limit: { type: 'string', description: 'Items per page', default: '50' },
  sortBy: {
    oneOf: [{ type: 'string' }, { type: 'array' }],
    default: 'timestamp:desc',
    description: 'Sorting field in the format (field:order) (e.g., name:asc)',
  },
  cursor: {
    type: 'string',
    description: 'Cursor for pagination',
  },
};

/**
 * Request parameter validation for UUID route params.
 * @constant
 * @type {object}
 */
export const REQ_PARAM_UUID = {
  type: 'object',
  properties: {
    id: { type: 'string', format: 'uuid' },
  },
  required: ['id'],
};

/**
 * Request parameter validation for MongoDB ObjectId route params.
 * @constant
 * @type {object}
 */
export const REQ_PARAM_OBJECT_ID = {
  type: 'object',
  properties: {
    id: { type: 'string', pattern: '^[a-fA-F0-9]{24}$' },
  },
  required: ['id'],
};

/**
 * Standard update (PATCH/PUT) API response schema.
 * @constant
 * @type {object}
 */
export const UPDATE_RESPONSE = {
  200: {
    description: 'Success response',
    type: 'object',
    properties: {
      message: { type: 'string' },
    },
  },
  ...ERROR_RESPONSE,
};

/**
 * Standard create (POST) API response schema.
 * @function
 * @param {object} [customProperties={}] - Custom data properties to include.
 * @returns {object} Create response schema object.
 */
export const CREATE_RESPONSE = (customProperties = {}) => ({
  201: {
    description: 'Success response',
    type: 'object',
    properties: {
      message: { type: 'string' },
      data: customProperties,
    },
  },
  ...ERROR_RESPONSE,
});

/**
 * Standard remove (DELETE) API response schema.
 * @function
 * @param {object} [customProperties={}] - Custom data properties to include.
 * @returns {object} Delete response schema object.
 */
export const REMOVE_RESPONSE = {
  200: {
    description: 'Success response',
    type: 'object',
    properties: {
      message: { type: 'string' },
    },
  },
  ...ERROR_RESPONSE,
};

/**
 * Standard view (GET) API response schema.
 * @function
 * @param {object} [customProperties={}] - Custom data properties to include.
 * @returns {object} View response schema object.
 */
export const VIEW_RESPONSE = (customProperties = {}) => ({
  200: {
    description: 'Success response',
    type: 'object',
    properties: {
      message: { type: 'string' },
      data: customProperties,
    },
  },
  ...ERROR_RESPONSE,
});

/**
 * Creates a pagination metadata schema with optional additional properties.
 * @function
 * @param {object} [additionalProperties={}] - Additional properties to include.
 * @returns {object} Combined pagination metadata schema.
 */
export const createOffsetPaginationResponseSchema = (additionalProperties = {}) => ({
  type: 'object',
  properties: {
    pagination: {
      type: 'object',
      properties: {
        ...OFFSET_PAGINATION_META_PROPERTIES,
      },
    },
    ...additionalProperties,
  },
});

/**
 * Creates a cursor pagination metadata schema with optional additional properties.
 * @function
 * @param {object} [additionalProperties={}] - Additional properties to include.
 * @returns {object} Combined pagination metadata schema.
 */
export const createCursorPaginationResponseSchema = (additionalProperties = {}) => ({
  type: 'object',
  properties: {
    pagination: {
      type: 'object',
      properties: {
        ...CURSOR_PAGINATION_META_PROPERTIES,
      },
    },
    ...additionalProperties,
  },
});

/**
 * Common headers properties used across many schemas.
 * @constant
 * @type {object}
 */
export const COMMON_HEADERS = {
  type: 'object',
  properties: {
    'Accept-Language': {
      type: 'string',
      description: 'Language of the response',
      enum: ['en', 'zh'],
      default: 'en',
    },
    'X-Forwarded-For': {
      type: 'string',
      oneOf: [{ format: 'ipv4' }, { format: 'ipv6' }],
      description: 'Simulated client IP address for testing (e.g. *************)',
    },
    // Add new header here
  },
};

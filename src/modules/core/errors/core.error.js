import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { createModuleErrors } from '#src/utils/error.util.js';

const CORE_ERROR_DEF = {
  unknownError: ['10001', 'Unknown error: %s', 500],
  notFound: ['10002', 'No %s were found', 404],
  unprocessable: ['10005', 'No changes detected', 422],
  versionConflict: ['10013', 'error.sentence.versionConflict', 409],
};

export const coreError = createModuleErrors(MODULE_NAMES.CORE, CORE_ERROR_DEF);

/* eslint-disable sonarjs/no-hardcoded-passwords */
export const ACCESS_LEVELS = {
  MERCHANT: 'merchant',
  ORGANIZATION: 'organization',
  ROOT: 'root',
};

export const ACCESS_LEVEL_KEYS = {
  user: 'user',
  member: 'member',
  webhook: 'webhook',
};

export const PERMISSION_FIELDS = [
  'canRead',
  'canCreate',
  'canEdit',
  'canManage',
  'canImport',
  'canExport',
];

export const CACHE_SECOND = {
  SHORT: 10, // For rapidly changing data or debounce-type caching
  MEDIUM: 30, // 30 second – suitable for moderately volatile data
  STANDARD: 60, // 1 minute – good default for most general cache
  LONG: 3600, // 1 hour – stable data that changes infrequently
  DAILY: 86400, // 24 hours – rarely changing reference data
  WEEKLY: 604800, // 7 days – archive-type or external lookup cache
  NEVER: 0, // Used when caching is disabled
};

export const COMMON_STATUSES = {
  ACTIVE: 'active',
  DELETED: 'deleted',
  INACTIVE: 'inactive',
};

export const EVENTS = {
  APP_CENTER: 'appCenter',
  AUDIT_TRAIL: 'auditTrail',
  AUTOMATION: 'automation',
  CREDIT_LIMIT_SETTINGS: 'creditLimitSettings',
  CURRENCY_SETTINGS: 'currencySettings',
  DATA_MASKING_SETTINGS: 'dataMaskingSettings',
  DEVELOPER_HUB: 'developerHub',
  GAME_PROVIDER: 'gameProvider',
  LANGUAGE_SETTINGS: 'languageSettings',
  LOCALISATION_SETTINGS: 'localisationSettings',
  LOGIN: 'login',
  LOGOUT: 'logout',
  MAINTENANCE: 'maintenance',
  MANAGE_MONITOR: 'manageMonitor',
  MEDIA: 'media',
  MEMBER: 'member',
  MEMBER_POINT: 'memberPoint',
  MEMBER_VIP: 'memberVIP',
  MERCHANT: 'merchant',
  MERCHANT_CREDIT: 'merchantCredit',
  ORGANIZATION: 'organization',
  OTP: 'OTP',
  PERSONAL_SETTINGS: 'personalSettings',
  REGION_SETTINGS: 'regionSettings',
  REGISTRATION_FORM: 'registrationForm',
  RESET_PASSWORD: 'resetPassword',
  RISK_GROUP: 'riskGroup',
  ROLE: 'role',
  SECURITY_ACCESS_CONTROL: 'securityAccessControl',
  SETTINGS: {
    PERSONAL: 'personalSettings',
    SAFETY: 'securitySafety',
    THEME: 'themeSettings',
  },
  SETTINGS_OPTIONS: {
    PERSONAL: 'personalSettingsOptions',
    SAFETY: 'securitySafetySettingsOptions',
    THEME: 'themeSettingsOptions',
  },
  TAGS: 'tags',
  TRANSACTIONS: 'transactions',
  TRIGGERED_EVENT_LOG: 'triggeredEventLog',
  TWO_FACTOR_AUTHENTICATION: 'twoFactorAuthentication',
  USER: 'user',
  USER_AUDIT_TRAIL: 'userAuditTrail',
  USER_EXTERNAL_INVITATION: 'userExternalInvitation',
  USER_HIERARCHY: 'userHierarchy',
  USER_LOGIN_LOG: 'userLoginLog',
  USER_SSO: 'userSSO',
  USER_SUB_ACCOUNT: 'userSubAccount',
};

export const EVENT_ACTIONS = {
  ARCHIVED: 'archived',
  ASSIGNED: 'assigned',
  CREATED: 'created',
  DELETED: 'deleted',
  DUPLICATED: 'duplicated',
  EDITED: 'edited',
  EXPORTED: 'exported',
  GENERATED_API_KEY: 'generatedAPIKey',
  IMPORTED: 'imported',
  INSTALLED: 'installed',
  IP_BLACKLISTED: 'IPBlacklisted',
  KILL_SWITCH: 'killSwitch',
  LOGIN: 'login',
  LOGOUT: 'logout',
  RAN_TEST: 'ranTest',
  RESET: 'reset',
  SEARCHED: 'searched',
  SETUP: 'setup',
  UNINSTALLED: 'uninstalled',
  UNKNOWN_ACTION: 'unknownAction',
  UPDATED: 'updated',
  UPDATED_RISK_SCORE: 'updatedRiskScore',
  UPDATED_STATUS: 'updatedStatus',
  VIEWED: 'viewed',
  VIEWED_DETAILS: 'viewedDetails',
  VIEWED_HISTORY: 'viewedHistory',
};

export const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  PATCH: 'PATCH',
  DELETE: 'DELETE',
};

export const MODULE_METHODS = {
  CREATE: 'create',
  DELETE: 'delete',
  EXPORT: 'export',
  INDEX: 'index',
  OPTION: 'option',
  UPDATE: 'update',
  UPDATE_ACCESS_CONTROL: 'updateAccessControls',
  UPDATE_BASIC_INFORMATION: 'updateBasicInformation',
  UPDATE_MODULE_POLICY: 'updateModulePolicy',
  UPDATE_PERMISSION: 'updatePermissions',
  UPDATE_PERSONAL: 'updatePersonal',
  UPDATE_SAFETY: 'updateSafety',
  UPDATE_STATUS: 'updateStatus',
  UPDATE_THEMES: 'updateThemes',
  VIEW: 'view',
};

export const MODULE_NAMES = {
  ACCESS_CONTROL: 'accessControls',
  AUDIT_TRAIL: 'auditTrails',
  BULK_JOB: 'bulkJobs',
  CORE: 'core',
  DEVELOPER_HUB: 'developerHubs',
  LOCALISATION: 'localisations',
  SETTING: 'settings',
  DEPARTMENT: 'departments',
  DEPARTMENT_TEMPLATE: 'departmentTemplates',
};

export const REDACT_FIELDS = {
  PASSWORD: 'password',
  CONFIRM_PASSWORD: 'confirmPassword',
};

export const REMARK_STATUSES = {
  ACTIVE: 'active',
  ARCHIVED: 'archived',
};

export const REMARK_TYPE = {
  AUDIT: 'audit',
  NOTE: 'note',
  SECURITY: 'security',
  SYSTEM: 'system',
  WARNING: 'warning',
};

export const REMARKABLE_TYPE = {
  IP_ACCESS_CONTROL: 'ip_access_control',
};

export const VIEW_ACTION = {
  SEARCHED: 'searched',
  VIEWED: 'viewed',
  VIEWED_DETAILS: 'viewedDetails',
};

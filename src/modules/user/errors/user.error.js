import { createCustomError } from '#src/utils/error.util.js';

const moduleName = 'User'; // Module name for error messages

/**
 * Creates a custom error for the user module.
 * @param {string} code - Error code for the custom error.
 * @param {string} message - Error message for the custom error with optional placeholders.
 * @param {number} statusCode - HTTP status code for the custom error.
 * @returns {Function} A constructor function for the custom error.
 */
const createUserError = (code, message, statusCode) => {
  return createCustomError(code, message, moduleName, statusCode);
};

// Defining custom user errors
const userNotFound = createUserError('ERR_USER_NOT_FOUND', 'User not found with ID: %s', 404);

export { userNotFound };

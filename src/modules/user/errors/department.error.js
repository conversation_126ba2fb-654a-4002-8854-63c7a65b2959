import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { createModuleErrors } from '#src/utils/error.util.js';

const DEPARTMENT_ERROR_DEF = {
  duplicate: ['15409', 'Department with name %s already exists', 409],
  notFound: ['15404', 'Department not found with id: %s', 404],
};

export const departmentError = createModuleErrors(MODULE_NAMES.DEPARTMENT, DEPARTMENT_ERROR_DEF);

import { DepartmentHandler } from '#src/modules/user/handlers/index.js';
import { DepartmentSchema } from '#src/modules/user/schemas/index.js';

/**
 * Department API routes.
 *
 * @param {import('fastify').FastifyInstance} fastify - Fastify instance
 * @param {Object} opts - Optional Fastify plugin options
 */
const DepartmentRoute = async (fastify, opts) => {
  const PREFIX = '/:id';

  // Define the common access configuration for all routes
  const commonAccessConfig = {
    user: true,
    member: false,
    webhook: false,
    public: false,
    ipWhitelist: ['127.0.0.1'],
  };

  fastify.get('/', {
    schema: DepartmentSchema.index,
    config: { name: 'department.index', access: commonAccessConfig },
    handler: DepartmentHandler.index,
  });

  fastify.get(PREFIX, {
    schema: DepartmentSchema.view,
    config: { name: 'department.view', access: commonAccessConfig },
    handler: DepartmentHandler.view,
  });

  fastify.post('/', {
    schema: DepartmentSchema.create,
    config: { name: 'department.create', access: commonAccessConfig },
    handler: DepartmentHandler.create,
  });

  fastify.patch(`${PREFIX}/basic-information`, {
    schema: DepartmentSchema.updateBasicInformation,
    config: { name: 'department.update', access: commonAccessConfig },
    handler: DepartmentHandler.updateBasicInformation,
  });

  fastify.patch(`${PREFIX}/module-policy`, {
    schema: DepartmentSchema.updateModulePolicy,
    config: { name: 'department.update', access: commonAccessConfig },
    handler: DepartmentHandler.updateModulePolicy,
  });

  fastify.patch(`${PREFIX}/status`, {
    schema: DepartmentSchema.updateStatus,
    config: { name: 'department.updateStatus', access: commonAccessConfig },
    handler: DepartmentHandler.updateStatus,
  });

  fastify.delete(PREFIX, {
    schema: DepartmentSchema.remove,
    config: { name: 'department.remove', access: commonAccessConfig },
    handler: DepartmentHandler.remove,
  });

  fastify.get(`/options`, {
    schema: DepartmentSchema.options,
    config: { name: 'department.options', access: commonAccessConfig },
    handler: DepartmentHandler.options,
  });
};

export default DepartmentRoute;

import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { DepartmentService } from '#src/modules/user/services/index.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

const {
  CACHE_SECOND: { SHORT },
  MODULE_NAMES: { DEPARTMENT_TEMPLATE },
  MODULE_METHODS: {
    CREATE,
    DELETE,
    INDEX,
    OPTION,
    UPDATE_MODULE_POLICY,
    UPDATE_STATUS,
    UPDATE_BASIC_INFORMATION,
    VIEW,
  },
} = CoreConstant;
const MODULE = DEPARTMENT_TEMPLATE;

/**
 * Handles the retrieval of department template data with pagination.
 *
 * @param {Object} request - The request object containing query parameters and other request data.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the department template data and pagination details.
 */
export const index = (request, reply) => {
  const cacheKey = generateCacheKey(`${MODULE}_${INDEX}`, request);
  const cachedServiceFn = () =>
    fetchFromCache(
      request.server.redis,
      cacheKey,
      () => DepartmentService.index(request, true),
      SHORT,
    );

  return handleServiceResponse({
    request,
    reply,
    serviceFn: cachedServiceFn,
    module: MODULE,
    method: INDEX,
  });
};

/**
 * Handles the retrieval of a specific department template view.
 * This function uses caching to improve performance for repeated requests.
 *
 * @param {Object} request - The incoming HTTP request object containing query parameters and other request data.
 * @param {Object} reply - The HTTP response object used to send the response back to the client.
 * @returns {Promise<Object>} A promise that resolves with the service response,
 *                            typically containing the requested department template view data.
 */
export const view = (request, reply) => {
  const cacheKey = generateCacheKey(`${MODULE}_${VIEW}`, request);
  const cachedServiceFn = () =>
    fetchFromCache(request.server.redis, cacheKey, () => DepartmentService.view(request), SHORT);

  return handleServiceResponse({
    request,
    reply,
    serviceFn: cachedServiceFn,
    module: MODULE,
    method: VIEW,
  });
};

/**
 * Handles the creation of a new department template.
 *
 * @param {Object} request - The incoming HTTP request object containing the new department template data.
 * @param {Object} reply - The HTTP response object used to send the response back to the client.
 * @returns {Promise<Object>} A promise that resolves with the service response,
 *                            typically containing the newly created department template data.
 */
export const create = (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: () => DepartmentService.create(request, true),
    module: MODULE,
    method: CREATE,
  });

/**
 * Handles the update of basic information for a department template.
 *
 * @param {Object} request - The incoming HTTP request object containing the updated department template data.
 * @param {Object} reply - The HTTP response object used to send the response back to the client.
 * @returns {Promise<Object>} A promise that resolves with the service response,
 *                            typically containing the updated department template data.
 */
export const updateBasicInformation = (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: DepartmentService.updateBasicInformation,
    module: MODULE,
    method: UPDATE_BASIC_INFORMATION,
  });

/**
 * Handles the update of module policy for a department template.
 *
 * @param {Object} request - The incoming HTTP request object containing the updated module policy data.
 * @param {Object} reply - The HTTP response object used to send the response back to the client.
 * @returns {Promise<Object>} A promise that resolves with the service response,
 *                            typically containing the updated module policy data.
 */
export const updateModulePolicy = (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: DepartmentService.updateModulePolicy,
    module: MODULE,
    method: UPDATE_MODULE_POLICY,
  });

/**
 * Handles the update of status for a department template.
 *
 * @param {Object} request - The incoming HTTP request object containing the updated status data.
 * @param {Object} reply - The HTTP response object used to send the response back to the client.
 * @returns {Promise<Object>} A promise that resolves with the service response,
 *                            typically containing the updated department template status.
 */
export const updateStatus = (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: DepartmentService.updateStatus,
    module: MODULE,
    method: UPDATE_STATUS,
  });

/**
 * Handles the removal of a department template.
 *
 * @param {Object} request - The incoming HTTP request object containing the department template ID to be removed.
 * @param {Object} reply - The HTTP response object used to send the response back to the client.
 * @returns {Promise<Object>} A promise that resolves with the service response,
 *                            typically containing the result of the removal operation.
 */
export const remove = (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: DepartmentService.remove,
    module: MODULE,
    method: DELETE,
  });

/**
 * Handles the options request for department templates.
 *
 * @param {Object} request - The incoming HTTP request object.
 * @param {Object} reply - The HTTP response object used to send the response.
 * @returns {Promise<Object>} A promise that resolves with the service response,
 *                            typically containing department template options data.
 */
export const options = (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: () => DepartmentService.options(request),
    module: MODULE,
    method: OPTION,
  });

import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { DepartmentService } from '#src/modules/user/services/index.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

const {
  CACHE_SECOND: { SHORT },
  MODULE_NAMES: { DEPARTMENT },
  MODULE_METHODS: {
    CREATE,
    DELETE,
    INDEX,
    OPTION,
    UPDATE_MODULE_POLICY,
    UPDATE_STATUS,
    UPDATE_BASIC_INFORMATION,
    VIEW,
  },
} = CoreConstant;
const MODULE = DEPARTMENT;

/**
 * Handles the retrieval of department data with pagination.
 *
 * @param {Object} request - The request object containing query parameters and other request data.
 * @param {Object} reply - The reply object used to send the response back to the client.
 * @returns {Promise<Object>} The response object containing the department data and pagination details.
 */
export const index = async (request, reply) => {
  const cacheKey = generateCacheKey(`${MODULE}_${INDEX}`, request);
  const cachedServiceFn = () =>
    fetchFromCache(
      request.server.redis,
      cacheKey,
      () => DepartmentService.index(request, false),
      SHORT,
    );

  return handleServiceResponse({
    request,
    reply,
    serviceFn: cachedServiceFn,
    module: MODULE,
    method: INDEX,
  });
};

/**
 * Handles the retrieval of a specific department view.
 * This function uses caching to improve performance for repeated requests.
 *
 * @param {Object} request - The incoming HTTP request object containing query parameters and other request data.
 * @param {Object} reply - The HTTP response object used to send the response back to the client.
 * @returns {Promise<Object>} A promise that resolves with the service response,
 *                            typically containing the requested department view data.
 */
export const view = async (request, reply) => {
  const cacheKey = generateCacheKey(`${MODULE}_${VIEW}`, request);
  const cachedServiceFn = () =>
    fetchFromCache(request.server.redis, cacheKey, () => DepartmentService.view(request), SHORT);

  return handleServiceResponse({
    request,
    reply,
    serviceFn: cachedServiceFn,
    module: MODULE,
    method: VIEW,
  });
};

/**
 * Handles the creation of a new department.
 *
 * @param {Object} request - The incoming HTTP request object containing the new department data.
 * @param {Object} reply - The HTTP response object used to send the response back to the client.
 * @returns {Promise<Object>} A promise that resolves with the service response,
 *                            typically containing the newly created department data.
 */
export const create = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: () => DepartmentService.create(request, false),
    module: MODULE,
    method: CREATE,
  });

/**
 * Handles the update of basic information for a department.
 *
 * @param {Object} request - The incoming HTTP request object containing the updated department data.
 * @param {Object} reply - The HTTP response object used to send the response back to the client.
 * @returns {Promise<Object>} A promise that resolves with the service response,
 *                            typically containing the updated department data.
 */
export const updateBasicInformation = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: DepartmentService.updateBasicInformation,
    module: MODULE,
    method: UPDATE_BASIC_INFORMATION,
  });

/**
 * Handles the update of module policy for a department.
 *
 * @param {Object} request - The incoming HTTP request object containing the updated module policy data.
 * @param {Object} reply - The HTTP response object used to send the response back to the client.
 * @returns {Promise<Object>} A promise that resolves with the service response,
 *                            typically containing the updated module policy data.
 */
export const updateModulePolicy = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: DepartmentService.updateModulePolicy,
    module: MODULE,
    method: UPDATE_MODULE_POLICY,
  });

/**
 * Handles the update of status for a department.
 *
 * @param {Object} request - The incoming HTTP request object containing the updated status data.
 * @param {Object} reply - The HTTP response object used to send the response back to the client.
 * @returns {Promise<Object>} A promise that resolves with the service response,
 *                            typically containing the updated department status.
 */
export const updateStatus = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: DepartmentService.updateStatus,
    module: MODULE,
    method: UPDATE_STATUS,
  });

/**
 * Handles the removal of a department.
 *
 * @param {Object} request - The incoming HTTP request object containing the department ID to be removed.
 * @param {Object} reply - The HTTP response object used to send the response back to the client.
 * @returns {Promise<Object>} A promise that resolves with the service response,
 *                            typically containing the result of the removal operation.
 */
export const remove = async (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: DepartmentService.remove,
    module: MODULE,
    method: DELETE,
  });

/**
 * Handles the options request for departments.
 *
 * @param {Object} request - The incoming HTTP request object.
 * @param {Object} reply - The HTTP response object used to send the response.
 * @returns {Promise<Object>} A promise that resolves with the service response,
 *                            typically containing department options data.
 */
export const options = (request, reply) =>
  handleServiceResponse({
    request,
    reply,
    serviceFn: () => DepartmentService.options(request),
    module: MODULE,
    method: OPTION,
  });

import { UserService } from '#src/modules/user/services/index.js';
import { formatSuccessResponse } from '#src/utils/response.util.js';

export const index = async (request, reply) => {
  const { page = 1, size = 25, username } = request.query;
  const result = await UserService.index(request.server, page, size, username);
  return reply.send(
    formatSuccessResponse('Users retrieved successfully', result.users, {
      totalRows: result.totalRows,
      totalPages: result.totalPages,
      currentPage: result.currentPage,
    }),
  );
};

export const view = async (request, reply) => {
  const { id } = request.params;
  const result = await UserService.view(request.server, id);
  return reply.send(formatSuccessResponse('User retrieved successfully', result.user));
};

import { Op } from 'sequelize';

export const findAll = async (fastify, page = 1, limit = 25, filters = {}) => {
  const offset = (page - 1) * limit;
  const { username } = filters;

  const queryOptions = {
    limit,
    offset,
    where: username ? { username: { [Op.like]: `%${username}%` } } : null,
  };

  return await fastify.psql.User.findAndCountAll(queryOptions);
};

export const findById = async (fastify, id) => await fastify.psql.User.findOne({ where: { id } });

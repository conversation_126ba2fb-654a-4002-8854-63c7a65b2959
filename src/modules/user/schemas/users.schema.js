export const getUsersSchema = {
  tags: ['BO / Users'],
  summary: 'Get all users',
  description: 'Retrieves a paginated list of users, optionally filtered by username.',
  querystring: {
    type: 'object',
    properties: {
      page: { type: 'integer', minimum: 1, default: 1, description: 'Page number' },
      size: {
        type: 'integer',
        minimum: 1,
        maximum: 100,
        default: 25,
        description: 'Items per page',
      },
      username: { type: 'string', description: 'Filter users by username' },
    },
  },
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'integer' },
              username: { type: 'string' },
              name: { type: 'string' },
              // Add other user properties as needed
            },
          },
        },
        meta: {
          type: 'object',
          properties: {
            totalRows: { type: 'integer' },
            totalPages: { type: 'integer' },
            currentPage: { type: 'integer' },
          },
        },
      },
    },
    '4xx': { $ref: 'ErrorResponse' },
    '5xx': { $ref: 'ErrorResponse' },
  },
  examples: [
    {
      description: 'Example Response',
      value: {
        success: true,
        message: 'Users retrieved successfully',
        data: [
          { id: 1, username: 'user1', name: 'user 1' },
          { id: 2, username: 'user2', name: 'user 2' },
        ],
        meta: {
          totalRows: 50,
          totalPages: 5,
          currentPage: 1,
        },
      },
    },
  ],
};

export const getUserByIdSchema = {
  tags: ['BO / Users'],
  summary: 'Get user by ID',
  description: 'Retrieves a single user by their ID.',
  params: {
    type: 'object',
    properties: {
      id: {
        type: 'integer',
        description: 'User ID',
      },
    },
  },
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'object',
          properties: {
            id: { type: 'integer' },
            username: { type: 'string' },
            name: { type: 'string' },
            // Add other user properties as needed
          },
        },
      },
      examples: [
        {
          description: 'Example Response',
          value: {
            message: 'User retrieved successfully',
            data: { id: 1, username: 'user1', name: 'user 1' },
          },
        },
      ],
    },
    '4xx': { $ref: 'ErrorResponse' },
    '5xx': { $ref: 'ErrorResponse' },
  },
};

import { UserError } from '#src/modules/user/errors/index.js';
import { UserRepository } from '#src/modules/user/repository/index.js';

/**
 * Retrieves a paginated list of users, optionally filtered by username.
 *
 * @param {FastifyInstance} fastify - The Fastify server instance.
 * @param {number} page - The page number to retrieve.
 * @param {number} size - The number of items per page to retrieve.
 * @param {string} [username] - The username to filter by.
 *
 * @returns {Promise<{ users: User[], totalRows: number, totalPages: number, currentPage: number }>}
 * The retrieved users, along with the total number of rows, the total number of pages,
 * and the current page number.
 */
export const index = async (fastify, page, size, username) => {
  const { count, rows } = await UserRepository.findAll(fastify, page, size, username);
  return {
    users: rows,
    totalRows: count,
    totalPages: Math.ceil(count / size),
    currentPage: page,
  };
};

/**
 * Retrieves a single user by their ID.
 *
 * @param {FastifyInstance} fastify - The Fastify server instance.
 * @param {number} id - The ID of the user to retrieve.
 *
 * @returns {Promise<{user: User}>} A promise that resolves to the user object if found.
 * The user object contains all the user details stored in the database.
 *
 * @throws {UserNotFoundError} An error object if a user with the given ID is not found.
 */
export const view = async (fastify, id) => {
  const user = await UserRepository.findById(fastify, id);
  if (!user) {
    throw new UserError.userNotFound(id, { details: ['extra details'] });
  }
  return { user };
};

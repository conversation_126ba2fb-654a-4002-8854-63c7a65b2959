import { DataTypes, Model } from 'sequelize';

function UserModel(fastify) {
  class User extends Model {}

  User.init(
    {
      username: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
    },
    {
      sequelize: fastify.psql.connection,
      modelName: 'User',
      tableName: 'users',
      timestamps: true,
      underscored: true,
    },
  );

  return User;
}

export default UserModel;

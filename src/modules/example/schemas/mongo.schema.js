export const getMongoSchema = {
  tags: ['Mongo'],
  summary: 'Get all mongo records',
  description: 'Retrieves a list of mongo.',
  querystring: {
    type: 'object',
    properties: {},
  },
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              timestamp: { type: 'string', format: 'date-time' },
            },
          },
        },
      },
    },
    '4xx': { $ref: 'ErrorResponse' },
    '5xx': { $ref: 'ErrorResponse' },
  },
  examples: [
    {
      description: 'Example Response',
      value: {
        success: true,
        message: 'Mongos retrieved successfully',
        data: [
          {
            _id: '60d5ecb8b98a7e001f3e5f8a',
            name: 'EXAMPLE',
            timestamp: '2023-06-25T12:34:56.789Z',
          },
        ],
      },
    },
  ],
};

export const createMongoSchema = {
  tags: ['Mongo'],
  summary: 'Create a new mongo entry',
  description: 'Creates a new mongo entry in the system.',
  body: {
    type: 'object',
    required: ['name'],
    properties: {
      name: { type: 'string' },
    },
  },
  response: {
    201: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'object',
          properties: {
            name: { type: 'string' },
            timestamp: { type: 'string', format: 'date-time' },
          },
        },
      },
    },
    '4xx': { $ref: 'ErrorResponse' },
    '5xx': { $ref: 'ErrorResponse' },
  },
};

export const createCollectionSchema = {
  body: {
    type: 'object',
    required: ['name', 'fields'],
    properties: {
      name: { type: 'string' },
      fields: {
        type: 'array',
        items: {
          type: 'object',
          required: ['name', 'type'],
          properties: {
            name: { type: 'string' },
            type: { type: 'string' },
          },
        },
      },
    },
  },
  response: {
    200: {
      type: 'object',
      properties: {
        message: { type: 'string' },
        data: {
          type: 'object',
          properties: {
            name: { type: 'string' },
            fields: { type: 'array' },
          },
        },
      },
    },
  },
};

export const createDocumentSchema = {
  body: {
    type: 'object',
    required: ['collectionName', 'document'],
    properties: {
      collectionName: { type: 'string' },
      document: { type: 'object' },
    },
  },
  response: {
    200: {
      type: 'object',
      properties: {
        message: { type: 'string' },
        data: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            name: { type: 'string' },
          },
        },
      },
    },
  },
};

export const searchDocumentSchema = {
  querystring: {
    type: 'object',
    required: ['collectionName', 'q', 'queryBy'],
    properties: {
      collectionName: { type: 'string' },
      q: { type: 'string' },
      queryBy: { type: 'string' },
    },
  },
  response: {
    200: {
      type: 'object',
      properties: {
        message: { type: 'string' },
        data: {
          type: 'object',
          properties: {
            found: { type: 'number' },
            hits: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  document: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      name: { type: 'string' },
                    },
                  },
                  name: { type: 'string' },
                },
              },
            },
          },
        },
      },
    },
  },
};

import { formatErrorResponse, formatSuccessResponse } from '#src/utils/response.util.js';
import { uploadSchema } from '#src/schemas/upload.schema.js';
import uploadToS3 from '#src/utils/upload.util.js';

/**
 * Registers the upload route with Fastify.
 *
 * @param {object} fastify - The Fastify instance.
 * @param {object} opts - Route options (optional).
 */
const UploadRoute = (fastify, handler) => {
  fastify.route({
    method: 'POST',
    url: '/upload',
    schema: uploadSchema,
    handler: async (request, reply) => {
      try {
        const { file } = request.body;
        const fileBuffer = await file.toBuffer();

        if (!fileBuffer || fileBuffer.length === 0) {
          return reply.status(400).send(formatErrorResponse('No file uploaded', 'BAD_REQUEST'));
        }

        const uploadedFileUrl = await uploadToS3(fileBuffer, file.filename, file.mimetype, fastify);
        return reply.status(200).send(
          formatSuccessResponse('File uploaded successfully', {
            success: true,
            file: uploadedFileUrl,
          }),
        );
      } catch (error) {
        fastify.log.error(error);
        return reply
          .status(500)
          .send(formatErrorResponse('Internal server error', 'INTERNAL_SERVER_ERROR'));
      }
    },
  });
};

export default UploadRoute;

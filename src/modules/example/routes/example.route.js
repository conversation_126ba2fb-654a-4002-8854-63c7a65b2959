import { fetchFromCache } from '#src/utils/cache.util.js';
// Root route
const ExampleRoute = (fastify, handler) => {
  fastify.get('/', async function (request, reply) {
    let count = (fastify.count || 0) + 1;
    fastify.count = count;
    const message = fastify.config.DOCKER_CONTAINER
      ? `Hello from <PERSON><PERSON>! I have been seen ${count} times.`
      : `Hello World! I have been seen ${count} times.`;

    return reply.status(200).send({
      success: true,
      message: message,
      data: { count },
    });
  });

  // Health check route
  fastify.get('/health-check', (request, reply) => {
    return reply.status(200).send({ success: true });
  });

  fastify.get('/cache', async (request, reply) => {
    const cacheKey = 'cache_route';
    const responseData = await fetchFromCache(fastify.redis, cacheKey, async () => {
      const fetchedData = {
        message: 'Hello, this is fresh data!',
        timestamp: new Date().toISOString(),
      };
      return fetchedData;
    });

    return reply.send({ source: 'generated', data: responseData });
  });

  fastify.get('/clear-all-cache', async (request, reply) => {
    await fastify.redis.flushall();
    return reply.send({ message: 'All cache cleared successfully.' });
  });
};

export default ExampleRoute;

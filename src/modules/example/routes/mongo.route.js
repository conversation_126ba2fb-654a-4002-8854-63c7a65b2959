import { MongoSchema } from '#src/modules/example/schemas/index.js';
import { formatSuccessResponse } from '#src/utils/response.util.js';

/**
 * Registers the upload route with Fastify.
 *
 * @param {object} fastify - The Fastify instance.
 * @param {object} opts - Route options (optional).
 */
const MongoRoute = (fastify, handler) => {
  fastify.route({
    method: 'GET',
    url: '/',
    schema: MongoSchema.getMongoSchema,
    handler: async (request, reply) => {
      return reply
        .status(200)
        .send(
          formatSuccessResponse(
            'Data retrieved successfully',
            await request.server.mongo.Mongo.find(),
          ),
        );
    },
  });
  fastify.route({
    method: 'POST',
    url: '/',
    schema: MongoSchema.createMongoSchema,
    handler: async (request, reply) => {
      const { name } = request.body;
      const mongo = new fastify.mongo.Mongo({ name });
      await mongo.save();
      return reply.status(200).send(formatSuccessResponse('Data created successfully', mongo));
    },
  });
};

export default MongoRoute;

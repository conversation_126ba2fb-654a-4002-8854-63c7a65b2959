import {
  createItemSchema,
  deleteItemSchema,
  getAllItemsSchema,
  getItemByIdSchema,
  updateItemSchema,
} from '#src/schemas/items.schema.js';
import { formatErrorResponse, formatSuccessResponse } from '#src/utils/response.util.js';

/**
 * Registers the example item route with Fastify.
 *
 * @param {object} fastify - The Fastify instance.
 * @param {object} opts - Route options (optional).
 */
const ItemRoute = (fastify, handler) => {
  const items = [
    { id: 1, name: 'Example Item 1', amount: 100.5 },
    { id: 2, name: 'Example Item 2', amount: 50 },
    { id: 3, name: 'Example Item 3', amount: 30.8 },
  ];

  // Read (GET) - Get all items
  fastify.route({
    method: 'GET',
    url: '/items',
    schema: getAllItemsSchema,
    handler: (request, reply) => {
      try {
        return reply.send(formatSuccessResponse('Items retrieved successfully', items));
      } catch (error) {
        fastify.log.error(error);
        return reply.internalServerError(error.message);
      }
    },
  });

  // Read (GET) - Get item by ID
  fastify.route({
    method: 'GET',
    url: '/items/:id',
    schema: getItemByIdSchema,
    handler: (request, reply) => {
      try {
        const item = items.find((i) => i.id === parseInt(request.params.id));
        if (!item) {
          return reply.notFound(formatErrorResponse('Item not found', 'ITEM_NOT_FOUND'));
        }
        return reply.send(formatSuccessResponse('Item retrieved successfully', item));
      } catch (error) {
        fastify.log.error(error);
        return reply.internalServerError(error.message);
      }
    },
  });

  // Create (POST) - Add a new item
  fastify.route({
    method: 'POST',
    url: '/items',
    schema: createItemSchema,
    handler: (request, reply) => {
      try {
        const { name, amount } = request.body;
        const newItem = { id: items.length + 1, name, amount };
        items.push(newItem);
        return reply.status(201).send(formatSuccessResponse('Item created successfully', newItem));
      } catch (error) {
        return reply.internalServerError(error.message);
      }
    },
  });

  // Update (PUT) - Update an item by ID
  fastify.route({
    method: 'PUT',
    url: '/items/:id',
    schema: updateItemSchema,
    handler: (request, reply) => {
      try {
        const item = items.find((i) => i.id === parseInt(request.params.id));
        if (!item) {
          return reply.notFound();
        }
        const { name, amount } = request.body;
        item.name = name;
        item.amount = amount;
        return reply.send(formatSuccessResponse('Item updated successfully', item));
      } catch (error) {
        return reply.internalServerError(error.message);
      }
    },
  });

  // Delete (DELETE) - Delete an item by ID
  fastify.route({
    method: 'DELETE',
    url: '/items/:id',
    schema: deleteItemSchema,
    handler: (request, reply) => {
      try {
        const index = items.findIndex((i) => i.id === parseInt(request.params.id));
        if (index === -1) {
          return reply.notFound();
        }
        items.splice(index, 1);
        return reply.send(formatSuccessResponse('Item deleted successfully'));
      } catch (error) {
        return reply.internalServerError(error.message);
      }
    },
  });
};

export default ItemRoute;

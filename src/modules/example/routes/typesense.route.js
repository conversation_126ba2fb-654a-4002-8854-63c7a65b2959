import {
  createCollectionSchema,
  createDocumentSchema,
  searchDocumentSchema,
} from '#src/modules/example/schemas/typesense.schema.js';
import { formatSuccessResponse } from '#src/utils/response.util.js';

/**
 * Registers the upload route with Fastify.
 *
 * @param {object} fastify - The Fastify instance.
 * @param {object} opts - Route options (optional).
 */
const TypesenseRoute = (fastify, handler) => {
  fastify.route({
    method: 'POST',
    url: '/collection',
    schema: createCollectionSchema,
    handler: async (request, reply) => {
      const { name, fields } = request.body;
      const collection = await request.server.typesense.collections().create({
        name,
        fields,
      });
      return reply
        .status(200)
        .send(formatSuccessResponse('Collection created successfully', collection));
    },
  });
  fastify.route({
    method: 'POST',
    url: '/document',
    schema: createDocumentSchema,
    handler: async (request, reply) => {
      const { collectionName, document } = request.body;
      const newDocument = await request.server.typesense
        .collections(collectionName)
        .documents()
        .create(document);
      return reply
        .status(200)
        .send(formatSuccessResponse('Document created successfully', newDocument));
    },
  });
  fastify.route({
    method: 'GET',
    url: '/document',
    schema: searchDocumentSchema,
    handler: async (request, reply) => {
      const { collectionName, q, queryBy } = request.query;

      const newDocument = await request.server.typesense
        .collections(collectionName)
        .documents()
        .search({
          q,
          query_by: queryBy,
        });
      return reply
        .status(200)
        .send(formatSuccessResponse('Document retrieved successfully', newDocument));
    },
  });
};

export default TypesenseRoute;

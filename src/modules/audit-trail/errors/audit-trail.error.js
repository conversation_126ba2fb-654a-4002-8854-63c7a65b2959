import { MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { createModuleErrors } from '#src/utils/error.util.js';

const AUDIT_TRAIL_ERROR_DEF = {
  invalidDate: ['40001', 'End date time must be within the same month as start date time', 400],
  missingFilter: ['40002', '%s is required', 400],
};

export const auditTrailError = createModuleErrors(MODULE_NAMES.AUDIT_TRAIL, AUDIT_TRAIL_ERROR_DEF);

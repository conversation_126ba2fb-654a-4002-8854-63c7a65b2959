import { applyMongoFiltersCursorPagination } from '#src/utils/pagination.util.js';
import getAuditTrailModel from '#src/modules/core/models/mongo/audit-trail.model.js';

/**
 * Retrieves all audit trails based on the provided filters and pagination options.
 *
 * @param {Object} request - The request object containing server and query properties.
 * @param {Object} [options] - Additional options for filtering and pagination.
 * @param {Date} [options.startDateTime] - The start date and time for filtering audit trails.
 * @param {boolean} [options.isExport=false] - Indicates whether the request is for exporting data.
 *
 * @returns {Promise<Array|Object>} - A promise that resolves to an array of audit trails or an error object.
 * If successful, the promise resolves to an array of audit trails.
 * If an error occurs, the promise resolves to an object with an 'error' property containing the error message.
 */
export const findAll = async (request, { startDateTime, isExport = false } = {}) => {
  try {
    const auditTrail = getAuditTrailModel(request.server, startDateTime);

    return await applyMongoFiltersCursorPagination(auditTrail, request.query, isExport);
  } catch (err) {
    const error = err.message;

    return { error };
  }
};

/**
 * Retrieves a single audit trail by its ID.
 *
 * @param {Object} request - The request object.
 * @param {string} id - The ID of the audit trail to retrieve.
 * @returns {Promise<Object|null|{error: string}>} The found document, null if not found, or an error object.
 */
export const findById = async (request, id) => {
  try {
    const auditTrail = getAuditTrailModel(
      request.server,
      new Date(request.query.filter_timestamp_eq),
    );

    const result = await auditTrail.findOne({ _id: id }).exec();

    return result;
  } catch (err) {
    const error = err.message;

    return { error };
  }
};

import snakeCase from 'lodash/snakeCase.js';

import { DataTypes, Op, Sequelize } from 'sequelize';
import { coerceAjv as ajv } from './validation.util.js';
import { createValidationError } from './error.util.js';

/**
 * List of operators that expect array values (comma-separated strings that will be split).
 * @type {string[]}
 * @private
 */
const arrayType = ['in', 'notIn', 'between', 'notBetween', 'overlap'];

/**
 * Mapping of API filter operators to Sequelize operators.
 * Used to translate filter query parameters to Sequelize query conditions.
 * @type {Object.<string, Symbol>}
 * @private
 */
const operatorMap = {
  eq: Op.eq,
  ne: Op.ne,
  gt: Op.gt,
  gte: Op.gte,
  lt: Op.lt,
  lte: Op.lte,
  like: Op.like,
  notLike: Op.notLike,
  iLike: Op.iLike,
  notILike: Op.notILike,
  in: Op.in,
  notIn: Op.notIn,
  between: Op.between,
  notBetween: Op.notBetween,
  overlap: Op.overlap,
  ipContains: 'ipContains',
};

/**
 * Create JSON schema for validation based on data type.
 *
 * @param {DataTypes} attributeType - The Sequelize data type
 * @returns {Object} A JSON schema object compatible with AJV validation
 * @private
 */
const createSchemaFromAttribute = (attributeType) => {
  if (attributeType instanceof DataTypes.BOOLEAN) {
    return { type: 'boolean' };
  }
  if (attributeType instanceof DataTypes.INTEGER || attributeType instanceof DataTypes.BIGINT) {
    return { type: 'integer' };
  }
  if (attributeType instanceof DataTypes.JSON || attributeType instanceof DataTypes.JSONB) {
    return { type: 'object' };
  }

  if (
    attributeType instanceof DataTypes.FLOAT ||
    attributeType instanceof DataTypes.REAL ||
    attributeType instanceof DataTypes.DOUBLE ||
    attributeType instanceof DataTypes.DECIMAL
  ) {
    return { type: 'number' };
  }

  if (
    attributeType instanceof DataTypes.STRING ||
    attributeType instanceof DataTypes.TEXT ||
    attributeType instanceof DataTypes.CHAR
  ) {
    return { type: 'string' };
  }

  if (attributeType instanceof DataTypes.ENUM) {
    return {
      type: 'string',
      enum: attributeType.values,
    };
  }

  if (attributeType instanceof DataTypes.UUID) {
    return { type: 'string', format: 'uuid' };
  }

  if (attributeType instanceof DataTypes.DATE) {
    return { type: 'string', format: 'date-time' };
  }

  if (attributeType instanceof DataTypes.ARRAY) {
    return {
      type: 'array',
      items: createSchemaFromAttribute(attributeType.type),
    };
  }

  return {};
};

/**
 * Adapts validation errors to match fastify format.
 *
 * @param {Array} error - Array of validation error objects from AJV
 * @param {string} key - The filter key that caused the validation error
 * @returns {Array} Enhanced error objects with additional context properties
 * @private
 */
const adaptValidationError = (error, key) => {
  return error.map((err) => ({
    ...err,
    instancePath: `/${key}`, // add attribute to validation error
    schemaPath: err.schemaPath || '',
    keyword: err.keyword || '',
    params: err.params || {},
  }));
};

/**
 * Validates if an attribute exists in the model and if the query value is valid for the attribute's type.
 *
 * @param {Object} model - The Sequelize model to validate against.
 * @param {string} attributeName - The name of the attribute to check.
 * @param {*} query - The query value(s) to validate against the attribute's type and constraints.
 *                   Can be a single value or an array of values.
 * @returns {Object} An object with either:
 *                   - { valid: true } if validation passes
 *                   - { error: Array<Object> } containing validation error details if validation fails
 * @private
 * @example
 * // Check direct attribute
 * checkAttributeValid(UserModel, 'status', 'active')
 * // Returns: { valid: true } or { error: [{ message: 'has invalid attribute' }] }
 *
 * // Check array values against enum
 * checkAttributeValid(UserModel, 'roles', ['admin', 'editor'])
 * // Returns: { valid: true } or { error: [...validation errors] }
 */
const checkAttributeValid = (model, attributeName, filterValue) => {
  // Check column exists
  const attribute = model.rawAttributes[attributeName];
  if (!attribute) {
    return { error: [{ message: 'has invalid attribute' }] };
  }

  // Validate value
  const filterValues = Array.isArray(filterValue) ? filterValue : [filterValue];

  // Loop through the data
  for (const data of filterValues) {
    const schema = createSchemaFromAttribute(attribute.type);
    const validate = ajv.compile(schema);

    let dataToValidate = data;
    if (schema?.type === 'array') {
      dataToValidate = [data];
    }
    const isValid = validate(dataToValidate);
    if (!isValid) {
      // When one value in array value is invalid, return immediately
      return { error: validate.errors };
    }
  }

  return { valid: true };
};

/**
 * Validates if an association exists in the model and returns the target model.
 *
 * @param {Object} model - The Sequelize model to check for the association.
 * @param {string} associateName - The name of the association to validate.
 * @returns {Object|false} The target model of the association if valid, false otherwise.
 * @private
 * @example
 * // Check if User model has a 'wallet' association
 * const walletModel = checkAssociateValid(UserModel, 'wallet');
 *
 * // Check if Post model has a 'comments' association
 * const commentModel = checkAssociateValid(PostModel, 'comments');
 */
const checkAssociateValid = (model, associateName) => {
  const association = model.associations[associateName];
  if (!association) {
    return false;
  }
  return association.target;
};

/**
 * Builds nested include options for Sequelize queries with associated where conditions.
 * This function recursively constructs the include structure for nested associations
 * and applies where conditions to the deepest level.
 *
 * @param {Array} includeArray - The array to build or extend with include options
 * @param {Array<string>} levels - Array of association names representing the hierarchy of associations
 * @param {Object} where - The where conditions to apply to the deepest level association
 * @returns {void} Modifies the includeArray in place
 * @example
 * // Build include for user -> wallet association with conditions
 * const includes = [];
 * buildIncludeOptions(includes, ['user', 'wallet'], { balance: { [Op.gt]: 100 } });
 *
 * // Build include for deeply nested associations
 * buildIncludeOptions(includes, ['businessPartner', 'category', 'subCategory'], { status: { [Op.eq]: 'active' } });
 */
const buildIncludeOptions = (includeArray, levels, where) => {
  let currentLevel = includeArray;

  for (let i = 0; i < levels.length; i++) {
    const associationName = levels[i];

    // Find or create association include
    let association = currentLevel.find((item) => item.association === associationName);

    if (!association) {
      // Association not found, create new
      association = { association: associationName };
      currentLevel.push(association);
    }

    if (i === levels.length - 1) {
      // Merge new where object into existing where object
      association.where = {
        ...(association.where || {}),
        ...where,
      };
    } else {
      if (!association.include) {
        // Create if not found
        association.include = [];
      }

      // Move to next deeper nested include
      currentLevel = association.include;
    }
  }
};

/**
 * Merges filter conditions into base include options for Sequelize queries.
 * This function recursively combines where conditions from filter includes into
 * matching base includes based on association names.
 *
 * @param {Array} baseIncludes - The base include options array for Sequelize eager loading
 * @param {Array} filterIncludes - The filter include options containing where conditions to merge
 * @returns {Array} The modified baseIncludes array with merged where conditions
 */
const mergeIncludeFilters = (baseIncludes, filterIncludes) => {
  for (const base of baseIncludes) {
    const match = filterIncludes.find((find) => find.association === base.association);
    if (!match) {
      continue;
    }

    // Merge `where` conditions if present in both base and filter includes
    if (match.where) {
      base.where = {
        ...(base.where || {}),
        ...match.where,
      };
    }

    // Continue with deeper level
    if (base.include && match.include) {
      mergeIncludeFilters(base.include, match.include);
    }
  }

  return baseIncludes;
};

/**
 * Exports internal utility functions for unit testing purposes.
 */
export const exportForUnitTest = {
  adaptValidationError,
  buildIncludeOptions,
  checkAssociateValid,
  checkAttributeValid,
  createSchemaFromAttribute,
  mergeIncludeFilters,
};

/**
 * Builds Sequelize where conditions and include options from filter parameters.
 * @param {Object} filters - An object containing filter key-value pairs in format filter_fieldName_operator.
 * @param {Object} model - The Sequelize model to validate columns and associations against.
 * @param {Array} [includes=[]] - Optional base include options to merge with filter-generated includes.
 * @returns {Object} An object with where conditions and include options for Sequelize queries.
 * @throws {Error} Throws validation error if any filter value is invalid for its attribute type.
 * @example
 * // Direct field filter
 * buildWhereFromFilters({ 'filter_name_like': '%John%' }, UserModel)
 * // Returns: { where: { name: { [Op.like]: '%John%' } }, include: [] }
 *
 * // Simple association filter
 * const baseIncludes = [{ association: 'user' }];
 * buildWhereFromFilters({ 'filter_user.name_eq': 'John' }, WalletModel, baseIncludes)
 * // Returns: {
 * //   where: {},
 * //   include: [{ association: 'user', where: { name: { [Op.eq]: 'John' } } }]
 * // }
 *
 * // Multiple filters
 * const baseIncludes = [{ association: 'wallet' }];
 * buildWhereFromFilters({
 *   'filter_status_eq': 'active',
 *   'filter_createdAt_gte': '2023-01-01',
 *   'filter_wallet.balance_between': '1,100'
 * }, UserModel, baseIncludes)
 * // Returns: {
 * //   where: {
 * //     status: { [Op.eq]: 'active' },
 * //     createdAt: { [Op.gte]: '2023-01-01' }
 * //   },
 * //   include: [{ association: 'wallet', where: { balance: { [Op.between]: ['1', '100'] } } }]
 * // }
 *
 * // Deeply nested association filter
 * const baseIncludes = [{
 *   association: 'businessPartner',
 *   include: [{
 *     association: 'category', include: [{ association: 'subCategory' }]
 *   }],
 * }];
 * buildWhereFromFilters({
 *   'filter_businessPartner.category.subCategory.name_eq': 'Freespin'
 * }, GameProviderModel, baseIncludes)
 * // Returns: {
 * //   where: {},
 * //   include: [{
 * //     association: 'businessPartner',
 * //     include: [{
 * //       association: 'category',
 * //       include: [{
 * //         association: 'subCategory',
 * //         where: { name: { [Op.eq]: 'Freespin' } }
 * //       }]
 * //     }]
 * //   }]
 * // }
 *
 * @description
 * This function parses filter parameters and builds Sequelize query conditions.
 * It supports:
 * - Direct field filtering (filter_name_eq: 'value')
 * - Association filtering with dot notation (filter_user.email_eq: 'value')
 * - Deeply nested association filtering (filter_businessPartner.category.subCategory.betLimit.betLimitGroup.name_eq: 'value')
 * - Automatic validation of field existence and enum values
 * - Array value handling for 'in', 'notIn', 'between', 'notBetween', 'overlap' operators
 * - Merging with existing include options to maintain eager loading requirements
 *
 * Supported operators: eq, ne, gt, gte, lt, lte, like, notLike, iLike, notILike, in, notIn, between, notBetween, overlap
 */
export const buildWhereFromFilters = (filters, model, includes = []) => {
  const result = { where: {}, include: [] };
  const errors = [];

  Object.entries(filters)
    .filter(([key, value]) => {
      return key.startsWith('filter_') && value !== undefined && value !== null;
    })
    .forEach(([key, value]) => {
      // break down the filter key for operator
      const [, ...fieldParts] = key.split('_');
      const operator = fieldParts.pop();
      const fieldPath = fieldParts.join('_');

      if (!operatorMap[operator]) {
        return;
      }

      // break down the association and attribute path (eg. member.wallet.balance)
      const [baseField, ...nestedFields] = fieldPath.split('.');

      const filterValue = arrayType.includes(operator) ? value.split(',') : value;

      if (nestedFields.length === 0) {
        const validationResult = checkAttributeValid(model, baseField, filterValue);
        if (validationResult.error) {
          errors.push(...adaptValidationError(validationResult.error, key));
          return;
        }

        applyFilterCondition(result, baseField, operator, filterValue);
      } else {
        // Pop the attribute from nestedFields array
        const associationColumn = nestedFields.pop();

        // Put 1st level association back into nestedFields array
        nestedFields.unshift(baseField);

        // Check the model association is valid
        let associateModel = model;
        for (const field of nestedFields) {
          associateModel = checkAssociateValid(associateModel, field);
          if (!associateModel) {
            return;
          }
        }

        // Check if the attribute exist in the last association model
        const validationResult = checkAttributeValid(
          associateModel,
          associationColumn,
          filterValue,
        );
        if (validationResult.error) {
          errors.push(...adaptValidationError(validationResult.error, key));
          return;
        }

        // build the nested includes
        const nestedWhere = {
          [associationColumn]: { [operatorMap[operator]]: filterValue },
        };
        buildIncludeOptions(result.include, nestedFields, nestedWhere);
      }
    });

  if (errors.length > 0) {
    // throw error when have validation error
    throw createValidationError(errors);
  }

  // Merge include filters
  result.include = mergeIncludeFilters(includes, result.include);

  return result;
};

/**
 * Applies the filter condition to the result object based on the operator.
 * @param {Object} result - The result object to modify.
 * @param {string} baseField - The field name to apply the condition to.
 * @param {string} operator - The operator to use for the condition.
 * @param {*} filterValue - The value to use in the condition.
 */
const applyFilterCondition = (result, baseField, operator, filterValue) => {
  if (operator === 'ipContains') {
    result.where[baseField] = createIpContainsCondition(baseField, filterValue);
  } else {
    result.where[baseField] = result.where[baseField] || {};
    result.where[baseField][operatorMap[operator]] = filterValue;
  }
};

/**
 * Creates a Sequelize condition for IP address containment check.
 * @param {string} baseField - The field name in camelCase.
 * @param {string} filterValue - The IP address or range to check against.
 * @returns {Object} Sequelize condition for IP containment.
 */
const createIpContainsCondition = (baseField, filterValue) => {
  const { where, fn, col } = Sequelize;
  const snakeCaseField = snakeCase(baseField);
  const inetColumn = fn('inet', col(snakeCaseField));

  return filterValue.includes('/')
    ? where(inetColumn, '<<=', fn('inet', filterValue))
    : where(inetColumn, '>>=', fn('inet', filterValue));
};

/**
 * Prepares scopes for database queries based on provided filters.
 * @param {Object} filters - An object containing filter key-value pairs.
 * @returns {Array} An array of scope objects ready to be used in a database query.
 * @deprecated This function is deprecated and will be removed in a future.
 * Use buildWhereFromFilters instead.
 */
export const prepareScopes = (filters) => {
  return Object.entries(filters)
    .filter(([_, value]) => value !== undefined && value !== null)
    .map(([key, value]) => {
      const parts = key.split('_').filter(Boolean);
      if (parts.length === 2) {
        return {
          method: [`by${parts[1].charAt(0).toUpperCase() + parts[1].slice(1)}`, value],
        };
      }
      return {};
    });
};

/**
 * Prepares sort order for database queries based on provided sort parameters.
 * @param {Object} fastify - The Fastify instance containing psql models.
 * @param {string|Array} sortBy - A string or array of strings specifying sort order.
 * @returns {Array} An array of sort specifications ready to be used in a database query.
 * @throws {Error} If an association model specified in the sort order is not found.
 */
export const prepareSortOrder = (fastify, sortBy) => {
  if (!sortBy || sortBy.length === 0) {
    return [['id', 'DESC']];
  }

  return (Array.isArray(sortBy) ? sortBy : [sortBy]).filter(Boolean).map((item) => {
    const parts = item.split(':');
    const field = parts[0];
    const direction = parts[1].toUpperCase();

    if (!field.includes('.')) {
      return [field, direction];
    }

    // Handle association sorting
    const [association, associationField] = field.split('.');
    const capitalizedAssociation = association.charAt(0).toUpperCase() + association.slice(1);

    if (!fastify.psql[capitalizedAssociation]) {
      throw new Error(`Association model ${capitalizedAssociation} not found in fastify.psql`);
    }

    return [
      { model: fastify.psql[capitalizedAssociation], as: association },
      associationField,
      direction,
    ];
  });
};

/**
 * Prepares filters for MongoDB queries based on the provided filter object.
 *
 * @param {Object} filters - An object containing filter parameters.
 * @returns {Object} - A MongoDB query object with the applied filters.
 *
 * @example
 * const filters = {
 *   filter_name_eq: 'John',
 *   filter_age_gte: 25,
 *   filter_email_like: '<EMAIL>',
 * };
 *
 * const query = prepareMongoFilters(filters);
 * console.log(query);
 * // Output: { name: 'John', age: { $gte: 25 }, email: { $regex: '<EMAIL>', $options: 'i' } }
 */
export const prepareMongoFilters = (filters) => {
  const regex = /^filter_(.+)_(eq|like|in|gte|gt|lte|lt)$/;

  const parseDateValue = (property, value) => {
    return !isNaN(new Date(value).getTime()) ? value : new Date(value);
  };

  const operatorMap = {
    eq: (property, value) => value,
    like: (property, value) => ({ $regex: value, $options: 'i' }),
    in: (property, value) => {
      let normalized;

      if (Array.isArray(value)) {
        normalized = value;
      } else if (typeof value === 'string') {
        normalized = value.split(',');
      } else {
        normalized = [value];
      }

      return { $in: normalized };
    },
    gte: (property, value) => ({ $gte: parseDateValue(property, value) }),
    lte: (property, value) => ({ $lte: parseDateValue(property, value) }),
    gt: (property, value) => ({ $gt: parseDateValue(property, value) }),
    lt: (property, value) => ({ $lt: parseDateValue(property, value) }),
  };

  return Object.entries(filters)
    .filter(([_, value]) => value !== undefined && value !== null)
    .reduce((query, [key, value]) => {
      const match = regex.exec(key);
      if (!match) {
        return query;
      }

      const [, property, operator] = match;
      const operation = operatorMap[operator];

      const result = operation(property, value);

      if (typeof result === 'object' && !Array.isArray(result)) {
        query[property] = { ...(query[property] || {}), ...result };
      } else {
        query[property] = result;
      }
      return query;
    }, {});
};

/**
 * Prepares sorting parameters for MongoDB queries based on the provided sort string.
 *
 * @param {string} sortBy - A string representing the sorting criteria. The string should be in the format "field:direction",
 *                          where "field" is the name of the field to sort by, and "direction" is either "asc" or "desc".
 * @returns {Object} - An object containing the sort field, sort order, and cursor operator for MongoDB queries.
 *
 * @example
 * const sortBy = 'name:asc';
 * const sorting = prepareMongoSorting(sortBy);
 * console.log(sorting);
 * // Output: { sortField: 'name', sortOrder: 1, cursorOperator: '$gt' }
 */
export const prepareMongoSorting = (sortBy = 'timestamp:desc') => {
  const [sortField = 'timestamp', sortDirection = 'desc'] = sortBy.split(':');
  const normalizedDirection = (sortDirection || 'desc').toLowerCase();
  const sortOrder = normalizedDirection === 'asc' ? 1 : -1;
  const cursorOperator = normalizedDirection === 'asc' ? '$gt' : '$lt';
  return { sortField, sortOrder, cursorOperator };
};

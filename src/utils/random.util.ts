/**
 * Generates a cryptographically secure random integer within a given range.
 *
 * Uses the `crypto.getRandomValues` API to obtain a random 32-bit unsigned integer
 * and maps it to the range `[0, length - 1]` using the modulo operator.
 *
 * @param {number} length - The upper bound (exclusive) for the random index. Must be greater than 0.
 * @returns {number} A securely generated random index in the range [0, length - 1].
 *
 * @throws {Error} If `length` is less than or equal to 0.
 *
 * @example
 * const items = ['a', 'b', 'c'];
 * const index = getSecureRandomIndex(items.length);
 * console.log(items[index]); // Randomly logs 'a', 'b', or 'c'
 */
export const getSecureRandomIndex = (length: number): number => {
  if (length <= 0) throw new Error('Invalid length for random index');

  const array = new Uint32Array(1);
  crypto.getRandomValues(array);
  return array[0]! % length;
};

/**
 * Returns a secure random integer between min and max (inclusive).
 *
 * @param min - Minimum value (inclusive)
 * @param max - Maximum value (inclusive)
 * @returns A securely generated integer between min and max
 */
export const getSecureRandomInt = (min: number, max: number): number => {
  const range = max - min + 1;
  const array = new Uint32Array(1);
  crypto.getRandomValues(array);
  return min + (array[0]! % range);
};

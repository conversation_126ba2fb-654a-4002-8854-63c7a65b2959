import { REDACT_FIELDS } from '#src/modules/core/constants/core.constant.js';
import { getPackageJson } from '#src/utils/file.util.js';

const packageJson = getPackageJson();

/**
 * Checks if the response should be modified to include debug information.
 *
 * @function shouldModifyResponse
 * @param {ServerResponse} reply - The reply object
 * @returns {boolean} True if the response should be modified, false otherwise
 */
export const shouldModifyResponse = (reply) => {
  return reply.getHeader('content-type')?.toLowerCase().includes('application/json');
};

/**
 * Modifies the response payload if it's a JSON content-type response and the payload is a string
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {IncomingMessage} request - The request object
 * @param {ServerResponse} reply - The reply object
 * @param {string} payload - The response payload
 * @returns {string} The modified response payload if it's a JSON content-type response, otherwise the original payload
 */
export const modifyPayload = (fastify, request, reply, payload) => {
  const appName = process.env.APP_NAME || packageJson.name || 'fastify-app';
  const environment = process.env.NODE_ENV || 'development';
  const requestId = request.id;
  const version = packageJson.version;

  if (typeof payload === 'string' && shouldModifyResponse(reply)) {
    try {
      const jsonPayload = JSON.parse(payload);

      // Add metadata to the payload
      const modifiedPayload = {
        ...jsonPayload,
        debug: {
          appName,
          environment,
          requestId,
          version,
        },
      };
      return JSON.stringify(modifiedPayload);
    } catch (error) {
      // If it's not valid JSON, don't modify the payload
      fastify.log.debug(error, 'Failed to parse JSON payload for a JSON content-type response');
    }
  }
  return payload;
};

/**
 * Recursively redacts specified keys in an object or array.
 *
 * @function deepRedact
 * @param {object|Array} obj - The object or array to redact
 * @returns {object|Array} The redacted object or array
 *
 * @example
 * const input = {
 *   username: 'john_doe',
 *   password: 'secret123',
 *   nested: {
 *     confirmPassword: 'secret123',
 *   },
 * };
 *
 * const redacted = deepRedact(input);
 *  Output:
 *  {
 *    username: 'john_doe',
 *    password: '[REDACTED]',
 *    nested: {
 *      confirmPassword: '[REDACTED]',
 *    },
 *  }
 */
export const deepRedact = (obj) => {
  const keysToRedact = Object.values(REDACT_FIELDS);

  if (Array.isArray(obj)) {
    return obj.map((item) => deepRedact(item));
  } else if (obj && typeof obj === 'object') {
    return Object.fromEntries(
      Object.entries(obj).map(([key, value]) => {
        if (keysToRedact.includes(key)) {
          return [key, '[REDACTED]'];
        } else {
          return [key, deepRedact(value)];
        }
      }),
    );
  }

  return obj;
};

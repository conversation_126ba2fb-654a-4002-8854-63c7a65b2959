import { U<PERSON>arser } from 'ua-parser-js';
import { VIEW_ACTION } from '#src/modules/core/constants/core.constant.js';
import _ from 'lodash';
import { deepRedact } from '#src/utils/response-modifier.util.js';
import geoip from 'geoip-lite';
import { setCache } from '#src/utils/cache.util.js';

// ───── PREPARATION ─────
/**
 * Prepare and populates audit trail entries for each request.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {FastifyRequest} request - The Fastify request object
 *
 * @returns {void}
 */
const prepareAuditTrailEntry = (fastify, request) => {
  let root = null;
  let organization = null;
  let merchant = null;
  // Build audit trail entry
  if (request.entity?.hierarchy === 'merchant') {
    organization = formatEntityInfo(request.parentEntity);
    merchant = formatEntityInfo(request.entity);
  } else if (request.entity?.hierarchy === 'organization') {
    organization = formatEntityInfo(request.entity);
  } else if (request.entity?.hierarchy === 'root') {
    root = formatEntityInfo(request.entity);
  }

  request.auditEntries = {
    timestamp: new Date().toISOString(),
    entityAccessId: request.entityAccessId || 'UNKNOWN_ACCESS_ID',
    hierarchyLevel: request.hierarchyLevel || 'UNKNOWN_HIERARCHY_LEVEL',
    module: null,
    event: null,
    action: {
      translationKey: null,
      translationParams: {},
    },
    actor: {
      type: request.authInfo?.authAccess || 'UNKNOWN_ACTOR_TYPE',
      id: request.authInfo?.id || 'UNKNOWN_ACTOR_ID',
      username: request.authInfo?.username || 'UNKNOWN_ACTOR_USERNAME',
      role: request.authInfo?.role || 'UNKNOWN_ACTOR_ROLE',
      department: request.authInfo?.department || 'UNKNOWN_ACTOR_DEPARTMENT',
      root,
      organization,
      merchant,
    },
    target: [],
    details: {
      request: {
        requestId: request.id || 'UNKNOWN_REQUEST_ID',
        statusCode: null,
        path: request.url || 'UNKNOWN_REQUEST_URL',
        method: request.method || 'UNKNOWN_REQUEST_METHOD',
        parameters: request.query || 'UNKNOWN_REQUEST_QUERY_PARAMETERS',
        payload: request.body || {},
      },
      error: {},
      metrics: {},
    },
    context: {
      ip: request.headers?.['x-forwarded-for'] || 'UNKNOWN_IP',
      location: getLocation(request),
      userAgent: request.headers?.['user-agent'] || 'UNKNOWN_USER_AGENT',
      device: getDeviceInfo(request),
      fingerprintId: request.authInfo?.fingerprintId || 'UNKNOWN_FINGERPRINT_ID',
      host: request.headers?.['host'] || 'UNKNOWN_HOST',
      origin: request.headers?.['origin'] || 'UNKNOWN_ORIGIN',
    },
    status: null,
    description: {
      translationKey: null,
      translationParams: {},
    },
  };
};

/**
 * Formats entity information for audit trail entries.
 *
 * @param {Object} entity - The entity object containing information such as id, code, prefix, and name.
 * @param {string} [fallback='UNKNOWN'] - The fallback value to use if any of the entity information is not provided.
 *
 * @returns {Object|null} An object containing formatted entity information or null if the entity is not provided.
 *                        The object has the following properties: id, code, prefix, and name.
 *                        If any of the entity information is not provided, it defaults to the fallback value suffixed with '_ID', '_CODE', '_PREFIX', or '_NAME'.
 */
const formatEntityInfo = (entity, fallback = 'UNKNOWN') => {
  if (!entity) {
    return null;
  }

  return {
    id: entity?.id || `${fallback}_ID`,
    code: entity?.code || `${fallback}_CODE`,
    prefix: entity?.prefix || `${fallback}_PREFIX`,
    name: entity?.name || `${fallback}_NAME`,
  };
};

/**
 * Retrieves the location of the client based on the provided request.
 *
 * @param {Object} request - The Fastify request object containing headers and IP information.
 * @param {string} [request.ip] - The client's IP address.
 * @param {Object} [request.headers] - The headers object containing 'x-forwarded-for' information.
 *
 * @returns {string} A string representing the client's location in the format:
 *                   `${city}, ${country}`. If the city or country information is not available,
 *                   it defaults to 'UNKNOWN_CITY' and 'UNKNOWN_COUNTRY' respectively.
 */
const getLocation = (request) => {
  const ip = request.ip || request.headers?.['x-forwarded-for'];
  if (!ip) {
    return 'UNKNOWN_LOCATION';
  }

  const geo = geoip.lookup(ip);
  if (!geo) {
    return 'UNKNOWN_LOCATION';
  }

  return `${geo.city || 'UNKNOWN_CITY'}, ${geo.country || 'UNKNOWN_COUNTRY'}`;
};

/**
 * Retrieves device information from the provided request headers.
 *
 * @param {Object} request - The Fastify request object containing headers.
 * @param {Object} request.headers - The headers object containing user-agent information.
 *
 * @returns {string} A string representing the device information in the format:
 *                   `${deviceType} - ${osName} - ${browserName}`.
 *                   If any of the device, OS, or browser information is not available,
 *                   it defaults to 'Desktop', 'UNKNOWN_OS', and 'UNKNOWN_BROWSER' respectively.
 */
const getDeviceInfo = (request) => {
  const userAgent = request.headers?.['user-agent'] || '';
  if (!userAgent) {
    return 'UNKNOWN_DEVICE - UNKNOWN_OS - UNKNOWN_BROWSER';
  }

  const parser = new UAParser(userAgent);
  const ua = parser.getResult();

  return `${ua.device.type ?? 'Desktop'} - ${ua.os.name ?? 'UNKNOWN_OS'} - ${ua.browser.name ?? 'UNKNOWN_BROWSER'}`;
};

/**
 * Computes the differences between the original and updated states of an entity.
 *
 * @function computeUpdateDiff
 * @param {Object} original - The original state of the entity.
 * @param {Object} updates - The updated state of the entity.
 *
 * @returns {Object} - An object containing the original state and an array of fields that have changed.
 *
 * @example
 * const original = { id: 1, name: 'John', age: 25 };
 * const updates = { id: 1, name: 'Jane', age: 25 };
 * const diff = computeUpdateDiff(original, updates);
 * console.log(diff); // { beforeState: { id: 1, name: 'John', age: 25 }, fieldsChanged: ['name'] }
 */
export const computeUpdateDiff = (original, updates) => {
  const beforeState = original.toJSON ? original.toJSON() : { ...original };

  const fieldsChanged = Object.keys(updates).filter((key) => beforeState[key] !== updates[key]);

  return {
    beforeState,
    fieldsChanged,
  };
};

// ───── INITIALIZE METADATA ─────
/**
 * Sets audit trail metadata and description with translation keys and parameters.
 *
 * @param {Object} request - The request object containing user and entity information.
 * @param {Object} params - An object containing the module, event, and action parameters.
 * @param {string} [id=''] - The reference ID associated with the event.
 *
 * @returns {void}
 */
const initializeAuditMeta = async (request, { module, event, action }, id = '') => {
  if (!request.auditEntries) {
    request.auditEntries = {};
  }

  const username = request?.authInfo?.username || 'UNKNOWN_USER';
  const hierarchy = request?.entity?.hierarchy || 'UNKNOWN_HIERARCHY';
  const entityName = request?.entity?.name || 'UNKNOWN_ENTITY';

  const descriptionParams = {
    username,
    referenceIds: id,
    hierarchy,
    entityName,
  };

  // Set audit trail metadata and description with translation keys and parameters
  Object.assign(
    request.auditEntries,
    generateAuditMetadata(action, module, event, descriptionParams),
  );
};

/**
 * Generates audit trail metadata based on the provided parameters.
 *
 * @param {string} action - The action being performed, e.g., 'UPDATED', 'VIEWED_DETAILS'.
 * @param {string} module - The module associated with the action, e.g., 'USER', 'PRODUCT'.
 * @param {string} event - The event being logged, e.g., 'CREATE', 'UPDATE'.
 * @param {Object} descriptionParams - Additional parameters for constructing the description.
 *
 * @returns {Object} - An object containing the audit trail metadata.
 */
const generateAuditMetadata = (action, module, event, descriptionParams) => ({
  module,
  event: `common.label.${event}`,
  action: {
    translationKey: `auditTrail.action.${action}`,
    translationParams: { module: `common.label.${module}` },
  },
  description: {
    translationKey: `auditTrail.sentence.${action}`,
    translationParams: descriptionParams,
  },
});

// ───── TARGET PROCESSING ─────
/**
 * Constructs an audit trail entry based on the provided parameters.
 *
 * @function buildAuditTrailTarget
 * @param {Object} request - The request object containing audit trail entries.
 * @param {Object} options - An object containing the necessary parameters for constructing the audit trail entry.
 * @param {Object} options.eventInfo - An object containing information about the event.
 * @param {Array} options.targets - An array of target objects, each containing the model name, reference ID, and changes made.
 *
 * @returns {void}
 *
 * The function constructs an audit trail entry by populating the `request.auditEntries` object with the provided parameters.
 * It maps the `targets` array to the `request.auditEntries.target` property, sets the `statusCode` based on the `request` object,
 * and adds the `metrics` to the `request.auditEntries` object if they are provided.
 * Finally, it sets the `description` from the `eventInfo` object to the `request.auditEntries` object.
 */
const buildAuditTrailTarget = (request, { modelMapping, action, isMultiple, metrics, status }) => {
  const targets = processAuditTargets(modelMapping, action, isMultiple);

  const referenceIds = targets.map((t) => t.referenceId).join(', ');

  updateAuditMeta(request, referenceIds, status);

  request.auditEntries.target = Object.values(targets).map((target) => ({
    referenceId: target.referenceId,
    referenceDetails: target.referenceDetails,
    model: target.model,
    changes: target.changes,
  }));

  if (metrics && Object.keys(metrics).length > 0) {
    request.auditEntries.details.metrics = metrics;
  }
};

/**
 * Processes model mappings and returns audit log targets with change details.
 * Supports single and batch operations.
 *
 * @param {Object} modelMapping - Map of model names to before/after states and fieldsChanged config.
 * @param {string} action - The action name, e.g., 'UPDATED', 'VIEWED_DETAILS'
 * @param {boolean} isMultiple - True if the operation involves multiple records.
 * @returns {Array<Object>} Array of audit log targets with referenceId, label, and changes.
 */
const processAuditTargets = (modelMapping, action, isMultiple) => {
  const targets = [];
  let entries = [];

  for (const [model, data] of Object.entries(modelMapping)) {
    const comparison = getChanges(data, isMultiple);

    // Skip if there are no changes
    if (_.isEmpty(comparison.changes)) {
      continue;
    }

    const referenceDetails = data.referenceDetails;

    if (Object.values(VIEW_ACTION).some((viewAction) => action.includes(viewAction))) {
      entries = buildViewTargets(model, comparison, referenceDetails);
    } else if (!isMultiple) {
      entries = buildSingleChangeTarget(model, comparison.changes, comparison, referenceDetails);
    } else {
      // For multiple row changes, create separate target entries for each row changed
      entries = buildMultipleChangeTargets(model, comparison.changes, referenceDetails);
    }

    targets.push(...entries);
  }

  return targets;
};

/**
 * Retrieves the changes between the before and after states of a model.
 *
 * @function getChanges
 * @param {Object} data - An object containing the necessary parameters for change extraction.
 * @param {Array<string>} data.fieldsChanged - An array of fields that have changed.
 * @param {Object} data.beforeState - The state of the model before the change.
 * @param {Object} data.afterState - The state of the model after the change.
 * @param {boolean} isMultiple - Indicates whether the operation involves multiple records.
 *
 * @returns {Object} - An object containing the before and after changes.
 */
const getChanges = (data, isMultiple) => {
  const { fieldsChanged, beforeState, afterState } = data;

  const beforeVal = extractData(beforeState);
  const afterVal = extractData(afterState);

  const changes = getChangedFields(beforeVal, afterVal, fieldsChanged, isMultiple);

  return { beforeVal, afterVal, changes };
};

/**
 * Builds an array of target objects for view actions.
 * Each target object contains the model name, reference ID, and reference details (optional).
 *
 * @param {string} model - The name of the model associated with the changes.
 * @param {Object} data - An object containing the necessary parameters for change extraction.
 * @param {Object} referenceDetails - Additional details related to the reference.
 *
 * @returns {Array} - An array of target objects, each containing the model name, reference ID, and reference details.
 */
const buildViewTargets = (model, comparison, referenceDetails) => {
  const referenceId = getReferenceId(comparison.beforeVal, comparison.afterVal);

  return [
    {
      model,
      referenceId,
      referenceDetails,
    },
  ];
};

/**
 * Builds an array of target objects for single row change actions.
 * Each target object contains the model name, reference ID, reference details, and changes made.
 *
 * @param {string} model - The name of the model associated with the changes.
 * @param {Object} changes - An object containing the before and after changes.
 * @param {Object} data - An object containing the necessary parameters for change extraction.
 * @param {Object} referenceDetails - Additional details related to the reference.
 *
 * @returns {Array} - An array of target objects, each containing the model name, reference ID, reference details, and changes made.
 */
const buildSingleChangeTarget = (model, changes, comparison, referenceDetails) => {
  const referenceId = getReferenceId(comparison.beforeVal, comparison.afterVal);

  return [
    {
      model,
      referenceId,
      referenceDetails,
      changes: {
        beforeState: changes.beforeChanged,
        afterState: changes.afterChanged,
      },
    },
  ];
};

/**
 * Builds an array of target objects for multiple row change actions.
 * Each target object contains the model name, reference ID, reference details, and changes made.
 *
 * @function buildMultipleChangeTargets
 * @param {string} model - The name of the model associated with the changes.
 * @param {Object} changes - An object containing the before and after changes.
 * @param {Object} referenceDetailsMap - Additional details related to the reference.
 *
 * @returns {Array} - An array of target objects, each containing the model name, reference ID, reference details, and changes made.
 */
const buildMultipleChangeTargets = (model, changes, referenceDetailsMap) => {
  const targets = [];

  for (const field of Object.keys(changes.beforeChanged)) {
    const before = changes.beforeChanged[field];
    const after = changes.afterChanged[field];
    const referenceDetails = referenceDetailsMap?.[field] ?? null;
    const referenceId = before?.id || after?.id || 'UNKNOWN_ID';

    targets.push({
      model,
      referenceId,
      referenceDetails,
      changes: {
        beforeState: { [field]: before?.value },
        afterState: { [field]: after?.value },
      },
    });
  }

  return targets;
};

/**
 * Extracts relevant data from the provided object.
 *
 * @function extractData
 * @param {Object} data - The object from which to extract data.
 * @returns {Object} - The extracted data. If the input object has a 'dataValues' property, it is returned.
 *                      Otherwise, the input object itself is returned. If the input object is falsy, an empty object is returned.
 */
const extractData = (data) => {
  if (!data) {
    return {};
  }
  if (data?.dataValues) {
    return data.dataValues;
  }
  return data;
};

/**
 * Retrieves the fields that have changed between the before and after states of a model.
 *
 * @function getChangedFields
 * @param {Object} before - The state of the model before the change.
 * @param {Object} after - The state of the model after the change.
 * @param {Array<string>} fieldsChanged - An array of fields that have changed.
 * @param {boolean} isMultiple - Indicates whether the operation involves multiple records.
 *
 * @returns {Object|undefined} - An object containing the before and after changes, or undefined if no changes are detected.
 * The returned object has the following structure:
 * {
 *   beforeChanged: { [fieldName: string]: any },
 *   afterChanged: { [fieldName: string]: any }
 * }
 */
const getChangedFields = (before, after, fieldsChanged, isMultiple) => {
  const beforeChanged = {};
  const afterChanged = {};
  const changedFields = determineChangedFields(before, after, fieldsChanged);

  if (!changedFields) {
    return;
  }

  for (const key of changedFields) {
    if (!isMultiple) {
      beforeChanged[key] = before?.[key];
      afterChanged[key] = after?.[key];
    } else {
      processMultipleChanges(before, after, key, beforeChanged, afterChanged);
    }
  }

  return { beforeChanged, afterChanged };
};

/**
 * Determines the fields that have changed between the before and after states of a model.
 *
 * @function determineChangedFields
 * @param {Object} before - The state of the model before the change.
 * @param {Object} after - The state of the model after the change.
 * @param {Array<string>|boolean} fieldsChanged - An array of fields that have changed or false if no changes.
 *
 * @returns {Array<string>} - An array of field names that have changed.
 *
 * If `fieldsChanged` is false, an empty array is returned.
 */
const determineChangedFields = (before, after, fieldsChanged) => {
  if (fieldsChanged === false) {
    // No changes
    return [];
  } else if (fieldsChanged && Array.isArray(fieldsChanged)) {
    // UPDATE action
    return fieldsChanged;
  } else if (!after || Object.keys(after).length === 0) {
    // DELETE action – derive keys from beforeState
    return Object.keys(before);
  } else {
    // CREATE action – derive keys from afterState
    return Object.keys(after);
  }
};

/**
 * Processes multiple changes between the before and after states of a model.
 * It extracts the relevant fields, compares their values, and constructs the before and after change objects.
 *
 * @function processMultipleChanges
 * @param {Object} before - The state of the model before the change.
 * @param {Object} after - The state of the model after the change.
 * @param {string} key - The field name for which changes are being processed.
 * @param {Object} beforeChanged - An object to store the before changes.
 * @param {Object} afterChanged - An object to store the after changes.
 *
 * @returns {void}
 *
 * The function iterates through the fields of the before and after states, compares their values,
 * and constructs the before and after change objects. If a field value has changed,
 * it adds the field name, id, and value to the respective before and after change objects.
 */
const processMultipleChanges = (before, after, key, beforeChanged, afterChanged) => {
  const beforeField = before?.[key];
  const afterValue = after?.[key];
  const beforeValue = beforeField?.value ?? beforeField;

  if (beforeValue !== afterValue) {
    beforeChanged[key] = {
      id: beforeField?.id,
      value: beforeValue,
    };
    afterChanged[key] = {
      id: beforeField?.id,
      value: afterValue,
    };
  }
};

// Gets a single referenceId from before/after values
const getReferenceId = (beforeVal, afterVal) => {
  return beforeVal?.id || afterVal?.id || beforeVal?.[0]?._id || afterVal?.[0]?._id || 'UNKNOWN_ID';
};

// ───── UPDATE & FINALIZE ─────
/**
 * Updates the audit trail metadata with reference IDs and status.
 *
 * @param {Object} request - The request object containing audit entries.
 * @param {string} referenceIds - The reference IDs associated with the event.
 * @param {string} status - The status of the event.
 *
 * @returns {void}
 */
const updateAuditMeta = async (request, referenceIds, status) => {
  request.auditEntries.description.translationParams =
    request.auditEntries.description.translationParams || {};

  Object.assign(request.auditEntries.description.translationParams, {
    referenceIds,
    status,
  });
};

/**
 * Function to finalizing the audit trail entry after an API request has been processed.
 * This function populates the request details, redacts sensitive fields, and attempts to send the audit trail to Kafka.
 * If an error occurs while sending the audit trail to Kafka, the function stores the audit trail in Redis instead.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {IncomingMessage} request - The incoming HTTP request
 * @param {ServerResponse} reply - The HTTP response
 *
 * @returns {void}
 */
const finalizeAuditTrailEntry = async (fastify, request, reply) => {
  if (!request.auditEntries) {
    return;
  }
  request.auditEntries.details = request.auditEntries.details ?? {};
  request.auditEntries.details.request = request.auditEntries.details.request ?? {};
  request.auditEntries.details.error = request.auditEntries.details.error ?? {};

  // Populate request details
  request.auditEntries.status = 'Success';
  request.auditEntries.details.request.statusCode = reply.statusCode;

  if (reply.statusCode >= 400) {
    request.auditEntries.status = 'Failed';
    request.auditEntries.details.error.message = request.responsePayload?.message;
    request.auditEntries.details.error.errorCode = request.responsePayload?.errorCode;
    request.auditEntries.details.error.meta = request.responsePayload?.meta;
  }

  // Redact sensitive fields
  const redactedAuditEntry = deepRedact(request.auditEntries);

  const message = [
    {
      key: request.authInfo?.id,
      value: JSON.stringify(redactedAuditEntry),
    },
  ];

  // Send audit trail to Kafka
  if (fastify.config.KAFKA) {
    try {
      await fastify.kafka.producer.send({
        topic: 'audit-trails',
        messages: message,
      });
    } catch (error) {
      // If an error occurs while sending the audit trail to Kafka, store the audit trail in Redis
      fastify.log.error(error, 'Kafka send failed');
      const cacheKey = `failed-kafka-message:${request.entityAccessId}:${request.authInfo?.id}:${Date.now()}`;
      setCache(fastify.redis, cacheKey, message);
      fastify.log.debug(message, 'Kafka message stored in Redis');
    }
  }
};

export {
  buildAuditTrailTarget,
  finalizeAuditTrailEntry,
  formatEntityInfo,
  initializeAuditMeta,
  prepareAuditTrailEntry,
  updateAuditMeta,
};

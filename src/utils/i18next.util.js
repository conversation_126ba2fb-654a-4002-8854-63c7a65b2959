import i18next from 'i18next';

export const translateMessage = (
  message,
  interpolationParams = {},
  type = 'message',
  i18nextInstance = i18next,
) => {
  if (typeof message === 'string') {
    const params = interpolationParams.message || interpolationParams;
    const translation = i18nextInstance.t(message, params);
    return translation !== message ? translation : message;
  }

  if (Array.isArray(message)) {
    return message.map((item, index) => {
      if (type === 'dropdown') {
        const dataToTranslate = item.dataValues || item;
        if ('name' in dataToTranslate) {
          return {
            ...dataToTranslate,
            name: i18nextInstance.t(dataToTranslate.name, { ns: 'string' }),
          };
        }
      }

      return translateMessage(item, interpolationParams.details[index], type, i18nextInstance);
    });
  }

  return message;
};

export const translateValidationErrors = (validationErrors, i18nextInstance = i18next) => {
  return validationErrors.map(({ instancePath, message: validationMessage, params }) => {
    const attribute = instancePath?.slice(1) || params.missingProperty;
    const translationParams = { ...params, attribute };

    Object.keys(translationParams).forEach((key) => {
      translationParams[key] = i18nextInstance.t(translationParams[key], { ns: 'validation' });
    });

    const formattedMessage = Object.entries(params).reduce((msg, [key, value]) => {
      const regex = new RegExp(`\\b${value}\\b`, 'g');
      return msg.replace(regex, `{{${key}}}`);
    }, validationMessage);

    const messageTemplate = instancePath?.slice(1)
      ? `{{attribute}} ${formattedMessage}`
      : formattedMessage;

    const translatedMessage = i18nextInstance.t(messageTemplate, {
      ...translationParams,
      ns: 'validation',
    });

    return { [attribute]: translatedMessage };
  });
};

import { access, readdir } from 'node:fs/promises';
import { extname, join } from 'node:path';
import { pathToFileURL } from 'node:url';

import { Sequelize } from 'sequelize';
import { getModelPath } from '#src/utils/file.util.js';

/**
 * MODEL_TYPES Configuration
 *
 * This object defines the configuration for different database types.
 * When adding a new database connection, you need to add a new entry here.
 *
 * Each database type should have the following keys:
 * - decoratorName: The name used to decorate the Fastify instance (e.g., fastify.mongo, fastify.psql)
 * - getModelName: A function that extracts the model name from the model instance
 * - isValidModel: A function that validates if the loaded module is a valid model for this database type
 *
 * To add a new database type:
 * 1. Create a new key in MODEL_TYPES (e.g., 'mysql', 'redis', etc.)
 * 2. Define the decoratorName, getModelName, and isValidModel functions for the new type
 * 3. Ensure that the corresponding plugin and model loading logic is implemented elsewhere in the application
 */
export const MODEL_TYPES = {
  mongo: {
    decoratorName: 'mongo',
    getModelName: (ModelInstance) => ModelInstance.modelName,
    isValidModel: (ModelInstance) => Boolean(ModelInstance.modelName),
  },
  postgres: {
    decoratorName: 'psql',
    getModelName: (ModelInstance) => ModelInstance.name,
    isValidModel: (ModelInstance) => ModelInstance.prototype instanceof Sequelize.Model,
  },
};

/**
 * Automatically loads models for a specified database type across all modules.
 *
 * @async
 * @function autoloadModels
 * @param {Object} fastify - The Fastify instance to which models will be attached.
 * @param {string} type - The database type (e.g., 'mongo', 'postgres') for which to load models.
 * @param {Object} [dbInstance=null] - The database instance, required for SQL databases.
 * @returns {Promise<void>} A promise that resolves when all models are loaded or rejects if an error occurs.
 * @throws {Error} If there's an error accessing or reading the models directory (except for ENOENT).
 *
 */
export const autoloadModels = async (fastify, type, dbInstance = null) => {
  const modelConfig = MODEL_TYPES[type];
  if (!modelConfig) {
    fastify.log.error(`Unsupported model type: ${type}`);
    return;
  }

  const modelDir = join(getModelPath(), type);
  try {
    await access(modelDir);

    await loadModels(fastify, modelDir, dbInstance, modelConfig);
  } catch (error) {
    if (error.code !== 'ENOENT') {
      fastify.log.error(error, 'Error autoloading models');
    }
  }
};

/**
 * Loads all model files from a specified directory and attaches them to the Fastify instance.
 *
 * @async
 * @function loadModels
 * @param {Object} fastify - The Fastify instance to which models will be attached.
 * @param {string} modelDir - The directory path containing model files to load.
 * @param {Object|null} dbInstance - The database instance, if applicable (for SQL databases).
 * @param {Object} modelConfig - Configuration object for the specific model type.
 * @returns {Promise<void>} A promise that resolves when all models in the directory are loaded.
 * @throws {Error} If there's an error reading the directory or loading any model file.
 *
 */
export const loadModels = async (fastify, modelDir, dbInstance, modelConfig) => {
  try {
    const files = await readdir(modelDir);
    await Promise.all(
      files.map((file) => loadModelFile(fastify, modelDir, file, dbInstance, modelConfig)),
    );
  } catch (error) {
    fastify.log.error(error, `Error accessing model directory`);
  }
};

/**
 * Dynamically imports a module from a URL.
 *
 * @function dynamicImport
 * @param {string} url - The URL or file path to import. For file paths, this should be
 *                       converted to a proper URL using pathToFileURL() before passing.
 * @returns {Promise<Object>} A promise that resolves to the imported module.
 *
 */
export const dynamicImport = (url) => import(url);

/**
 * Loads a single model file and attaches it to the Fastify instance.
 *
 * @async
 * @function loadModelFile
 * @param {Object} fastify - The Fastify instance to which the model will be attached.
 * @param {string} modelDir - The directory path where the model file is located.
 * @param {string} file - The name of the model file to be loaded.
 * @param {Object|null} dbInstance - The database instance, if applicable (for SQL databases).
 * @param {Object} modelConfig - Configuration object for the specific model type.
 * @returns {Promise<void>} A promise that resolves when the model is successfully loaded and attached.
 *
 */
export const loadModelFile = async (
  fastify,
  modelDir,
  file,
  dbInstance,
  modelConfig,
  dynamicImportFn = dynamicImport,
) => {
  if (extname(file) !== '.js') {
    return;
  }

  const modelPath = join(modelDir, file);
  const modelUrl = pathToFileURL(modelPath).href;

  try {
    const model = await dynamicImportFn(modelUrl);
    if (!model.default || typeof model.default !== 'function') {
      return;
    }

    const ModelInstance = model.default(fastify, dbInstance);
    if (!modelConfig.isValidModel(ModelInstance)) {
      return;
    }

    const modelName = modelConfig.getModelName(ModelInstance);
    fastify[modelConfig.decoratorName][modelName] = ModelInstance;

    fastify.log.info(`Loaded model: ${modelName}`);
  } catch (error) {
    fastify.log.error(error, `Error loading model ${file}`);
  }
};

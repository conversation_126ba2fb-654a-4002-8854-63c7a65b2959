/**
 * Transforms an array of objects into an object where each key is a unique
 * property of each object and the value is the corresponding object.
 *
 * @param {any[]} arr Array of objects
 * @param {string} [key='id'] Property to be used as key
 * @returns {Record<string, any>} An object with the specified keys and values
 */
const objectArray = (arr: any[], key = 'id'): Record<string, any> =>
  arr.reduce((accumulator, current) => {
    const updatedAccumulator = { ...accumulator };
    updatedAccumulator[current[key]] = current;
    return updatedAccumulator;
  }, {});

export default objectArray;

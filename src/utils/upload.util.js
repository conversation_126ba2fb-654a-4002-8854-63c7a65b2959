import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import crypto from 'crypto';

const S3_CLIENT_CACHE = new Map(); // Cache S3 clients per Fastify instance
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5 MB
const ALLOWED_MIME_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];

/**
 * Retrieves or creates an S3 client for the given Fastify instance.
 * @param {FastifyInstance} fastify - The Fastify instance.
 * @returns {S3Client} The S3 client.
 */
async function createS3Client(fastify) {
  if (!S3_CLIENT_CACHE.has(fastify)) {
    const s3Client = new S3Client({
      region: fastify.config.AWS_REGION,
      credentials: {
        accessKeyId: fastify.config.AWS_ACCESS_KEY_ID,
        secretAccessKey: fastify.config.AWS_SECRET_ACCESS_KEY,
      },
    });
    S3_CLIENT_CACHE.set(fastify, s3Client);
  }
  return S3_CLIENT_CACHE.get(fastify);
}

/**
 * Validates file properties before upload.
 * @param {Buffer} fileContent - The file data as a buffer.
 * @param {string} mimeType - The MIME type of the file.
 */
function validateFile(fileContent, mimeType) {
  if (fileContent.length > MAX_FILE_SIZE) {
    throw new Error(`File size exceeds ${MAX_FILE_SIZE / (1024 * 1024)} MB limit`);
  }

  if (!ALLOWED_MIME_TYPES.includes(mimeType)) {
    throw new Error('Invalid file type. Allowed types are JPEG, JPG, PNG, and PDF.');
  }
}

/**
 * Generates a unique filename to avoid collisions in S3.
 * @param {string} originalFilename - The original filename of the uploaded file.
 * @returns {string} The unique filename.
 */
function generateUniqueFilename(originalFilename) {
  const timestamp = Date.now().toString(36);
  const randomString = crypto.randomBytes(8).toString('hex');
  const extension = originalFilename.split('.').pop();
  return `${timestamp}-${randomString}.${extension}`;
}

/**
 * Uploads a validated file to AWS S3.
 * @param {Buffer} fileContent - The file data as a buffer.
 * @param {string} originalFilename - The original filename of the uploaded file.
 * @param {string} mimeType - The MIME type of the file.
 * @param {FastifyInstance} fastify - The Fastify instance containing AWS configuration.
 * @returns {Promise<string>} The URL of the uploaded file.
 */
export default async function uploadToS3(fileContent, originalFilename, mimeType, fastify) {
  const fileName = generateUniqueFilename(originalFilename);

  const params = {
    Bucket: fastify.config.AWS_BUCKET,
    Key: fileName,
    Body: fileContent,
    ContentType: mimeType,
    ACL: 'public-read',
  };

  try {
    validateFile(fileContent, mimeType);
    const s3 = await createS3Client(fastify);
    const command = new PutObjectCommand(params);
    await s3.send(command);
    const fileUrl = `${fastify.config.AWS_S3_PATH}/${fileName}`;
    return fileUrl;
  } catch (error) {
    throw new Error(`Failed to upload file: ${error.message}`);
  }
}

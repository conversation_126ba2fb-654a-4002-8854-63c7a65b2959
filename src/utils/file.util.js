import { dirname as pathDirName, join as pathJoin } from 'node:path';
import { fileURLToPath } from 'node:url';
import { readFileSync } from 'node:fs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = pathDirName(__filename);

export const getPackageJson = () => {
  return JSON.parse(readFileSync(pathJoin(__dirname, '../../', 'package.json'), 'utf8'));
};

export const getLogFilePath = () => {
  return pathJoin(__dirname, '../../logs', process.env.APP_NAME);
};

export const getPluginsPath = () => {
  return pathJoin(__dirname, '../plugins');
};

export const getModulesPath = () => {
  return pathJoin(__dirname, '../modules');
};

export const getHooksPath = () => {
  return pathJoin(__dirname, '../hooks');
};

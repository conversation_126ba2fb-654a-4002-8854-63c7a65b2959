/**
 * Converts a route string to a regular expression that can be used to match
 * the route.
 *
 * The regular expression is generated by:
 * - Escaping special characters in the route
 * - Replacing dynamic segments (i.e. `[:segment]`) with a regex pattern that
 *   matches one or more non-slash characters
 * - Wrapping the resulting pattern in `^$` to ensure a full match
 *
 * @param route The route string to convert
 * @returns The regular expression to match the route
 */
export const convertRouteToRegex = (route: string): RegExp => {
  const escapedRoute = route.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&'); // Escape special characters
  const dynamicRoutePattern = escapedRoute.replace(/\\\[[^\]]*\\\]/g, '[^/]+'); // Replace dynamic segments
  return new RegExp(`^${dynamicRoutePattern}$`);
};

/**
 * Checks if a given route is active by comparing it to the current pathname.
 *
 * A route is considered active if:
 * - The route matches the pathname exactly
 * - The route is a parent of the current pathname (i.e. pathname starts with
 *   the route followed by a '/')
 * - The route is a regex match for the pathname (after converting dynamic
 *   segments to regex patterns)
 *
 * @param route The route to check, or undefined if the route is not applicable
 * @param pathname The current pathname to check against
 * @returns Whether the route is active
 */
export const isRouteActive = (route: string | undefined, pathname: string): boolean => {
  if (!route) return false;
  return (
    pathname === route ||
    pathname.startsWith(route + '/') ||
    convertRouteToRegex(route).test(pathname)
  );
};

// eslint-disable-next-line consistent-return
/**
 * Creates a deep copy of the provided object, array, or date.
 *
 * @param obj - The object, array, or date to be copied. If the input is not an object, array, or date, it is returned as is.
 * @returns A deep copy of the input object, array, or date. If the input is not an object, array, or date, the input itself is returned.
 */
export function copy(obj: any): any {
  if (typeof obj !== 'object' || obj === null) {
    return obj;
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime());
  }

  if (Array.isArray(obj)) {
    return obj.reduce((arr: any[], item) => {
      return [...arr, copy(item)]; // Return a new array with copied items
    }, []);
  }

  if (obj !== null && typeof obj === 'object') {
    return Object.keys(obj).reduce((newObj, key) => {
      return { ...newObj, [key]: copy(obj[key]) }; // Return a new object each time
    }, {});
  }
}

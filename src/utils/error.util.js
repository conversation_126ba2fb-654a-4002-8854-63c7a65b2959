import createError from '@fastify/error';

/**
 * Utility function to create an extended error class with additional details.
 * @param {string} code - The error code.
 * @param {string} message - The error message template.
 * @param {number} statusCode - The HTTP status code.
 * @returns {Function} A constructor function for the custom error.
 */
function createCustomError(code, message, moduleName, statusCode = 500) {
  const BaseError = createError(code, message, statusCode);

  return class CustomError extends BaseError {
    /**
     * Constructs a new CustomError with additional details.
     * @param {...any} args - Arguments for the error message template.
     * @param {Object} [metaData] - Additional details about the error.
     */
    constructor(...args) {
      let extraDetails = {};
      if (args.length > 0 && typeof args[args.length - 1] === 'object') {
        extraDetails = args.pop();
      }
      super(...args);
      this.name = `${moduleName}ModuleError`;
      this.metaData = extraDetails;
    }
  };
}

export { createCustomError };

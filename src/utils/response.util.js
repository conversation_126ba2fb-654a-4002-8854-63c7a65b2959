import { translateDropdownItem, translateMessage } from '#src/utils/i18next.util.js';

/**
 * Formats a successful API response.
 *
 * @param {any} data - The data to be included in the response.
 * @param {string} message - A message to be included in the response.
 * @returns {Object} The formatted success response object.
 */
export const formatSuccessResponse = (
  message = '',
  data = [],
  meta = {},
  interpolationParams = {},
) => {
  return {
    message: translateMessage(message, interpolationParams),
    data,
    meta: {
      ...meta,
      ...(meta.details && {
        details: translateMessage(meta.details, interpolationParams),
      }),
    },
  };
};

/**
 * Formats an error API response.
 *
 * @param {string} message - An error message to be included in the response.
 * @param {string} errorCode - Error code for the response.
 * @param {Object} meta - Additional metadata for the error response.
 * @returns {Object} The formatted error response object.
 */
export const formatErrorResponse = (
  message = '',
  errorCode = '',
  meta = {},
  interpolationParams = {},
) => {
  return {
    message: translateMessage(message, interpolationParams),
    errorCode,
    meta,
  };
};

/**
 * Formats an dropdown API response.
 *
 * @param {string} message - An error message to be included in the response.
 * @param {any} data - The data to be included in the response.
 * @param {Object} meta - Additional metadata for the error response.
 * @returns {Object} The formatted error response object.
 */
export const formatDropdownResponse = (
  message = '',
  data = [],
  meta = {},
  interpolationParams = {},
) => {
  return {
    message: translateMessage(message, interpolationParams),
    data: translateDropdownItem(data),
    meta: {
      ...meta,
      ...(meta.details && {
        details: translateMessage(meta.details, interpolationParams),
      }),
    },
  };
};

/**
 * Handles the response from a service function and sends a success reply.
 *
 * @param {Object} params - The parameters for handling the service response.
 * @param {Object} params.request - The request object to be passed to the service function.
 * @param {Object} params.reply - The reply object used to send the response.
 * @param {Function} params.serviceFn - The service function to be executed with the request.
 * @param {string} params.module - The name of the module for logging or tracking purposes.
 * @param {string} params.method - The name of the method for logging or tracking purposes.
 * @returns {Promise<Object>} The result of the reply's success method, indicating a successful response.
 */
export const handleServiceResponse = async ({ request, reply, serviceFn, module, method }) => {
  const result = await serviceFn(request);

  // Check if it's an index/listing request
  if (method.toLowerCase() === 'index' && result?.rows && result?.pagination) {
    return reply.success(module, method, result.rows, { pagination: result.pagination });
  }

  return reply.success(module, method, result);
};

import { translateMessage } from '#src/utils/i18next.util.js';

/**
 * Formats a successful API response.
 *
 * @param {any} data - The data to be included in the response.
 * @param {string} message - A message to be included in the response.
 * @returns {Object} The formatted success response object.
 */
export const formatSuccessResponse = (
  message = '',
  data = [],
  meta = {},
  interpolationParams = {},
) => {
  return {
    message: translateMessage(message, interpolationParams),
    data,
    meta: {
      ...meta,
      ...(meta.details && {
        details: translateMessage(meta.details, interpolationParams),
      }),
    },
  };
};

/**
 * Formats an error API response.
 *
 * @param {string} message - An error message to be included in the response.
 * @param {string} errorCode - Error code for the response.
 * @param {Object} meta - Additional metadata for the error response.
 * @returns {Object} The formatted error response object.
 */
export const formatErrorResponse = (
  message = '',
  errorCode = '',
  meta = {},
  interpolationParams = {},
) => {
  return {
    message: translateMessage(message, interpolationParams),
    errorCode,
    meta,
  };
};

/**
 * Formats an dropdown API response.
 *
 * @param {string} message - An error message to be included in the response.
 * @param {any} data - The data to be included in the response.
 * @param {Object} meta - Additional metadata for the error response.
 * @returns {Object} The formatted error response object.
 */
export const formatDropdownResponse = (
  message = '',
  data = [],
  meta = {},
  interpolationParams = {},
) => {
  return {
    message: translateMessage(message, interpolationParams),
    data: translateMessage(data, {}, 'dropdown'),
    meta: {
      ...meta,
      ...(meta.details && {
        details: translateMessage(meta.details, interpolationParams),
      }),
    },
  };
};

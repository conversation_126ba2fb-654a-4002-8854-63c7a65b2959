import type { Customization } from '@/contexts/customization';
import { cookies } from 'next/headers';

const CUSTOMIZATION_STORAGE_KEY = 'uifort.customization';

export const restoreCustomization = async (): Promise<Customization | undefined> => {
  const cookieList = await cookies();

  if (cookieList.has(CUSTOMIZATION_STORAGE_KEY)) {
    try {
      const restored = cookieList.get(CUSTOMIZATION_STORAGE_KEY);

      if (restored) {
        return JSON.parse(restored.value) as Customization;
      }
    } catch (err) {
      console.error('Failed to restore customization', err);
    }
  }

  return undefined;
};

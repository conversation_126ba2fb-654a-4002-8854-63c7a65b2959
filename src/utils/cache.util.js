/**
 * Fetches data from the cache using the provided key. If the data is not found, it calls the provided callback function to fetch the data,
 * stores it in the cache with the specified expiration time, and returns the fetched data.
 *
 * @param {RedisClient} redis - The Redis client instance.
 * @param {string} key - The key to use for fetching and storing data in the cache.
 * @param {function} callback - The callback function to fetch the data if it's not found in the cache.
 * @param {number} [expiration=86400] - The expiration time for the cached data in seconds. Default is 86400 seconds (1 day).
 * @returns {Promise<any>} Resolves with the fetched or cached data.
 */
export const fetchFromCache = async (redis, key, callback, expiration = 86400) => {
  // Validate the key
  if (!key || typeof key !== 'string' || key.trim() === '') {
    throw new Error('Cache key must be a non-empty string');
  }

  const cachedData = await redis.get(key);
  if (cachedData) {
    return JSON.parse(cachedData);
  }
  const newCacheData = await callback();
  await redis.setex(key, expiration, JSON.stringify(newCacheData));
  return newCacheData;
};

/**
 * Retrieve data from the cache by key
 *
 * @param {RedisClient} redis - The Redis client instance.
 * @param {string} key - The key to remove from the cache.
 * @returns {Promise<any>} Resolves with the cached data or null if not found.
 */
export const getCache = async (redis, key) => {
  // Validate that key is not empty
  if (!key || typeof key !== 'string' || key.trim() === '') {
    throw new Error('Cache key must be a non-empty string');
  }

  const cachedData = await redis.get(key);
  return cachedData ? JSON.parse(cachedData) : null;
};

/**
 * Sets data in the cache with an expiration time.
 *
 * @param {RedisClient} redis - The Redis client instance.
 * @param {string} key - The key to store in the cache.
 * @param {any} value - The value to store in the cache. It will be stringified before storing.
 * @param {number} [expiration=86400] - The expiration time for the key in seconds. Default is 86400 seconds (1 day).
 * @returns {Promise<void>} Resolves when the data is set successfully in the cache.
 */
export const setCache = async (redis, key, value, expiration = 86400) => {
  await redis.setex(key, expiration, JSON.stringify(value));
};

/**
 * Removes a specific key from the Redis cache.
 *
 * @param {RedisClient} redis - The Redis client instance.
 * @param {string} key - The key to remove from the cache.
 * @returns {Promise<void>} Resolves when the key is removed successfully from the cache.
 */
export const clearCache = async (redis, key) => {
  await redis.del(key);
};

/**
 * Clears all keys in the Redis cache with a specific prefix.
 *
 * @param {RedisClient} redis - The Redis client instance.
 * @param {string} prefix - The prefix to match keys against.
 * @returns {Promise<void>} Resolves when the cache is cleared successfully.
 */
export const clearCacheWithPrefix = async (redis, prefix) => {
  const keys = await redis.keys(`${prefix}*`);
  if (keys.length > 0) {
    await redis.del(keys);
  }
};

/**
 * Clears all keys in the Redis cache.
 *
 * @param {RedisClient} redis - The Redis client instance.
 * @returns {Promise<void>} Resolves when the cache is cleared successfully.
 */
export const clearAllCache = async (redis) => {
  await redis.flushall();
};

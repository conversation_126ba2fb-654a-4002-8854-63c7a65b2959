/**
 * Decodes a JWT token using the provided Fastify instance.
 *
 * @param {string} token - The JWT token to decode.
 * @param {FastifyInstance} fastify - The Fastify instance for logging.
 * @returns {Promise<object|null>} - The decoded payload of the JWT token, or null if invalid or missing.
 */
export const decodeJWT = async (token, fastify) => {
  if (!token) {
    fastify.log.debug('No JWT token provided');
    return null;
  }

  try {
    return await fastify.jwt.verify(token);
  } catch (err) {
    fastify.log.debug(err, 'Failed to verify JWT token');
    return null;
  }
};

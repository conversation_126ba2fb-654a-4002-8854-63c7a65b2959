import type { Customization } from '@/contexts/customization';
import Cookies from 'js-cookie';

const CUSTOMIZATION_STORAGE_KEY = 'uifort.customization';

export const updateCustomization = (customization: Customization): void => {
  try {
    Cookies.set(CUSTOMIZATION_STORAGE_KEY, JSON.stringify(customization));
    window.location.reload();
  } catch (err) {
    console.error(err);
  }
};

export const resetCustomization = (): void => {
  try {
    Cookies.remove(CUSTOMIZATION_STORAGE_KEY);
    window.location.reload();
  } catch (err) {
    console.error(err);
  }
};

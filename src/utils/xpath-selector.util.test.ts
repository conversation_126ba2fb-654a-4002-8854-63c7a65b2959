import { afterEach, beforeEach, describe, expect, it } from 'vitest';
import { getAllByXPath, getByXPath } from './xpath-selector.util';
import '@testing-library/jest-dom';

describe('XPath selector util', () => {
  let container: HTMLDivElement;

  // Set up test DOM before each test
  beforeEach(() => {
    container = document.createElement('div');
    container.innerHTML = `
      <div id="parent">
        <p class="paragraph">First paragraph</p>
        <p class="paragraph">Second paragraph</p>
      </div>
    `;
    document.body.appendChild(container);
  });

  // Clean up after each test
  afterEach(() => {
    document.body.removeChild(container);
  });

  describe('getByXPath', () => {
    it('should return the element when exactly one element matches the XPath', () => {
      const element = getByXPath('//div[@id="parent"]');

      expect(element).toBeDefined();
    });

    it('should throw an error when no element matches the XPath', () => {
      expect(() => getByXPath('//div[@id="non-existent"]')).toThrow(
        'Unable to find an element by xpath: //div[@id="non-existent"]'
      );
    });

    it('should throw an error when multiple elements match the XPath', () => {
      expect(() => getByXPath('//p[@class="paragraph"]')).toThrow(
        'Unable to find an element by xpath: //p[@class="paragraph"]'
      );
    });
  });

  describe('getAllByXPath', () => {
    it('should return all elements that match the XPath', () => {
      const elements = getAllByXPath('//p[@class="paragraph"]');

      expect(elements).toHaveLength(2);
    });

    it('should return an empty array when no elements match the XPath', () => {
      const elements = getAllByXPath('//div[@id="non-existent"]');

      expect(elements).toHaveLength(0);
    });
  });
});

import { describe, vi } from 'vitest';
import { getSecureRandomIndex, getSecureRandomInt } from './random.util';

vi.mock('crypto', () => ({
  getRandomValues: (array: Uint32Array) => {
    const newArray = new Uint32Array(array.length);
    newArray[0] = 1234567890; // Example fixed value for testing purposes
    return newArray;
  },
}));

describe('getSecureRandomIndex', () => {
  it('should throw an error when length is zero for getSecureRandomIndex', () => {
    expect(() => getSecureRandomIndex(0)).toThrow('Invalid length for random index');
  });

  it('should return a number between 0 and length-1 for a positive length in getSecureRandomIndex', () => {
    const length = 10;
    const index = getSecureRandomIndex(length);
    expect(index).toBeGreaterThanOrEqual(0);
    expect(index).toBeLessThan(length);
  });

  it('should return 0 when length is 1 for getSecureRandomIndex', () => {
    const index = getSecureRandomIndex(1);
    expect(index).toBe(0);
  });

  it('should handle large values of length without errors in getSecureRandomIndex', () => {
    const largeLength = 1_000_000;
    const index = getSecureRandomIndex(largeLength);
    expect(index).toBeGreaterThanOrEqual(0);
    expect(index).toBeLessThan(largeLength);
  });

  it('should return different values on subsequent calls for getSecureRandomIndex with the same length', () => {
    const length = 10;
    const index1 = getSecureRandomIndex(length);
    const index2 = getSecureRandomIndex(length);
    expect(index1).not.toBe(index2);
  });
});
describe('getSecureRandomInt', () => {
  it('should return a number between min and max inclusive for getSecureRandomInt', () => {
    const min = 5;
    const max = 15;
    const randomInt = getSecureRandomInt(min, max);
    expect(randomInt).toBeGreaterThanOrEqual(min);
    expect(randomInt).toBeLessThanOrEqual(max);
  });

  it('should return min when min equals max for getSecureRandomInt', () => {
    const min = 10;
    const max = 10;
    const randomInt = getSecureRandomInt(min, max);
    expect(randomInt).toBe(min);
  });

  it('should handle large values of min and max without errors in getSecureRandomInt', () => {
    const min = 1_000_000;
    const max = 2_000_000;
    const randomInt = getSecureRandomInt(min, max);
    expect(randomInt).toBeGreaterThanOrEqual(min);
    expect(randomInt).toBeLessThanOrEqual(max);
  });

  it('should return different values on subsequent calls for getSecureRandomInt with the same min and max', () => {
    const min = 5;
    const max = 15;
    const randomInt1 = getSecureRandomInt(min, max);
    const randomInt2 = getSecureRandomInt(min, max);
    expect(randomInt1).not.toBe(randomInt2);
  });
});

/**
 * Finds the first element that matches the specified XPath expression.
 * Throws an error if no element or more than one element is found, similar to Testing Library's getBy* queries.
 * https://testing-library.com/docs/queries/about/
 *
 * @param xpath - The XPath expression to evaluate
 * @param container - The context node for the XPath expression (defaults to document)
 * @returns The first matching element
 * @throws Error if no element matches the XPath expression
 *
 * @example
 * const element = getByXPath('//div[@id="my-element-id"]');
 * const element = getByXPath('//tbody/tr[contains(@id, "my-element-id")]');
 * const element = getByXPath('//span[contains(text(), "Hello World")]');
 *
 * @example Multiple conditions
 * const element = getByXPath('//div[@role="tooltip" and @data-popper-placement="bottom-end"]');
 *
 */
export const getByXPath = (xpath: string, container = document): HTMLElement => {
  const elements = getAllByXPath(xpath, container);

  if (elements.length !== 1 || !elements[0]) {
    throw new Error(`Unable to find an element by xpath: ${xpath}`);
  }

  return elements[0];
};

/**
 * Finds all elements that match the specified XPath expression.
 * https://testing-library.com/docs/queries/about/
 *
 * @param xpath - The XPath expression to evaluate
 * @param container - The context node for the XPath expression (defaults to document)
 * @returns An array of matching elements or an empty array if no matches are found
 *
 * @example
 * const paragraphs = getAllByXPath('//p');
 * const buttons = getAllByXPath('//button[contains(@class, "primary")]');
 *
 * @example With container
 * const form = document.getElementById('my-form');
 * const inputs = getAllByXPath('.//input[@type="text"]', form);
 */
export const getAllByXPath = (xpath: string, container = document): HTMLElement[] => {
  const result = document.evaluate(
    xpath,
    container,
    null,
    XPathResult.ORDERED_NODE_SNAPSHOT_TYPE,
    null
  );
  const elements: HTMLElement[] = [];
  for (let i = 0; i < result.snapshotLength; i++) {
    elements.push(result.snapshotItem(i) as HTMLElement);
  }
  return elements;
};

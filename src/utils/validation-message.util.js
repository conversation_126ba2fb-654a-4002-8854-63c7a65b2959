/**
 * Create validation message helper
 *
 * @param {Object} i18next - The i18next translation instance
 * @param {string} key - The validation key (e.g., 'required', 'enum')
 * @param {string} attribute - The name of the attribute being validated
 * @param {Object} [extra={}] - Additional parameters to include in the translation
 * @returns {string} The localized validation message
 *
 */
const createAttributeMessage = (i18next, key, attribute, extra = {}) =>
  i18next.t(`error.validation.sentence.${key}`, { attribute, ...extra });

/**
 * Exports internal utility functions for unit testing purposes.
 */
export const exportForUnitTest = { createAttributeMessage };

/**
 * Generates required field validation message
 */
export const m_required = ({ i18next, attribute }) =>
  createAttributeMessage(i18next, 'required', attribute);

/**
 * Generates an enum validation message for when a value must be one of a set of allowed values
 */
export const m_enum = ({ i18next, attribute, params }) =>
  createAttributeMessage(i18next, 'enum', attribute, {
    values: params.allowedValues
      .map((value) => i18next.t(`common.label.${value}`))
      .join(`${i18next.t('common.label.comma')} `),
  });

/**
 * Generates a format validation message for when a value doesn't match the required format
 */
export const m_format = ({ i18next, attribute, params }) =>
  createAttributeMessage(i18next, 'format', attribute, {
    format: i18next.t(`common.label.${params.format}`),
  });

/**
 * Generates a type validation message for when a value is not of the expected type
 */
export const m_type = ({ i18next, attribute, params }) =>
  createAttributeMessage(i18next, 'type', attribute, {
    type: i18next.t(`common.label.${params.type}`),
  });

/**
 * Generates a minimum length validation message
 */
export const m_minLength = ({ i18next, attribute, params }) =>
  createAttributeMessage(i18next, 'minLength', attribute, {
    limit: params.limit,
  });

/**
 * Generates a maximum length validation message
 */
export const m_maxLength = ({ i18next, attribute, params }) =>
  createAttributeMessage(i18next, 'maxLength', attribute, {
    limit: params.limit,
  });

/**
 * Generates a minimum value validation message
 */
export const m_minimum = ({ i18next, attribute, params }) =>
  createAttributeMessage(i18next, 'minimum', attribute, {
    limit: params.limit,
  });

/**
 * Generates a maximum value validation message
 */
export const m_maximum = ({ i18next, attribute, params }) =>
  createAttributeMessage(i18next, 'maximum', attribute, {
    limit: params.limit,
  });

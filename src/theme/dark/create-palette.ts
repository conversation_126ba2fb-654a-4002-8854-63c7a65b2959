import type { ColorPreset } from '@/theme';
import { darkTheme, neutral } from '@/theme/colors';
import { getPrimaryDark } from '@/theme/utils';
import type { PaletteOptions } from '@mui/material';
import { alpha, darken, lighten } from '@mui/material/styles';

interface PaletteConfig {
  colorPreset?: ColorPreset;
}

export const createPalette = (config: PaletteConfig): PaletteOptions => {
  const { colorPreset } = config;

  // Extract standard colors
  const error = darkTheme.error;
  const info = darkTheme.info;
  const success = darkTheme.success;
  const warning = darkTheme.warning;

  // Extract custom colors
  const darkViolet = darkTheme.darkViolet;
  const emerald = darkTheme.emerald;
  const greenery = darkTheme.greenery;
  const honeyGold = darkTheme.honeyGold;
  const livingCoral = darkTheme.livingCoral;
  const monacoBlue = darkTheme.monacoBlue;
  const radiantOrchid = darkTheme.radiantOrchid;
  const roseQuartz = darkTheme.roseQuartz;
  const royalBlue = darkTheme.royalBlue;
  const tangerineTango = darkTheme.tangerineTango;
  const ultraViolet = darkTheme.ultraViolet;

  return {
    action: {
      disabledBackground: alpha(neutral[800], 0.2),
      disabled: neutral[600],
      disabledOpacity: 0.3,
      hover: alpha(neutral[800], 0.5),
      hoverOpacity: 0.08,
      selected: lighten(neutral[900], 0.15),
      selectedOpacity: 0.12,
      active: neutral[300],
      focus: neutral[300],
      focusOpacity: 0.12,
      activatedOpacity: 0.12,
    },
    background: {
      default: neutral[900],
      paper: lighten(neutral[900], 0.025),
    },
    divider: darken(neutral[800], 0.2),
    // Standard colors
    info,
    error,
    success,
    warning,
    contrastThreshold: 3,
    tonalOffset: 0.2,
    mode: 'dark',
    neutral,
    primary: getPrimaryDark(colorPreset),
    secondary: {
      dark: alpha(neutral[700], 0.3),
      main: alpha(neutral[700], 0.2),
      light: neutral[800],
      contrastText: neutral[300],
    },
    text: {
      primary: neutral[50],
      secondary: neutral[500],
      disabled: alpha(neutral[500], 0.7),
    },
    // Custom colors
    darkViolet,
    emerald,
    greenery,
    honeyGold,
    livingCoral,
    monacoBlue,
    radiantOrchid,
    roseQuartz,
    royalBlue,
    tangerineTango,
    ultraViolet,
  };
};

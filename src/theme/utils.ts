import type { PaletteColor } from '@mui/material/styles';
import { darkTheme, lightTheme } from './colors';
import type { ColorPreset } from './index';

export const getPrimaryDark = (preset?: ColorPreset): PaletteColor => {
  if (!preset) {
    return darkTheme.ultraViolet; // Default case
  }
  const color = darkTheme[preset.replace('-', '') as keyof typeof darkTheme];
  return color || darkTheme.ultraViolet; // Fallback
};

export const getPrimary = (preset?: ColorPreset): PaletteColor => {
  if (!preset) {
    return lightTheme.ultraViolet; // Default case
  }
  const color = lightTheme[preset.replace('-', '') as keyof typeof lightTheme];
  return color || lightTheme.ultraViolet; // Fallback
};

// Extended Sidebar Layout
export const SIDEBAR_WIDTH = 288;
export const SIDEBAR_WIDTH_COLLAPSED = 98;
export const HEADER_HEIGHT = 54;

// Common
export const BORDER_RADIUS = 6;
export const SPACING_UNIT = 10;

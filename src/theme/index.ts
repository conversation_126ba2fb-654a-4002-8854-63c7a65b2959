import { type Layout } from '@/contexts/customization';
import { type ThemeColor } from '@/theme/colors';
import type { Direction, PaletteMode, Theme } from '@mui/material';
import { createTheme as createMuiTheme, responsiveFontSizes } from '@mui/material/styles';
import { createOptions as createBaseOptions } from './base/create-options';
import { createOptions as createDarkOptions } from './dark/create-options';
import { createOptions as createLightOptions } from './light/create-options';

declare module '@mui/material/styles' {
  export interface NeutralColors {
    25: string;
    50: string;
    100: string;
    200: string;
    300: string;
    400: string;
    500: string;
    600: string;
    700: string;
    800: string;
    900: string;
  }

  /**
   * Extended Palette interface with custom colors
   */
  interface Palette {
    neutral: NeutralColors;

    // ColorPreset colors
    darkViolet: ThemeColor;
    emerald: ThemeColor;
    greenery: ThemeColor;
    honeyGold: ThemeColor;
    livingCoral: ThemeColor;
    monacoBlue: ThemeColor;
    radiantOrchid: ThemeColor;
    roseQuartz: ThemeColor;
    royalBlue: ThemeColor;
    tangerineTango: ThemeColor;
    ultraViolet: ThemeColor;

    // Additional custom colors (add new ones here)
    // teal: ThemeColor;
  }

  /**
   * Extended PaletteOptions interface with custom colors
   */
  interface PaletteOptions {
    neutral?: NeutralColors;

    // ColorPreset colors
    darkViolet?: ThemeColor;
    emerald?: ThemeColor;
    greenery?: ThemeColor;
    honeyGold?: ThemeColor;
    livingCoral?: ThemeColor;
    monacoBlue?: ThemeColor;
    radiantOrchid?: ThemeColor;
    roseQuartz?: ThemeColor;
    royalBlue?: ThemeColor;
    tangerineTango?: ThemeColor;
    ultraViolet?: ThemeColor;

    // Additional custom colors (add new ones here)
    // teal?: ThemeColor;
  }

  interface TypeBackground {
    paper: string;
    default: string;
  }
}

/**
 * Color presets for the theme
 */
export type ColorPreset =
  | 'livingCoral'
  | 'greenery'
  | 'ultraViolet'
  | 'roseQuartz'
  | 'radiantOrchid'
  | 'tangerineTango'
  | 'emerald'
  | 'honeyGold'
  | 'monacoBlue'
  | 'darkViolet'
  | 'royalBlue';

/**
 * Configuration options for the theme
 */
interface ThemeConfig {
  colorPreset?: ColorPreset;
  direction?: Direction;
  paletteMode?: PaletteMode;
  layout?: Layout;
}

/**
 * Creates a theme based on provided configuration
 * @param config - Theme configuration options
 * @returns MUI theme object
 */
export const createTheme = (config: ThemeConfig): Theme => {
  let theme = createMuiTheme(
    createBaseOptions({
      direction: config.direction,
    }),
    config.paletteMode === 'dark'
      ? createDarkOptions({
          colorPreset: config.colorPreset,
        })
      : createLightOptions({
          colorPreset: config.colorPreset,
          layout: config.layout,
        })
  );
  theme = responsiveFontSizes(theme);

  return theme;
};

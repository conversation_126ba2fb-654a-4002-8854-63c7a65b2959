import type { Direction, ThemeOptions } from '@mui/material';
import { BORDER_RADIUS } from '../utils';
import { createComponents } from './create-components';
import { createTypography } from './create-typography';

/**
 * Configuration options for theme components
 */
interface ThemeConfig {
  direction?: Direction;
}

/**
 * Creates theme options based on provided configuration
 * @param config - Theme configuration options
 * @returns ThemeOptions object for MUI theme creation
 */
export const createOptions = (config: ThemeConfig = {}): ThemeOptions => {
  const { direction = 'ltr' } = config;

  return {
    direction,
    spacing: 10,
    shape: {
      borderRadius: BORDER_RADIUS,
    },
    typography: createTypography(),
    components: createComponents(),
  };
};

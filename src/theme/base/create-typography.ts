import type { TypographyVariantsOptions } from '@mui/material/styles';

// Define constants for common values to improve maintainability
const INTER_FONT_FAMILY = "'Inter', sans-serif";
const SYSTEM_FONT_STACK =
  "-apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji'";
const DEFAULT_LINE_HEIGHT = 1.5;

// Group related typography variants for better organization
export const createTypography = (): TypographyVariantsOptions => {
  return {
    fontFamily: `'Inter', ${SYSTEM_FONT_STACK}`,

    // Headings
    h1: {
      fontFamily: INTER_FONT_FAMILY,
      fontWeight: 700,
      fontSize: '2.6rem',
      lineHeight: 1.2,
      letterSpacing: '-0.05rem',
    },
    h2: {
      fontFamily: INTER_FONT_FAMILY,
      fontWeight: 600,
      fontSize: '1.9rem',
      lineHeight: 1.3,
      letterSpacing: '-0.04rem',
    },
    h3: {
      fontFamily: INTER_FONT_FAMILY,
      fontWeight: 600,
      fontSize: '1.5rem',
      lineHeight: 1.4,
      letterSpacing: '-0.03rem',
    },
    h4: {
      fontFamily: INTER_FONT_FAMILY,
      fontWeight: 600,
      fontSize: '1.2rem',
      lineHeight: 1.6,
    },
    h5: {
      fontFamily: INTER_FONT_FAMILY,
      fontWeight: 600,
      fontSize: '1rem',
      lineHeight: DEFAULT_LINE_HEIGHT,
    },
    h6: {
      fontFamily: INTER_FONT_FAMILY,
      fontWeight: 600,
      fontSize: '.92rem',
      lineHeight: DEFAULT_LINE_HEIGHT,
    },

    // Body text
    body1: {
      fontSize: '0.875rem',
      fontWeight: 400,
      lineHeight: DEFAULT_LINE_HEIGHT,
    },
    body2: {
      fontSize: '0.8rem',
      fontWeight: 500,
      lineHeight: DEFAULT_LINE_HEIGHT,
    },

    // Other text elements
    subtitle1: {
      fontSize: '.915rem',
      fontWeight: 400,
      lineHeight: DEFAULT_LINE_HEIGHT,
    },
    subtitle2: {
      fontSize: '0.875rem',
      fontWeight: 400,
      lineHeight: DEFAULT_LINE_HEIGHT,
    },
    button: {
      fontSize: '0.875rem',
      fontWeight: 500,
      textTransform: 'uppercase',
    },
    caption: {
      fontSize: '0.85rem',
      fontWeight: 500,
      lineHeight: DEFAULT_LINE_HEIGHT,
      textTransform: 'uppercase',
    },
    overline: {
      fontSize: '0.75rem',
      fontWeight: 700,
      lineHeight: 2,
      textTransform: 'uppercase',
    },
  };
};

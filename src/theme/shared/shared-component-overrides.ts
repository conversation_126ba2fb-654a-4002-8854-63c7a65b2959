import {
  getMuiChartsSurfaceStyles,
  getMuiCheckboxStyles,
  getMuiChipStyles,
  getMuiFilledInputStyles,
  getMuiInputStyles,
  getMuiLineElementStyles,
  getMuiListItemButtonStyles,
  getMuiMenuItemStyles,
  getMuiOutlinedInputStyles,
  getMuiRadioStyles,
  getMuiTimelineConnectorStyles,
} from '@/theme/shared/mui-style-parts';
import { type PaletteOptions } from '@mui/material/styles';

/**
 * Creates shared Material-UI component style overrides that are common across all themes.
 */
export const sharedStyles = (palette: PaletteOptions) => {
  return {
    MuiRadio: {
      styleOverrides: getMuiRadioStyles(palette),
    },
    MuiCheckbox: {
      styleOverrides: getMuiCheckboxStyles(palette),
    },
    MuiChartsSurface: {
      styleOverrides: getMuiChartsSurfaceStyles(palette),
    },
    MuiLineElement: {
      styleOverrides: getMuiLineElementStyles(palette),
    },
    MuiFilledInput: {
      styleOverrides: getMuiFilledInputStyles(palette),
    },
    MuiPickersFilledInput: {
      styleOverrides: getMuiFilledInputStyles(palette),
    },
    MuiOutlinedInput: {
      styleOverrides: getMuiOutlinedInputStyles(palette),
    },
    MuiPickersOutlinedInput: {
      styleOverrides: getMuiOutlinedInputStyles(palette, true),
    },
    MuiInput: {
      styleOverrides: getMuiInputStyles(palette),
    },
    MuiPickersInput: {
      styleOverrides: getMuiInputStyles(palette),
    },
    MuiMenuItem: {
      styleOverrides: getMuiMenuItemStyles(palette),
    },
    MuiListItemButton: {
      styleOverrides: getMuiListItemButtonStyles(palette),
    },
    MuiChip: {
      styleOverrides: getMuiChipStyles(palette),
    },
    MuiTimelineConnector: {
      styleOverrides: getMuiTimelineConnectorStyles(palette),
    },
  };
};

import { neutral } from '@/theme/colors';
import { common } from '@mui/material/colors';
import { alpha, type PaletteColor, type PaletteOptions } from '@mui/material/styles';

/**
 * Creates style overrides for MuiRadio components.
 */
export const getMuiRadioStyles = (palette: PaletteOptions) => ({
  root: {
    '&.Mui-focusVisible': {
      backgroundColor: alpha((palette.primary as PaletteColor).main, 0.12),
    },
    '&:hover > span': {
      boxShadow: `0 0 0 3px ${alpha((palette.primary as PaletteColor).main, 0.12)} inset`,
    },
  },
});

/**
 * Creates style overrides for MuiCheckbox components.
 */
export const getMuiCheckboxStyles = (palette: PaletteOptions) => ({
  root: {
    '&.Mui-focusVisible': {
      backgroundColor: alpha((palette.primary as PaletteColor).main, 0.12),
    },
  },
});

/**
 * Creates style overrides for MuiChartsSurface components.
 */
export const getMuiChartsSurfaceStyles = (palette: PaletteOptions) => ({
  root: {
    '& .MuiBarElement-root, & .MuiAreaElement-root, & .MuiPieArc-root': {
      filter: `drop-shadow(5px -5px 5px ${palette.mode === 'dark' ? 'rgba(0,0,0,0.16)' : 'rgba(114,117,120,0.12)'})`,
    },
  },
});

/**
 * Creates style overrides for MuiLineElement components.
 */
export const getMuiLineElementStyles = (palette: PaletteOptions) => ({
  root: {
    filter: `drop-shadow(2px 5px 5px ${palette.mode === 'dark' ? 'rgba(0,0,0,0.20)' : 'rgba(114,117,120,0.16)'})`,
  },
});

/**
 * Creates style overrides for MuiFilledInput and MuiPickersFilledInput components.
 */
export const getMuiFilledInputStyles = (palette: PaletteOptions) => ({
  root: {
    backgroundColor:
      palette.mode === 'dark'
        ? alpha(palette.neutral![800], 0.16)
        : alpha(palette.neutral![50], 0.4),
    boxShadow: `${
      palette.mode === 'dark'
        ? alpha(palette.neutral![900], 0.64)
        : alpha(palette.neutral![400], 0.3)
    } 0 1px 3px`,
    borderColor: palette.mode === 'dark' ? palette.neutral![800] : palette.neutral![400],

    '&:hover': {
      backgroundColor: palette.background!.paper,
      borderColor: palette.mode === 'dark' ? palette.neutral![700] : palette.neutral![500],
    },
    '&.Mui-focused': {
      backgroundColor: palette.background!.paper,
      borderColor: (palette.primary as PaletteColor).main,
      boxShadow: `${(palette.primary as PaletteColor).main} 0 0px 0 1px inset`,
    },
    '&.Mui-disabled': {
      backgroundColor: palette.mode === 'dark' ? palette.neutral![900] : palette.neutral![50],
      borderColor: palette.mode === 'dark' ? palette.neutral![800] : palette.neutral![300],
      boxShadow: `${
        palette.mode === 'dark'
          ? alpha(palette.neutral![900], 0.3)
          : alpha(palette.neutral![400], 0.3)
      } 0 1px 3px`,
    },
    '&.Mui-error': {
      backgroundColor: alpha((palette.error as PaletteColor).main, 0.02),
      borderColor: (palette.error as PaletteColor).main,
      boxShadow: `${alpha((palette.error as PaletteColor).main, 0.12)} 0 1px 3px`,
      '&.Mui-focused': {
        backgroundColor: palette.background!.paper,
        boxShadow: `${(palette.error as PaletteColor).main} 0 0px 0 1px inset`,
      },
      '&:hover': {
        backgroundColor: alpha((palette.error as PaletteColor).main, 0.02),
        borderColor: (palette.error as PaletteColor).main,
      },
    },
  },
});

/**
 * Creates style overrides for MuiOutlinedInput and MuiPickersOutlinedInput components.
 */
export const getMuiOutlinedInputStyles = (palette: PaletteOptions, pickers: boolean = false) => {
  const prefix = pickers ? 'Pickers' : '';

  return {
    root: {
      boxShadow: `${
        palette.mode === 'dark'
          ? alpha(palette.neutral![900], 0.64)
          : alpha(palette.neutral![400], 0.3)
      } 0 1px 3px`,
      backgroundColor: palette.background!.paper,

      '&:hover': {
        [`.Mui${prefix}OutlinedInput-notchedOutline`]: {
          borderColor: palette.mode === 'dark' ? palette.neutral![700] : palette.neutral![500],
        },

        '&.Mui-focused': {
          [`.Mui${prefix}OutlinedInput-notchedOutline`]: {
            borderColor: (palette.primary as PaletteColor).main,
          },
        },

        '&.Mui-error': {
          [`.Mui${prefix}OutlinedInput-notchedOutline`]: {
            borderColor: (palette.error as PaletteColor).main,
          },
        },
      },

      '&.Mui-disabled': {
        backgroundColor: palette.mode === 'dark' ? palette.neutral![900] : palette.neutral![25],
        boxShadow: `${
          palette.mode === 'dark'
            ? alpha(palette.neutral![900], 0.3)
            : alpha(palette.neutral![400], 0.3)
        } 0 1px 3px, ${
          palette.mode === 'dark' ? palette.neutral![800] : alpha(palette.neutral![400], 0.3)
        } 0 1px 3px inset`,

        [`.Mui${prefix}OutlinedInput-notchedOutline`]: {
          borderColor: palette.mode === 'dark' ? palette.neutral![800] : palette.neutral![400],
        },
      },

      '&.Mui-error': {
        backgroundColor: alpha((palette.error as PaletteColor).main, 0.01),
        boxShadow: `${alpha((palette.error as PaletteColor).main, 0.12)} 0 1px 3px`,
        '&.Mui-focused': {
          boxShadow: 'none',
        },
      },
    },
    notchedOutline: {
      borderColor: palette.mode === 'dark' ? palette.neutral![800] : palette.neutral![400],
    },
  };
};

/**
 * Creates style overrides for MuiInput and MuiPickersInput components.
 */
export const getMuiInputStyles = (palette: PaletteOptions) => ({
  underline: {
    '&.Mui-error .MuiNativeSelect-outlined': {
      borderColor: (palette.error as PaletteColor).main,
      backgroundColor: alpha((palette.error as PaletteColor).main, 0.01),
      boxShadow: `${alpha((palette.error as PaletteColor).main, 0.12)} 0 1px 3px`,
      color: (palette.error as PaletteColor).main,

      '&:hover': {
        borderColor: (palette.error as PaletteColor).main,
      },

      '&:focus': {
        borderColor: (palette.error as PaletteColor).main,
        boxShadow: 'none',
      },
    },
  },
});

/**
 * Creates style overrides for MuiMenuItem components.
 */
export const getMuiMenuItemStyles = (palette: PaletteOptions) => ({
  root: {
    '&.Mui-focusVisible': {
      backgroundColor:
        palette.mode === 'dark' ? alpha(palette.neutral![800], 0.25) : palette.neutral![50],
    },
    color: palette.mode === 'dark' ? palette.neutral![100] : palette.neutral![700],
    '&:hover': {
      backgroundColor:
        palette.mode === 'dark' ? alpha(palette.neutral![800], 0.25) : palette.neutral![50],
      color: palette.mode === 'dark' ? palette.neutral![100] : palette.neutral![900],
    },
    '&.Mui-selected, &.Mui-selected:hover': {
      backgroundColor:
        palette.mode === 'dark' ? alpha(palette.neutral![800], 0.25) : palette.neutral![100],
      color: palette.mode === 'dark' ? palette.neutral![100] : palette.neutral![900],

      '&.Mui-focusVisible': {
        backgroundColor:
          palette.mode === 'dark' ? alpha(palette.neutral![800], 0.25) : palette.neutral![200],
      },
    },
  },
});

/**
 * Creates style overrides for MuiListItemButton components.
 */
export const getMuiListItemButtonStyles = (palette: PaletteOptions) => ({
  root: {
    '&.Mui-focusVisible': {
      backgroundColor:
        palette.mode === 'dark' ? alpha(palette.neutral![800], 0.2) : palette.neutral![50],
    },
    color: palette.mode === 'dark' ? palette.neutral![500] : palette.neutral![700],
    '&:hover': {
      backgroundColor:
        palette.mode === 'dark' ? alpha(palette.neutral![800], 0.2) : palette.neutral![50],
      color: palette.mode === 'dark' ? palette.neutral![25] : palette.neutral![900],
    },
    '&.Mui-selected, &.Mui-selected:hover': {
      backgroundColor:
        palette.mode === 'dark' ? alpha(palette.neutral![800], 0.2) : palette.neutral![100],
      color: palette.mode === 'dark' ? palette.neutral![25] : palette.neutral![900],

      '&.Mui-focusVisible': {
        backgroundColor:
          palette.mode === 'dark' ? alpha(palette.neutral![800], 0.2) : palette.neutral![200],
      },
    },
  },
});

/**
 * Creates style overrides for MuiChip components.
 */
export const getMuiChipStyles = (palette: PaletteOptions) => ({
  root: {
    ...(palette.mode === 'dark' && {
      '&.MuiChip-outlined.MuiChip-colorDefault': {
        backgroundColor: alpha(common.white, 0.06),
        borderColor: alpha(common.white, 0.18),
      },
    }),

    // Dynamically apply styles to all color variants (both standard and custom)
    ...Object.entries(palette)
      .filter(
        ([_, value]) =>
          // Only include entries that are PaletteColor objects with a 'main' property
          value && typeof value === 'object' && 'main' in value
      )
      .reduce((acc, [colorKey, colorValue]) => {
        const color = colorValue as PaletteColor;

        return {
          ...acc,
          [`&.MuiChip-color${colorKey.charAt(0).toUpperCase() + colorKey.slice(1)}`]: {
            backgroundColor: alpha(color.main, 0.08),
            borderColor: alpha(color.main, 0.3),
            color: palette.mode === 'dark' ? color.dark : color.light,
            '&.MuiChip-clickable:hover': {
              backgroundColor: alpha(color.main, 0.12),
            },
          },
        };
      }, {}),
  },
  ...(palette.mode === 'dark' && {
    outlinedSecondary: {
      color: neutral[400],
    },
  }),
});

/**
 * Creates style overrides for MuiTimelineConnector components.
 */
export const getMuiTimelineConnectorStyles = (palette: PaletteOptions) => ({
  root: {
    backgroundColor: palette.divider,
  },
});

import type { ColorPreset } from '@/theme';
import { lightTheme, neutral } from '@/theme/colors';
import { getPrimary } from '@/theme/utils';
import type { PaletteOptions } from '@mui/material';
import { common } from '@mui/material/colors';
import { alpha } from '@mui/material/styles';

interface PaletteConfig {
  colorPreset?: ColorPreset;
}

export const createPalette = (config: PaletteConfig): PaletteOptions => {
  const { colorPreset } = config;

  // Extract standard colors
  const error = lightTheme.error;
  const info = lightTheme.info;
  const success = lightTheme.success;
  const warning = lightTheme.warning;

  // Extract custom colors
  const darkViolet = lightTheme.darkViolet;
  const emerald = lightTheme.emerald;
  const greenery = lightTheme.greenery;
  const honeyGold = lightTheme.honeyGold;
  const livingCoral = lightTheme.livingCoral;
  const monacoBlue = lightTheme.monacoBlue;
  const radiantOrchid = lightTheme.radiantOrchid;
  const roseQuartz = lightTheme.roseQuartz;
  const royalBlue = lightTheme.royalBlue;
  const tangerineTango = lightTheme.tangerineTango;
  const ultraViolet = lightTheme.ultraViolet;

  return {
    action: {
      disabledBackground: alpha(neutral[25], 0.8),
      disabledOpacity: 0.6,
    },
    background: {
      default: neutral[50],
      paper: common.white,
    },
    divider: neutral[200],
    // Standard colors
    info,
    error,
    success,
    warning,
    contrastThreshold: 3,
    tonalOffset: 0.2,
    mode: 'light',
    neutral,
    primary: getPrimary(colorPreset),
    secondary: {
      dark: neutral[800],
      main: neutral[900],
      light: neutral[700],
      contrastText: neutral[50],
    },
    text: {
      primary: neutral[800],
      secondary: neutral[700],
      disabled: alpha(neutral[900], 0.35),
    },
    // Custom colors
    darkViolet,
    emerald,
    greenery,
    honeyGold,
    livingCoral,
    monacoBlue,
    radiantOrchid,
    roseQuartz,
    royalBlue,
    tangerineTango,
    ultraViolet,
  };
};

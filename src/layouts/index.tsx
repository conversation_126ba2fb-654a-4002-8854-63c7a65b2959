import { VerticalShellsLight } from '@/components/application-ui/shells/vertical-shells-light';
import { withAuthGuard } from '@/hocs/with-auth-guard';
import getMenuItems from '@/router/menu-items';
import { type ReactNode } from 'react';
import { useTranslation } from 'react-i18next';

interface LayoutProps {
  children?: ReactNode;
  menuItems?: MenuItem[];
}

const BaseLayout = (props: LayoutProps) => {
  const { t } = useTranslation();
  const defaultMenuItems = getMenuItems(t);

  return (
    <VerticalShellsLight
      menuItems={props.menuItems ?? defaultMenuItems}
      {...props}
    />
  );
};

export const Layout = withAuthGuard(BaseLayout);

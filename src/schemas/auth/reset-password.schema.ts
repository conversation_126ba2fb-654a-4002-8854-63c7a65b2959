import { z as zod } from 'zod';

/**
 * Reset password form validation schema
 *
 * Validates email field for the reset password form.
 */
export const resetPasswordSchema = zod.object({
  email: zod.string().min(1, { message: 'Email is required' }).email(),
});

/**
 * Reset password form values type
 */
export type ResetPasswordFormValues = zod.infer<typeof resetPasswordSchema>;

/**
 * Default values for the reset password form
 */
export const defaultResetPasswordValues: ResetPasswordFormValues = {
  email: '',
};

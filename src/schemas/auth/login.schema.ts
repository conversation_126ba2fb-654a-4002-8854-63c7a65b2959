import { transformText } from '@/utils/text-transform.util';
import { type TFunction } from 'i18next';
import { z as zod } from 'zod';

/**
 * Normal login form validation schema
 *
 * Validates access id, username and password fields for the normal login form.
 * Pass a translation function (t) as an argument.
 */
export const normalLoginSchema = (t: TFunction) => {
  const digitExactValidation =
    t('validation.sentence.exactLength', {
      attribute: t('common.label.accessId'),
      limit: '12',
    }) +
    ' (' +
    t('validation.sentence.numbersOnly') +
    ')';

  const minLengthValidation = t('validation.sentence.minimumLength', {
    attribute: transformText(t('common.label.username'), 'sentenceCase'),
    limit: '3',
  });

  const maxLengthValidation = t('validation.sentence.maximumLength', {
    attribute: transformText(t('common.label.username'), 'sentenceCase'),
    limit: '50',
  });

  const requiredValidation = t('validation.sentence.requiredField', {
    attribute: transformText(t('common.label.password'), 'sentenceCase'),
  });

  return zod.object({
    accessId: zod.string().regex(/^$|^\d{12}$/, {
      message: digitExactValidation,
    }),
    username: zod
      .string()
      .min(3, { message: minLengthValidation })
      .max(50, { message: maxLengthValidation }),
    password: zod.string().min(1, { message: requiredValidation }),
  });
};

/**
 * Normal login form values type
 */
// Type must match the shape returned by the schema
export type NormalLoginFormValues = {
  accessId: string;
  username: string;
  password: string;
};

/**
 * Default values for the normal login form
 */
export const defaultNormalLoginValues: NormalLoginFormValues = {
  accessId: '',
  username: '',
  password: '',
};

/**
 * OAuth login form validation schema
 *
 * Validates access id for the OAuth login form.
 */
export const oAuthLoginSchema = (t: TFunction) => {
  const digitExactValidation =
    t('validation.sentence.exactLength', {
      input: t('common.label.accessId'),
      digit: '12',
    }) +
    ' (' +
    t('validation.sentence.numbersOnly') +
    ')';

  return zod.object({
    accessId: zod.string().regex(/^$|^\d{12}$/, {
      message: digitExactValidation,
    }),
  });
};

/**
 * OAuth login form values type
 */
export type OAuthLoginFormValues = {
  accessId: string;
};
/**
 * Default values for the OAuth login form
 */
export const defaultOAuthLoginValues: OAuthLoginFormValues = {
  accessId: '',
};

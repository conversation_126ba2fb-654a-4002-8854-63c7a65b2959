// Define Item Schema
const getAllItemsSchema = {
  tags: ['Example'],
  summary: 'Get all items',
  description: 'Retrieves a list of all items.',
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'integer' },
              name: { type: 'string' },
              amount: { type: 'number' },
            },
          },
        },
      },
      examples: [
        {
          description: 'Example Response',
          value: {
            message: 'Items retrieved successfully',
            data: [
              { id: 1, name: 'Item A', amount: 10 },
              { id: 2, name: 'Item B', amount: 5 },
              { id: 3, name: 'Item C', amount: 12 },
            ],
          },
        },
      ],
    },
    '4xx': { $ref: 'ErrorResponse' },
    '5xx': { $ref: 'ErrorResponse' },
  },
};

const getItemByIdSchema = {
  tags: ['Example'],
  summary: 'Get item by ID',
  description: 'Retrieves a single item by its ID.',
  params: {
    type: 'object',
    properties: {
      id: {
        type: 'integer',
        description: 'Item ID',
      },
    },
  },
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'object',
          properties: {
            id: { type: 'integer' },
            name: { type: 'string' },
            amount: { type: 'number' },
          },
        },
      },
      examples: [
        {
          description: 'Example Response',
          value: {
            message: 'Item retrieved successfully',
            data: { id: 1, name: 'Item A', amount: 10 },
          },
        },
      ],
    },
    '4xx': { $ref: 'ErrorResponse' },
    '5xx': { $ref: 'ErrorResponse' },
  },
};

const createItemSchema = {
  tags: ['Example'],
  summary: 'Create a new item',
  description: 'Creates a new item with the provided details.',
  body: {
    type: 'object',
    required: ['name', 'amount'],
    properties: {
      name: { type: 'string', description: 'Name of the item' },
      amount: { type: 'number', description: 'Amount of the item' },
    },
  },
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'object',
          properties: {
            id: { type: 'integer' },
            name: { type: 'string' },
            amount: { type: 'number' },
          },
        },
      },
      examples: [
        {
          description: 'Example Response',
          value: {
            message: 'Item created successfully',
            data: { id: 4, name: 'New Item', amount: 15 },
          },
        },
      ],
    },
    '4xx': { $ref: 'ErrorResponse' },
    '5xx': { $ref: 'ErrorResponse' },
  },
};

const updateItemSchema = {
  tags: ['Example'],
  summary: 'Update an item by ID',
  description: 'Updates an existing item with the provided details.',
  params: {
    type: 'object',
    properties: {
      id: { type: 'integer', description: 'Item ID' },
    },
  },
  body: {
    type: 'object',
    required: ['name', 'amount'],
    properties: {
      name: { type: 'string', description: 'Updated name of the item' },
      amount: { type: 'number', description: 'Updated amount of the item' },
    },
  },
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'object',
          properties: {
            id: { type: 'integer' },
            name: { type: 'string' },
            amount: { type: 'number' },
          },
        },
      },
      examples: [
        {
          description: 'Example Response',
          value: {
            message: 'Item updated successfully',
            data: { id: 1, name: 'Updated Item', amount: 20 },
          },
        },
      ],
    },
    '4xx': { $ref: 'ErrorResponse' },
    '5xx': { $ref: 'ErrorResponse' },
  },
};

const deleteItemSchema = {
  tags: ['Example'],
  summary: 'Delete an item by ID',
  description: 'Deletes an item with the specified ID.',
  params: {
    type: 'object',
    properties: {
      id: { type: 'integer', description: 'Item ID' },
    },
  },
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
      },
      examples: [
        {
          description: 'Example Response',
          value: {
            message: 'Item deleted successfully',
          },
        },
      ],
    },
    '4xx': { $ref: 'ErrorResponse' },
    '5xx': { $ref: 'ErrorResponse' },
  },
};

export {
  getAllItemsSchema,
  getItemByIdSchema,
  createItemSchema,
  updateItemSchema,
  deleteItemSchema,
};

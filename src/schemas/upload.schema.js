const uploadSchema = {
  tags: ['Example'],
  summary: 'Upload a file to S3',
  description: 'Uploads a file to AWS S3 and returns the file URL.',
  consumes: ['multipart/form-data'],
  body: {
    type: 'object',
    properties: {
      file: {
        isFile: true,
        type: 'object',
        properties: {
          filename: { type: 'string', description: 'Name of the file' },
          mimetype: {
            type: 'string',
            enum: ['image/jpeg', 'image/png', 'application/pdf'],
            description: 'MIME type of the file',
          },
          encoding: { type: 'string', description: 'File encoding' },
        },
      },
    },
    required: ['file'],
  },
  response: {
    200: {
      description: 'Success response',
      type: 'object',
      properties: {
        message: { type: 'string', description: 'Success message' },
        data: {
          type: 'object',
          properties: {
            success: { type: 'boolean', description: 'Indicates if the upload was successful' },
            file: { type: 'string', description: 'URL of the uploaded file' },
          },
        },
      },
      examples: [
        {
          description: 'Example Response',
          value: {
            message: 'File uploaded successfully',
            data: {
              success: true,
              file: 'https://your-bucket.s3.amazonaws.com/uploaded-file.jpg',
            },
          },
        },
      ],
    },
    '4xx': { $ref: 'ErrorResponse' },
    '5xx': { $ref: 'ErrorResponse' },
  },
};

export { uploadSchema };

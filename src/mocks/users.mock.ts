import type { User } from '@/services/users';

const AVATAR_PATHS = {
  1: '/avatars/1.png',
  2: '/avatars/2.png',
  3: '/avatars/3.png',
  4: '/avatars/4.png',
  5: '/avatars/5.png',
} as const;

const COVER_PATHS = {
  1: '/placeholders/covers/1.jpg',
  2: '/placeholders/covers/2.jpg',
  3: '/placeholders/covers/3.jpg',
  4: '/placeholders/covers/4.jpg',
  5: '/placeholders/covers/5.jpg',
  6: '/placeholders/covers/6.jpg',
} as const;

class UsersApi {
  getUsers(): Promise<User[]> {
    const users: User[] = [
      {
        id: '1',
        name: '<PERSON>',
        avatar: AVATAR_PATHS[1],
        email: '<EMAIL>',
        jobtitle: 'Digital Marketing Manager',
        username: 'AliceMktg',
        location: 'Newport City',
        role: 'admin',
        coverImg: COVER_PATHS[1],
        followers: '1234',
        description: 'Passionate about digital marketing trends and social media strategies.',
        posts: 15,
        status: 'active',
        accessId: '************',
      },
      {
        id: '2',
        name: 'Bob <PERSON>',
        avatar: AVATAR_PATHS[2],
        email: '<EMAIL>',
        jobtitle: 'Senior Software Engineer',
        username: 'DevBob',
        location: 'Techville',
        role: 'subscriber',
        coverImg: COVER_PATHS[2],
        followers: '567',
        description:
          'Dedicated software engineer with a knack for creating intuitive user experiences.',
        posts: 10,
        status: 'active',
        accessId: '************',
      },
      {
        id: '3',
        name: 'Cynthia Ray',
        avatar: AVATAR_PATHS[3],
        email: '<EMAIL>',
        jobtitle: 'Project Manager',
        username: 'CynthiaPM',
        location: 'Riverbank City',
        role: 'customer',
        coverImg: COVER_PATHS[3],
        followers: '890',
        description:
          'Expert in managing diverse projects with a focus on efficiency and collaboration.',
        posts: 20,
        status: 'active',
        accessId: '************',
      },
      {
        id: '4',
        name: 'Daniel Green',
        avatar: AVATAR_PATHS[4],
        email: '<EMAIL>',
        jobtitle: 'Creative Director',
        username: 'CreativeDan',
        location: 'Uptown',
        role: 'admin',
        coverImg: COVER_PATHS[4],
        followers: '450',
        description: 'Leading creative campaigns with innovative ideas and a modern approach.',
        posts: 9,
        status: 'inactive',
        accessId: '************',
      },
      {
        id: '5',
        name: 'Evan Turner',
        avatar: AVATAR_PATHS[5],
        email: '<EMAIL>',
        jobtitle: 'IT Analyst',
        username: 'EvanTech',
        location: 'Greenwood',
        role: 'customer',
        coverImg: COVER_PATHS[5],
        followers: '300',
        description: 'Analyzing tech trends and providing insightful IT solutions.',
        posts: 7,
        status: 'active',
        accessId: '************',
      },
      {
        id: '6',
        name: 'Fiona Grant',
        avatar: AVATAR_PATHS[1],
        email: '<EMAIL>',
        jobtitle: 'Human Resources Coordinator',
        username: 'FionaHR',
        location: 'Hilltop',
        role: 'subscriber',
        coverImg: COVER_PATHS[6],
        followers: '215',
        description:
          'Dedicated to creating a positive work environment and fostering professional growth.',
        posts: 14,
        status: 'active',
        accessId: '************',
      },
      {
        id: '7',
        name: 'George Hall',
        avatar: AVATAR_PATHS[2],
        email: '<EMAIL>',
        jobtitle: 'Business Development Manager',
        username: 'GeorgeBiz',
        location: 'Laketown',
        role: 'admin',
        coverImg: COVER_PATHS[1],
        followers: '541',
        description:
          'Strategizing new business opportunities and building strong client relationships.',
        posts: 19,
        status: 'suspended',
        accessId: '************',
      },
      {
        id: '8',
        name: 'Hannah Scott',
        avatar: AVATAR_PATHS[3],
        email: '<EMAIL>',
        jobtitle: 'Account Executive',
        username: 'HannahSales',
        location: 'Riverfield',
        role: 'subscriber',
        coverImg: COVER_PATHS[2],
        followers: '622',
        description: 'Expert in client management and driving sales growth.',
        posts: 23,
        status: 'inactive',
        accessId: '************',
      },
      {
        id: '9',
        name: 'Isaac Newton',
        avatar: AVATAR_PATHS[4],
        email: '<EMAIL>',
        jobtitle: 'Research Scientist',
        username: 'IsaacSci',
        location: 'Techton',
        role: 'customer',
        coverImg: COVER_PATHS[3],
        followers: '982',
        description: 'Exploring the frontiers of science and technology.',
        posts: 30,
        status: 'inactive',
        accessId: '************',
      },
      {
        id: '10',
        name: 'Julia Cruz',
        avatar: AVATAR_PATHS[5],
        email: '<EMAIL>',
        jobtitle: 'Graphic Designer',
        username: 'JuliaDesigns',
        location: 'CreativeVille',
        role: 'subscriber',
        coverImg: COVER_PATHS[4],
        followers: '765',
        description: 'Creating visually stunning designs that speak volumes.',
        posts: 27,
        status: 'active',
        accessId: '************',
      },
      {
        id: '11',
        name: 'Kevin Lopez',
        avatar: AVATAR_PATHS[1],
        email: '<EMAIL>',
        jobtitle: 'Operations Manager',
        username: 'KevinOps',
        location: 'IndustryHub',
        role: 'admin',
        coverImg: COVER_PATHS[5],
        followers: '436',
        description: 'Ensuring seamless operations and efficient management processes.',
        posts: 14,
        status: 'active',
        accessId: '************',
      },
      {
        id: '12',
        name: 'Laura Martin',
        avatar: AVATAR_PATHS[2],
        email: '<EMAIL>',
        jobtitle: 'Content Writer',
        username: 'LauraWrites',
        location: 'Wordtown',
        role: 'subscriber',
        coverImg: COVER_PATHS[6],
        followers: '390',
        description: 'Crafting compelling content that captivates audiences.',
        posts: 21,
        status: 'suspended',
        accessId: '************',
      },
      {
        id: '13',
        name: 'Miguel Gonzalez',
        avatar: AVATAR_PATHS[3],
        email: '<EMAIL>',
        jobtitle: 'Data Analyst',
        username: 'MiguelData',
        location: 'DataCity',
        role: 'customer',
        coverImg: COVER_PATHS[1],
        followers: '523',
        description: 'Transforming data into insights and strategic actions.',
        posts: 18,
        status: 'active',
        accessId: '************',
      },
      {
        id: '14',
        name: 'Nina Patel',
        avatar: AVATAR_PATHS[4],
        email: '<EMAIL>',
        jobtitle: 'UI/UX Designer',
        username: 'NinaDesign',
        location: 'DesignTown',
        role: 'admin',
        coverImg: COVER_PATHS[2],
        followers: '689',
        description: 'Designing intuitive and engaging user experiences.',
        posts: 26,
        status: 'suspended',
        accessId: '************',
      },
      {
        id: '15',
        name: 'Oscar Wallace',
        avatar: AVATAR_PATHS[5],
        email: '<EMAIL>',
        jobtitle: 'Marketing Coordinator',
        username: 'OscarMktg',
        location: 'Marketville',
        role: 'subscriber',
        coverImg: COVER_PATHS[3],
        followers: '412',
        description: 'Coordinating marketing efforts to maximize brand exposure.',
        posts: 17,
        status: 'active',
        accessId: '************',
      },
      {
        id: '16',
        name: 'Pamela Wright',
        avatar: AVATAR_PATHS[1],
        email: '<EMAIL>',
        jobtitle: 'Chief Financial Officer',
        username: 'PamelaCFO',
        location: 'FinanceCity',
        role: 'admin',
        coverImg: COVER_PATHS[4],
        followers: '530',
        description: 'Directing financial strategies for sustainable growth.',
        posts: 22,
        status: 'inactive',
        accessId: '************',
      },
      {
        id: '17',
        name: 'Quentin Ramirez',
        avatar: AVATAR_PATHS[2],
        email: '<EMAIL>',
        jobtitle: 'Network Administrator',
        username: 'QuentinNet',
        location: 'Netville',
        role: 'customer',
        coverImg: COVER_PATHS[5],
        followers: '345',
        description: 'Maintaining robust and secure network infrastructures.',
        posts: 12,
        status: 'inactive',
        accessId: '************',
      },
      {
        id: '18',
        name: 'Rachel Kim',
        avatar: AVATAR_PATHS[3],
        email: '<EMAIL>',
        jobtitle: 'Public Relations Specialist',
        username: 'RachelPR',
        location: 'MediaCity',
        role: 'subscriber',
        coverImg: COVER_PATHS[6],
        followers: '601',
        description: 'Building and maintaining positive public images for businesses.',
        posts: 29,
        status: 'active',
        accessId: '************',
      },
      {
        id: '19',
        name: 'Steven Ford',
        avatar: AVATAR_PATHS[4],
        email: '<EMAIL>',
        jobtitle: 'Sales Director',
        username: 'StevenSales',
        location: 'SalesTown',
        role: 'admin',
        coverImg: COVER_PATHS[1],
        followers: '489',
        description: 'Leading sales teams to achieve outstanding results.',
        posts: 33,
        status: 'active',
        accessId: '************',
      },
      {
        id: '20',
        name: 'Tracy Nguyen',
        avatar: AVATAR_PATHS[5],
        email: '<EMAIL>',
        jobtitle: 'Product Manager',
        username: 'TracyProd',
        location: 'InnovationHub',
        role: 'subscriber',
        coverImg: COVER_PATHS[2],
        followers: '556',
        description: 'Overseeing product development from concept to launch.',
        posts: 16,
        status: 'suspended',
        accessId: '************',
      },
    ];

    return Promise.resolve(users);
  }

  getUser(): Promise<User> {
    const user: User = {
      id: '1',
      name: 'Alice Johnson',
      avatar: AVATAR_PATHS[1],
      email: '<EMAIL>',
      jobtitle: 'Digital Marketing Manager',
      username: 'AliceMktg',
      location: 'Newport City',
      role: 'admin',
      coverImg: COVER_PATHS[1],
      followers: '1234',
      description: 'Passionate about digital marketing trends and social media strategies.',
      posts: 15,
      status: 'active',
      accessId: '************',
    };

    return Promise.resolve(user);
  }
}

export const usersApi = new UsersApi();

import { GuestGuard } from '@/components/features/auth/guards/guest-guard';

/**
 * Higher-order component that wraps a given component with a guest guard.
 *
 * This ensures that the wrapped component is only accessible to non-authenticated
 * users. If the user is authenticated, they will be redirected as configured in the
 * `GuestGuard` component.
 *
 * @prop {React.ComponentType<P>} Component - The component to be wrapped with the guest guard.
 */
export const withGuestGuard = <P extends object>(Component: React.ComponentType<P>) => {
  return function WithGuestGuard(props: P) {
    return (
      <GuestGuard>
        <Component {...props} />
      </GuestGuard>
    );
  };
};

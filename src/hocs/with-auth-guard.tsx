import { AuthGuard } from '@/components/features/auth/guards/auth-guard';

/**
 * Higher-order component that wraps a given component with an authentication guard.
 *
 * This ensures that the wrapped component is only accessible to authenticated users.
 * If the user is not authenticated, they will be redirected as configured in the
 * `AuthGuard` component.
 *
 * @prop {React.ComponentType<P>} Component - The component to be wrapped with the authentication guard.
 */
export const withAuthGuard = <P extends object>(Component: React.ComponentType<P>) => {
  return function WithAuthGuard(props: P) {
    return (
      <AuthGuard>
        <Component {...props} />
      </AuthGuard>
    );
  };
};

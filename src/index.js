import { lambda<PERSON><PERSON><PERSON>, localHandler } from "./handler.js";
import { MongoClient } from "mongodb";

let client;

/**
 * Initializes the MongoDB database connection.
 * 
 * Sets up the MongoDB client with appropriate authentication options
 * based on the current environment. In production, configures AWS IAM
 * authentication using environment variables.
 * 
 * No need manual connect as recommended by MongoDB documentation
 * https://www.mongodb.com/docs/atlas/manage-connections-aws-lambda/#connection-example
 * 
 * @async
 * @returns {Promise<void>} A promise that resolves when the database client is initialized
 */
export const initDb = async() => {
  let options = {};
  if (process.env.APP_ENV === "production") {
    options = {
      auth: {
        username: process.env.AWS_ACCESS_KEY_ID,
        password: process.env.AWS_SECRET_ACCESS_KEY
      },
      authSource: '$external',
      authMechanism: 'MONGODB-AWS'
    };
  }
  client = new MongoClient(process.env.MONGO_URI, options);
}

await initDb();

/**
 * Mocks the MongoDB client for unit testing purposes.
 * 
 * This function replaces the global MongoDB client instance with a mock object,
 * allowing tests to run without connecting to an actual database.
 *
 * @param {Object} clientMock - The mock object to replace the MongoDB client.
 * @returns {void} This function does not return a value.
 */
export const mockForUnitTest = (clientMock) => {
  client = clientMock;
}

/**
 * Handles incoming MSK events and processes them based on the environment.
 *
 * This function determines the environment (production or local) and calls
 * the appropriate handler function (`lambdaHandler` for production and
 * `localHandler` for local) to process the event.
 *
 * @param {Object} event - The MSK event data passed to the handler.
 * @param {Object} context - The lambda context in which the handler is executed.
 * @returns {Promise<string>} A promise that resolves to a string indicating completion.
 */
export const handler = async (event, context) => {

  // Lambda and local kafka used different subscription method
  if (process.env.APP_ENV === "production") {
    await lambdaHandler(event, client);
  } else {
    await localHandler(context, client);
  }

  return "Done";
};

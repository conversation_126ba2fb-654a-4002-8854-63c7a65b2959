import { useRefMounted } from '@/hooks/lifecycle';
import isEqual from 'lodash.isequal';
import { createContext, useCallback, useEffect, useMemo, useState, type JSX } from 'react';
import { DEFAULT_CUSTOMIZATION, INITIAL_CUSTOMIZATION_STATE } from './customization.constant';
import {
  type Customization,
  type CustomizationContextValue,
  type CustomizationProviderProps,
  type CustomizationState,
} from './customization.type';

/**
 * Context for customization settings
 *
 * This context provides access to the current customization settings and
 * functions to manage customization throughout the application.
 */
/**
 * Default context value with empty handlers
 */
const defaultContextValue: CustomizationContextValue = {
  ...DEFAULT_CUSTOMIZATION,
  ...INITIAL_CUSTOMIZATION_STATE,
  handleReset: () => {},
  handleUpdate: () => {},
  isCustom: false,
};

/**
 * Context for customization settings
 */
export const CustomizationContext = createContext<CustomizationContextValue>(defaultContextValue);

/**
 * Name for the context, useful for debugging
 */
CustomizationContext.displayName = 'CustomizationContext';

export const CustomizationConsumer = CustomizationContext.Consumer;

/**
 * Provider component for customization context
 *
 * This component initializes and manages the customization settings, providing
 * customization-related data and functions to its children components.
 */
export const CustomizationProvider = ({
  children,
  onReset = () => {},
  onUpdate = () => {},
  settings: initialCustomization,
}: CustomizationProviderProps): JSX.Element => {
  // State for customization data
  const [state, setState] = useState<CustomizationState>(INITIAL_CUSTOMIZATION_STATE);
  const isMounted = useRefMounted();

  const safeSetState = useCallback(
    (updater: (prev: CustomizationState) => CustomizationState) => {
      if (isMounted()) {
        setState(updater);
      }
    },
    [isMounted]
  );

  // Combine default and initial customization settings
  const settings = useMemo(() => {
    return {
      ...DEFAULT_CUSTOMIZATION,
      ...initialCustomization,
    } as Customization;
  }, [initialCustomization]);

  /**
   * Update customization settings
   */
  const handleUpdate = useCallback(
    (newCustomization: Customization): void => {
      onUpdate({
        colorPreset: settings.colorPreset,
        direction: settings.direction,
        layout: settings.layout,
        paletteMode: settings.paletteMode,
        stretch: settings.stretch,
        ...newCustomization,
      });
    },
    [onUpdate, settings]
  );

  /**
   * Check if current settings are different from defaults
   */
  const isCustom = useMemo(() => {
    return !isEqual(initialCustomization, {
      colorPreset: settings.colorPreset,
      direction: settings.direction,
      layout: settings.layout,
      paletteMode: settings.paletteMode,
      stretch: settings.stretch,
    });
  }, [settings, initialCustomization]);

  /**
   * Initialize the context on first render
   */
  useEffect(() => {
    safeSetState((prev) => ({ ...prev, isInitialized: true }));
  }, [safeSetState]);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(
    () => ({
      ...settings,
      ...state,
      handleReset: onReset,
      handleUpdate,
      isCustom,
    }),
    [settings, state, onReset, handleUpdate, isCustom]
  );

  return (
    <CustomizationContext.Provider value={contextValue}>{children}</CustomizationContext.Provider>
  );
};

export default CustomizationProvider;

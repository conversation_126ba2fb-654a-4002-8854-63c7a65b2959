import type { ColorPreset } from '@/theme';
import type { Direction, PaletteMode } from '@mui/material';
import type { ReactNode } from 'react';

/**
 * Layout options for the application
 */
export type Layout =
  | 'vertical-shells-dark'
  | 'vertical-shells-dark-alternate'
  | 'vertical-shells-brand'
  | 'vertical-shells-white'
  | 'vertical-shells-white-off'
  | 'vertical-shells-light'
  | 'vertical-shells-accent-header';

/**
 * Customization settings interface
 */
export interface Customization {
  /** The color preset to use */
  colorPreset?: ColorPreset;

  /** The direction of the layout */
  direction?: Direction;

  /** The layout to use */
  layout?: Layout;

  /** The palette mode to use */
  paletteMode?: PaletteMode;

  /** Whether to stretch the layout to the viewport height */
  stretch?: boolean;
}

/**
 * Customization state interface
 */
export interface CustomizationState extends Customization {
  /** Whether the context has been initialized */
  isInitialized: boolean;
}

/**
 * Customization context value interface
 */
export interface CustomizationContextValue extends CustomizationState {
  /** A function to reset the customization settings to their default values */
  handleReset: () => void;

  /** A function to update the customization settings */
  handleUpdate: (settings: Customization) => void;

  /** Whether the customization settings are custom (i.e., not the default values) */
  isCustom: boolean;
}

/**
 * Props for the CustomizationProvider component
 */
export interface CustomizationProviderProps {
  /** The children to render */
  children?: ReactNode;

  /** A function to call when the user resets the customization settings */
  onReset?: () => void;

  /** A function to call when the user updates the customization settings */
  onUpdate?: (settings: Customization) => void;

  /** The customization settings to use */
  settings?: Customization;
}

import type { Customization, CustomizationState } from './customization.type';

/**
 * Default customization settings
 */
export const DEFAULT_CUSTOMIZATION: Customization = {
  colorPreset: 'ultraViolet',
  direction: 'ltr',
  layout: 'vertical-shells-light',
  paletteMode: 'light',
  stretch: false,
};

/**
 * Initial customization state
 */
export const INITIAL_CUSTOMIZATION_STATE: CustomizationState = {
  isInitialized: false,
};

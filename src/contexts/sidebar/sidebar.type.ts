import type { ReactNode } from 'react';

/**
 * Sidebar state interface
 */
export interface SidebarState {
  /** A boolean indicating whether the sidebar is collapsed or not */
  isSidebarCollapsed: boolean;

  /** A boolean indicating whether the sidebar is being hovered or not */
  isSidebarHovered: boolean;
}

/**
 * Sidebar context value interface
 */
export interface SidebarContextValue extends SidebarState {
  /** A function to toggle the sidebar collapsed state */
  toggleSidebarCollapsed: () => void;

  /** A function to toggle the sidebar hover state */
  toggleSidebarHover: (hovered: boolean) => void;
}

/**
 * Props for the SidebarProvider component
 */
export interface SidebarProviderProps {
  /** The child components to be wrapped by the SidebarProvider */
  children: ReactNode;
}

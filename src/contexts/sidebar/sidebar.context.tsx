import { useRefMounted } from '@/hooks/lifecycle';
import { usePathname } from '@/hooks/navigation/use-pathname.hook';
import { createContext, useCallback, useEffect, useMemo, useState, type JSX } from 'react';
import { INITIAL_SIDEBAR_STATE } from './sidebar.constant';
import {
  type SidebarContextValue,
  type SidebarProviderProps,
  type SidebarState,
} from './sidebar.type';

/**
 * Default context value with empty handlers
 */
export const defaultContextValue: SidebarContextValue = {
  ...INITIAL_SIDEBAR_STATE,
  toggleSidebarCollapsed: () => {},
  toggleSidebarHover: () => {},
};

/**
 * Context for sidebar state and functions
 *
 * This context provides access to the current sidebar state and
 * functions to manage the sidebar throughout the application.
 */
export const SidebarContext = createContext<SidebarContextValue>(defaultContextValue);

/**
 * Name for the context, useful for debugging
 */
SidebarContext.displayName = 'SidebarContext';

export const SidebarConsumer = SidebarContext.Consumer;

/**
 * Provider component for sidebar context
 *
 * This component initializes and manages the sidebar state, providing
 * sidebar-related data and functions to its children components.
 *
 * The component also handles hover state persistence on route
 * changes and provides memoized values to the context.
 *
 * @example
 * ```tsx
 * function App() {
 *   return (
 *     <SidebarProvider>
 *       <Layout>
 *         <Sidebar />
 *         <Content />
 *       </Layout>
 *     </SidebarProvider>
 *   );
 * }
 * ```
 */
export const SidebarProvider = ({ children }: SidebarProviderProps): JSX.Element => {
  // State for sidebar data
  const [state, setState] = useState<SidebarState>(INITIAL_SIDEBAR_STATE);
  const isMounted = useRefMounted();
  const pathname = usePathname();

  const safeSetState = useCallback(
    (updater: (prev: SidebarState) => SidebarState) => {
      if (isMounted()) {
        setState(updater);
      }
    },
    [isMounted]
  );

  /**
   * Toggle the sidebar collapsed state
   */
  const toggleSidebarCollapsed = useCallback(() => {
    safeSetState((prev) => ({
      ...prev,
      isSidebarCollapsed: !prev.isSidebarCollapsed,
    }));
  }, [safeSetState]);

  /**
   * Toggle the sidebar hover state
   */
  const toggleSidebarHover = useCallback(
    (hovered: boolean) => {
      safeSetState((prev) => {
        if (prev.isSidebarCollapsed) {
          return {
            ...prev,
            isSidebarHovered: hovered,
          };
        }
        return prev;
      });
    },
    [safeSetState]
  );

  /**
   * Handle hover state persistence on route changes
   */
  useEffect(() => {
    safeSetState((prev) => {
      if (prev.isSidebarCollapsed) {
        return {
          ...prev,
          isSidebarHovered: false,
        };
      }
      return prev;
    });
  }, [pathname, safeSetState]);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(
    () => ({
      isSidebarCollapsed: state.isSidebarCollapsed,
      isSidebarHovered: state.isSidebarHovered,
      toggleSidebarCollapsed,
      toggleSidebarHover,
    }),
    [state.isSidebarCollapsed, state.isSidebarHovered, toggleSidebarCollapsed, toggleSidebarHover]
  );

  return <SidebarContext.Provider value={contextValue}>{children}</SidebarContext.Provider>;
};

export default SidebarProvider;

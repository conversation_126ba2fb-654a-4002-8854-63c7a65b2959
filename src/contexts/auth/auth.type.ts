import type { User } from '@/services/users';
import type { ReactNode } from 'react';

/**
 * Authentication state interface
 */
export interface AuthState {
  /** The current authenticated user, or `null` if not authenticated */
  user: User | null;

  /** An error message if an error occurred, or `null` */
  error: string | null;

  /** A boolean indicating if the authentication state is still being checked */
  isLoading: boolean;

  /** The access ID of the authenticated user, or `null` */
  accessId: string | null;
}

/**
 * Authentication context value interface
 */
export interface AuthContextValue extends AuthState {
  /** A function to manually check the authentication state */
  checkSession: () => Promise<void>;

  /** A function to sign in with username and password */
  login: (
    accessId: string,
    username: string,
    password: string
  ) => Promise<{ success: boolean; error?: string }>;

  /** A function to sign out the current user */
  logout: () => Promise<void>;

  /** A function to reset the password for an email */
  resetPassword: (email: string) => Promise<{ success: boolean; error?: string }>;
}

/**
 * Props for the AuthProvider component
 */
export interface AuthProviderProps {
  /** The child components to be wrapped by the AuthProvider */
  children: ReactNode;
}

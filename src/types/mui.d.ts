import '@mui/material/styles';
import '@mui/material/Button';
import '@mui/material/Chip';
import '@mui/material/Badge';
import '@mui/material/Avatar';
import '@mui/material/Typography';
import '@mui/material/IconButton';
import '@mui/material/SvgIcon';
import { type PaletteColorKey } from '@/theme/colors';

/**
 * Extend the Material UI theme to include custom colors
 */
declare module '@mui/material/styles' {
  interface Palette {
    // Custom colors are already defined in src/theme/index.ts
  }

  interface PaletteOptions {
    // Custom colors are already defined in src/theme/index.ts
  }
}

/**
 * Create a mapped type that converts all custom PaletteColorKey values to 'true'
 * This excludes the standard MUI colors which are already included
 */
type CustomColorMap = {
  [K in Exclude<
    PaletteColorKey,
    'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info'
  >]: true;
};

/**
 * Extend various MUI components to include custom colors
 */

// Button
declare module '@mui/material/Button' {
  interface ButtonPropsColorOverrides extends CustomColorMap {}
}

// Chip
declare module '@mui/material/Chip' {
  interface ChipPropsColorOverrides extends CustomColorMap {}
}

// Badge
declare module '@mui/material/Badge' {
  interface BadgePropsColorOverrides extends CustomColorMap {}
}

// Avatar
declare module '@mui/material/Avatar' {
  interface AvatarPropsColorOverrides extends CustomColorMap {}
}

// Typography
declare module '@mui/material/Typography' {
  interface TypographyPropsColorOverrides extends CustomColorMap {}
}

// IconButton
declare module '@mui/material/IconButton' {
  interface IconButtonPropsColorOverrides extends CustomColorMap {}
}

// SvgIcon
declare module '@mui/material/SvgIcon' {
  interface SvgIconPropsColorOverrides extends CustomColorMap {}
}

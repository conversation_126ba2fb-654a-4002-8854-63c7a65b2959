/**
 * API response types that match the backend response format
 */

/**
 * Base metadata interface for API responses
 */
export interface ResponseMeta {
  /**
   * Optional details about the response
   */
  details?: string | string[];

  /**
   * Any additional metadata properties
   */
  [key: string]: any;
}

/**
 * Success response interface
 */
export interface SuccessResponse<T = any> {
  /**
   * Success message
   */
  message?: string;

  /**
   * Response data
   */
  data?: T;

  /**
   * Additional metadata
   */
  meta?: ResponseMeta;
}

/**
 * Error response interface
 */
export interface ErrorResponse {
  /**
   * Error message
   */
  message?: string;

  /**
   * Error code
   */
  errorCode?: string;

  /**
   * Additional metadata
   */
  meta?: ResponseMeta;
}

/**
 * Generic API response type
 * Can be either a success or error response
 */
export type ApiResponse<T = any> = SuccessResponse<T> | ErrorResponse;

/**
 * Type guard to check if a response is a success response
 */
export function isSuccessResponse<T>(response: ApiResponse<T>): response is SuccessResponse<T> {
  return 'data' in response;
}

/**
 * Type guard to check if a response is an error response
 */
export function isErrorResponse(response: ApiResponse): response is ErrorResponse {
  return 'errorCode' in response;
}

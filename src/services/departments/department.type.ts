import { type ApiResponse } from '@/types/api-responses.type';

export type DepartmentStatus = 'active' | 'inactive';

export interface Department {
  id: string;

  name: string;
  description?: string;

  status?: DepartmentStatus;

  createdBy?: string;
  createdDate: Date;
  updatedBy?: string;
  updatedDate: Date;
  [key: string]: any;
}

/**
 * Filter parameters for users
 */
export interface DepartmentFilterParams {
  filters: {
    searchName: string;
    status: string[];
    updatedDate: Date | null;
    createdDate: Date | null;
  };
}

/**
 * User service interface
 */
export interface DepartmentService {
  /**
   * Get filtered users based on provided filters
   * @param filters - Filter parameters
   * @returns Promise with filtered users and error state
   */
  getFilteredDepartments(filters: DepartmentFilterParams): Promise<ApiResponse<Department[]>>;

  // /**
  //  * Delete a user by ID
  //  * @param id - User ID
  //  * @returns Promise with success status and message
  //  */
  // deleteUser(id: string | number): Promise<ApiResponse<boolean>>;
}

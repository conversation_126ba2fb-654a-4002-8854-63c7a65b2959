import axiosInstance from '@/providers/axios/axios';
import { errorService } from '@/services/error/error.service';
import type {
  PersonalFormData,
  SafetyFormData,
  Setting,
  SettingCategory,
  SettingOption,
  SettingService,
  ThemesFormData,
} from '@/services/settings/setting.type';
import { type ApiResponse } from '@/types/api-responses.type';

/**
 * Module API prefix
 */
export const PREFIX = '/settings';

/**
 * Setting service for handling setting-related operations
 */
class SettingServiceImpl implements SettingService {
  /**
   * Retrieves settings for a specific category
   *
   * @param category - The setting category to retrieve
   * @returns Promise resolving to an array of settings
   */
  getSettings = async (category: SettingCategory): Promise<ApiResponse<Setting[]>> => {
    try {
      const response = await axiosInstance.get(`${PREFIX}/${category}`);
      return response.data;
    } catch (error) {
      return errorService.handleError(error);
    }
  };

  /**
   * Retrieves available options for settings in a specific category
   *
   * @param category - The setting category to get options for
   * @returns Promise resolving to setting options
   */
  getSettingOptions = async (category: SettingCategory): Promise<ApiResponse<SettingOption>> => {
    try {
      const response = await axiosInstance.get(`${PREFIX}/${category}/options`);
      return response.data;
    } catch (error) {
      return errorService.handleError(error);
    }
  };

  /**
   * Updates personal settings for the current user
   *
   * @param formData - Personal setting form data
   * @returns Promise resolving to the update result
   */
  updatePersonalSetting = async (formData: PersonalFormData): Promise<ApiResponse> => {
    try {
      const response = await axiosInstance.put(`${PREFIX}/personal`, formData);
      return response.data;
    } catch (error) {
      return errorService.handleError(error);
    }
  };

  /**
   * Updates safety and security settings
   *
   * @param formData - Safety setting form data
   * @returns Promise resolving to the update result
   */
  updateSafetySetting = async (formData: SafetyFormData): Promise<ApiResponse> => {
    try {
      const response = await axiosInstance.put(`${PREFIX}/safety`, formData);
      return response.data;
    } catch (error) {
      return errorService.handleError(error);
    }
  };

  /**
   * Updates theme and UI settings
   *
   * @param formData - Theme setting form data
   * @returns Promise resolving to the update result
   */
  updateThemesSetting = async (formData: ThemesFormData): Promise<ApiResponse> => {
    try {
      const response = await axiosInstance.put(`${PREFIX}/themes`, formData);
      return response.data;
    } catch (error) {
      return errorService.handleError(error);
    }
  };
}

export const settingService = new SettingServiceImpl();

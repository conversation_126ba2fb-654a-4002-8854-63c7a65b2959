import { errorService } from '@/services/error/error.service';
import { AxiosError } from 'axios';
import { beforeEach, describe, expect, it, vi } from 'vitest';

describe('Error service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('handleError', () => {
    it('should return standardized error response when error is AxiosError', () => {
      const mockError = new AxiosError(
        'Network error',
        'ERR_BAD_REQUEST',
        {} as any,
        {},
        {
          data: { message: 'API error', errorCode: '10000' },
          status: 400,
          statusText: 'Bad request',
          headers: {},
          config: {} as any,
        }
      );

      const result = errorService.handleError(mockError);

      expect(result).toEqual({ message: 'API error', errorCode: '10000' });
    });

    it('should return axios error message when response is empty', () => {
      const mockError = new AxiosError(
        'Network error',
        'ERR_BAD_REQUEST',
        {} as any,
        {},
        {
          data: {},
          status: 400,
          statusText: 'Bad request',
          headers: {},
          config: {} as any,
        }
      );

      const result = errorService.handleError(mockError);

      expect(result).toEqual({
        message: 'Network error',
        errorCode: 'UNKNOWN_API_ERROR',
      });
    });

    it('should return default error message when the error is not AxiosError', () => {
      const mockError = new Error('mock non axios error');

      const result = errorService.handleError(mockError);

      expect(result).toEqual({
        message: 'An unexpected error occurred',
        errorCode: 'UNKNOWN_ERROR',
      });
    });
  });
});

import { type ApiResponse } from '@/types/api-responses.type';
import type { DepartmentStatus } from '../departments';

export type DepartmentTemplateStatus = DepartmentStatus;

export type DepartmentTemplateScope = 'organisation' | 'merchant';

export interface DepartmentTemplate {
  id: string;

  name: string;
  description?: string;
  scope?: DepartmentTemplateScope;

  status?: DepartmentTemplateStatus;

  createdBy?: string;
  createdDate: Date;
  updatedBy?: string;
  updatedDate: Date;
  [key: string]: any;
}

/**
 * Filter parameters for users
 */
export interface DepartmentTemplateFilterParams {
  filters: {
    searchName: string;
    scope: DepartmentTemplateScope[];
    status: string[];
    updatedDate: Date | null;
    createdDate: Date | null;
  };
}

/**
 * User service interface
 */
export interface DepartmentTemplateService {
  /**
   * Get filtered users based on provided filters
   * @param filters - Filter parameters
   * @returns Promise with filtered users and error state
   */
  getFilteredDepartmentTemplates(
    filters: DepartmentTemplateFilterParams
  ): Promise<ApiResponse<DepartmentTemplate[]>>;

  // /**
  //  * Delete a user by ID
  //  * @param id - User ID
  //  * @returns Promise with success status and message
  //  */
  // deleteUser(id: string | number): Promise<ApiResponse<boolean>>;
}

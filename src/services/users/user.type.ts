import { type ApiResponse } from '@/types/api-responses.type';

export type UserStatus = 'active' | 'inactive' | 'suspended';

export interface User {
  id: string;
  accessId: string;
  avatar?: string;
  email?: string;
  name?: string;
  jobtitle?: string;
  username?: string;
  location?: string;
  role?: string;
  coverImg?: string;
  followers?: string;
  description?: string;
  posts?: number;
  status?: UserStatus;
  [key: string]: any;
}

/**
 * Filter parameters for users
 */
export interface UserFilterParams {
  filters: {
    status: string[];
    role: string[];
  };
}

/**
 * User service interface
 */
export interface UserService {
  /**
   * Get filtered users based on provided filters
   * @param filters - Filter parameters
   * @returns Promise with filtered users and error state
   */
  getFilteredUsers(filters: UserFilterParams): Promise<ApiResponse<User[]>>;

  /**
   * Delete a user by ID
   * @param id - User ID
   * @returns Promise with success status and message
   */
  deleteUser(id: string | number): Promise<ApiResponse<boolean>>;
}

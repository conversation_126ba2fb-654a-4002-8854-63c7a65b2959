import { usersApi } from '@/mocks/users.mock';
import { type ApiResponse } from '@/types/api-responses.type';
import { wait } from '@/utils/wait.util';
import type { User, UserFilterParams, UserService } from './user.type';

/**
 * User service for handling user-related operations
 */
class UserServiceImpl implements UserService {
  /**
   * Get filtered users based on provided filters
   * @param filters - Filter parameters
   * @returns Promise with filtered users and error state
   */
  getFilteredUsers = async (filters: UserFilterParams): Promise<ApiResponse<User[]>> => {
    try {
      // Get users from mock API
      const response = await usersApi.getUsers();

      // Apply filters to the response
      let filteredUsers = response;

      // Apply status filter
      if (filters.filters.status.length > 0) {
        filteredUsers = filteredUsers.filter(
          (user) => user.status !== undefined && filters.filters.status.includes(user.status)
        );
      }

      // Apply role filter
      if (filters.filters.role.length > 0) {
        filteredUsers = filteredUsers.filter(
          (user) => user.role !== undefined && filters.filters.role.includes(user.role)
        );
      }

      // Simulate network delay
      await wait(500);

      return {
        data: filteredUsers,
        message: 'Users retrieved successfully',
        meta: {
          totalCount: filteredUsers.length,
        },
      };
    } catch (err) {
      return {
        message: err instanceof Error ? err.message : 'Unknown error',
        errorCode: 'ERR_FETCH_USERS',
      };
    }
  };

  /**
   * Delete a user by ID
   * @param id - User ID
   * @returns Promise with success status and message
   */
  deleteUser = async (id: string | number): Promise<ApiResponse<boolean>> => {
    try {
      // In a real application, you would call an API here
      // await api.delete(`/users/${id}`);

      // Simulate network delay
      await wait(500);

      return {
        data: true,
        message: `User ${id} deleted successfully`,
      };
    } catch (err) {
      return {
        message: err instanceof Error ? err.message : 'Failed to delete user',
        errorCode: 'ERR_DELETE_USER',
      };
    }
  };
}

export const userService = new UserServiceImpl();

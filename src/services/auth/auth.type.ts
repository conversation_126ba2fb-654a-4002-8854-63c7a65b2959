import { type ApiResponse } from '@/types/api-responses.type';
import type { User } from '../users/user.type';

/**
 * Login parameters
 */
export interface LoginParams {
  accessId: string;
  username: string;
  password: string;
}

/**
 * Reset password parameters
 */
export interface ResetPasswordParams {
  email: string;
}

/**
 * Auth service interface
 */
export interface AuthService {
  /**
   * Sign in with email and password
   */
  signInWithPassword(params: LoginParams): Promise<ApiResponse<void>>;

  /**
   * Reset password
   */
  resetPassword(params: ResetPasswordParams): Promise<ApiResponse<void>>;

  /**
   * Get current user
   */
  getUser(): Promise<ApiResponse<User | null>>;

  /**
   * Sign out
   */
  signOut(): Promise<ApiResponse<void>>;
}

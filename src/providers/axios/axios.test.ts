import axios from 'axios';
import { beforeEach, describe, expect, it, vi } from 'vitest';

vi.mock('axios', async () => {
  const actual = await vi.importActual('axios');

  return {
    ...actual,
    default: {
      create: vi.fn(() => ({
        interceptors: {
          request: { use: vi.fn() },
          response: { use: vi.fn() },
        },
      })),
    },
  };
});

describe('axios', () => {
  let mockAxios: any;
  let requestInterceptor: any;
  let requestErrorHandler: any;
  let responseInterceptor: any;
  let responseErrorHandler: any;
  let config: object;

  beforeEach(async () => {
    vi.resetAllMocks();
    vi.resetModules();

    vi.spyOn(Storage.prototype, 'getItem');
    vi.spyOn(Storage.prototype, 'setItem');
    global.window = Object.create(window);
    Object.defineProperty(window, 'location', {
      value: {
        pathname: '/123456789012/personal-setting',
      },
    });

    config = { headers: {} };
    process.env.NEXT_PUBLIC_API_URL = 'https://api.example.com';

    mockAxios = vi.mocked(axios, true);
    await import('./axios');

    const mockAxiosInstance = mockAxios.create.mock.results[0].value;

    requestInterceptor = mockAxiosInstance.interceptors.request.use.mock.calls[0][0];
    requestErrorHandler = mockAxiosInstance.interceptors.request.use.mock.calls[0][1];

    responseInterceptor = mockAxiosInstance.interceptors.response.use.mock.calls[0][0];
    responseErrorHandler = mockAxiosInstance.interceptors.response.use.mock.calls[0][1];
  });

  it('should create an axios instance with the correct base URL', async () => {
    expect(mockAxios.create).toHaveBeenCalledWith({
      baseURL: process.env.NEXT_PUBLIC_API_URL,
    });
  });

  it('should add JWT token to request headers if available', () => {
    const mockToken = 'mock-jwt-token';
    Storage.prototype.getItem = vi
      .fn()
      .mockImplementation((key) => (key === 'jwtToken' ? mockToken : null));

    const result = requestInterceptor(config);

    expect(result.headers['Authorization']).toEqual(`Bearer ${mockToken}`);
  });

  it('should add language to request headers if available', () => {
    Storage.prototype.getItem = vi
      .fn()
      .mockImplementation((key) => (key === 'i18nextLng' ? 'zh' : null));

    const result = requestInterceptor(config);

    expect(result.headers['Accept-Language']).toEqual('zh');
  });

  it('should use default language "en" if not set', () => {
    Storage.prototype.getItem = vi.fn().mockReturnValue(null);

    const result = requestInterceptor(config);

    expect(result.headers['Accept-Language']).toEqual('en');
  });

  it('should add access id to request headers if has window object', () => {
    const result = requestInterceptor(config);

    expect(result.headers['x-access-id']).toEqual('123456789012');
  });

  it('should not add access id when no window object', () => {
    const originalWindow = global.window;
    global.window = undefined as any;

    const result = requestInterceptor(config);

    expect(result.headers['x-access-id']).toBeUndefined();

    global.window = originalWindow;
  });

  it('should not add access id when url format is invalid', () => {
    const originalWindow = global.window;

    global.window = Object.create(window);
    Object.defineProperty(global.window, 'location', {
      value: {
        pathname: '/not-access-id',
      },
    });

    const result = requestInterceptor(config);

    expect(result.headers['x-access-id']).toBeUndefined();

    global.window = originalWindow;
  });

  it('should reject request errors', async () => {
    const error = new Error('Request error');

    await expect(requestErrorHandler(error)).rejects.toThrow('Request error');
  });

  it('should pass through successful responses', () => {
    const response = { data: 'test' };
    const result = responseInterceptor(response);

    expect(result).toEqual(response);
  });

  it('should reject response errors', async () => {
    const error = new Error('Response error');

    await expect(responseErrorHandler(error)).rejects.toThrow('Response error');
  });
});

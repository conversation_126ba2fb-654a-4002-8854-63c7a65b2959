import axios, { AxiosError } from 'axios';

const axiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
});

axiosInstance.interceptors.request.use(
  (config) => {
    const newConfig = config;

    // Add JWT token
    const jwtToken = localStorage.getItem('jwtToken');
    if (jwtToken) {
      newConfig.headers['Authorization'] = `Bearer ${jwtToken}`;
    }

    // Add language
    const language = localStorage.getItem('i18nextLng') ?? 'en';
    newConfig.headers['Accept-Language'] = language;

    // Add access id
    if (typeof window !== 'undefined') {
      const accessIdMatch = /^\/(\d{12})\//.exec(window.location.pathname);
      if (accessIdMatch) {
        newConfig.headers['x-access-id'] = accessIdMatch[1];
      }
    }

    return newConfig;
  },
  (error) => {
    return Promise.reject(error as AxiosError);
  }
);

axiosInstance.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    return Promise.reject(error as AxiosError);
  }
);

export default axiosInstance;

import { beforeEach, describe, expect, it, vi } from 'vitest';

vi.mock('i18next-http-backend', () => ({
  default: {
    type: 'backend',
    init: vi.fn(),
    read: vi.fn((_language, _namespace, callback) => {
      callback(null, {});
      return null;
    }),
    create: vi.fn(),
    save: vi.fn(),
  },
}));

describe('i18n configuration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.resetModules();

    vi.stubEnv('NEXT_PUBLIC_S3_TRANSLATIONS_URL', 'mock url');
    vi.stubEnv('NODE_ENV', 'development');
    // cannot import in beforeEach or "disable debug mode" test will fail
  });

  it('should initialize with correct configuration', async () => {
    const i18nModule = await import('./i18n');
    const i18n = i18nModule.default;

    expect(i18n).toBeDefined();

    expect(i18n.options.supportedLngs).toEqual(['en', 'zh', 'cimode']);
    expect(i18n.options.fallbackLng).toEqual(['en']);
    expect(i18n.options.defaultNS).toEqual('string');
    expect(i18n.options.ns).toEqual(['string']);
    expect(i18n.options.interpolation!.escapeValue).toEqual(false);
    expect((i18n.options.backend as any).loadPath).toEqual(
      process.env.NEXT_PUBLIC_S3_TRANSLATIONS_URL
    );
    expect((i18n.options.backend as any).reloadInterval).toEqual(60000);
    expect(i18n.options.debug).toEqual(true);
    expect(i18n.options.preload).toEqual(['en', 'zh']);
  });

  it('should disable debug mode when env in production', async () => {
    vi.stubEnv('NODE_ENV', 'production');
    const i18nModule = await import('./i18n');
    const i18n = i18nModule.default;

    expect(i18n.options.debug).toEqual(false);
  });

  it('should log error when initialization fails', async () => {
    // Mock i18n for this test case
    vi.doMock('i18next', () => ({
      default: {
        use: vi.fn().mockReturnThis(),
        init: vi.fn().mockImplementation(() => {
          return Promise.reject(new Error('Initialization failed'));
        }),
      },
    }));
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    await import('./i18n');

    // Wait for promises to resolve/reject
    await new Promise(process.nextTick);

    expect(consoleSpy).toHaveBeenCalled();

    consoleSpy.mockRestore();
  });
});

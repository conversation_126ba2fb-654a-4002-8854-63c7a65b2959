import { render, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { Provider } from './Provider';
import '@testing-library/jest-dom';

vi.mock('@tanstack/react-query', async () => {
  const actual = await vi.importActual('@tanstack/react-query');
  return {
    ...actual,
    isServer: false,
    QueryClient: vi.fn(),
    QueryClientProvider: ({ children }: { children: React.ReactNode }) => (
      <div data-testid="query-client-provider">{children}</div>
    ),
  };
});

vi.mock('@tanstack/react-query-devtools', () => ({
  ReactQueryDevtools: ({ initialIsOpen }: { initialIsOpen: boolean }) => (
    <div
      data-testid="react-query-devtools"
      data-initial-open={initialIsOpen}
    />
  ),
}));

describe('QueryClient init', () => {
  const queryClient = vi.fn();

  beforeEach(() => {
    vi.resetAllMocks();
    vi.resetModules();
  });

  it('should create QueryClient with default options when no config provided', async () => {
    vi.doMock('@tanstack/react-query', async () => {
      const actual = await vi.importActual('@tanstack/react-query');
      return {
        ...actual,
        isServer: false,
        QueryClient: queryClient,
        QueryClientProvider: vi.fn(),
      };
    });

    const { Provider: TestProvider } = await import('./Provider');

    render(
      <TestProvider>
        <div data-testid="test-child">Test Content</div>
      </TestProvider>
    );

    expect(queryClient).toHaveBeenCalledWith(undefined);
  });

  it('should create QueryClient with provided config for server', async () => {
    vi.doMock('@tanstack/react-query', async () => {
      const actual = await vi.importActual('@tanstack/react-query');
      return {
        ...actual,
        isServer: true,
        QueryClient: queryClient,
        QueryClientProvider: vi.fn(),
      };
    });

    const { Provider: TestProvider } = await import('./Provider');

    render(
      <TestProvider>
        <div data-testid="test-child">Test Content</div>
      </TestProvider>
    );

    expect(queryClient).toHaveBeenCalledWith({
      defaultOptions: {
        queries: {
          staleTime: 10 * 1000,
        },
      },
    });
  });
});

describe('Provider component', () => {
  it('should render children within QueryClientProvider', () => {
    render(
      <Provider>
        <div data-testid="test-child">Test Content</div>
      </Provider>
    );

    expect(screen.getByTestId('query-client-provider')).toBeInTheDocument();
    expect(screen.getByTestId('test-child')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('should render ReactQueryDevtools with initialIsOpen set to false', () => {
    render(
      <Provider>
        <div data-testid="test-child">Test2 Content</div>
      </Provider>
    );

    const devtools = screen.getByTestId('react-query-devtools');
    expect(devtools).toBeInTheDocument();
    expect(devtools).toHaveAttribute('data-initial-open', 'false');
  });
});

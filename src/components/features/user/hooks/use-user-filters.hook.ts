import { useFilter } from '@/hooks/data/use-filter.hook';
import { type UserFilterParams } from '@/services/users';
import { useCallback, useState } from 'react';

/**
 * Hook for managing user filters
 * @returns Filter state and handlers
 */
export const useUserFilters = () => {
  // Filter state management using the useFilter hook
  const { filterState, updateFilter, resetFilters } = useFilter({
    status: [],
    role: [],
  });

  // Applied filters (what's actually used for filtering)
  const [appliedFilters, setAppliedFilters] = useState<UserFilterParams>({
    filters: {
      status: [],
      role: [],
    },
  });

  // Apply the current filter state to the applied filters
  const handleApplyFilters = useCallback(() => {
    setAppliedFilters({
      filters: {
        status: [...filterState.filters.status],
        role: [...filterState.filters.role],
      },
    });
  }, [filterState]);

  // Reset all filters
  const handleResetFilters = useCallback(() => {
    // Reset the filter state
    resetFilters();

    // Also reset the applied filters
    setAppliedFilters({
      filters: {
        status: [],
        role: [],
      },
    });
  }, [resetFilters]);

  // Handle filter changes from components
  const handleFilterChange = useCallback(
    (key: string, value: any) => {
      if (key === 'filters') {
        // When the entire filters object is updated from the filter component
        updateFilter('status', value.status);
        updateFilter('role', value.role);
      }
    },
    [updateFilter]
  );

  return {
    filterState,
    appliedFilters,
    handleApplyFilters,
    handleResetFilters,
    handleFilterChange,
  };
};

export default useUserFilters;

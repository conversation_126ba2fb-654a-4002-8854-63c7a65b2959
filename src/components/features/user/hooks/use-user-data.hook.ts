import { userService, type User, type UserFilterParams } from '@/services/users';
import { isSuccessResponse } from '@/types/api-responses.type';
import { useCallback, useEffect, useState } from 'react';
import toast from 'react-hot-toast';

/**
 * Hook for fetching and managing user data
 * @param filters - Filter parameters for users
 * @returns User data, loading state, and refresh function
 */
export const useUserData = (filters: UserFilterParams) => {
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch users with the provided filters
  const fetchUsers = useCallback(async () => {
    setIsLoading(true);

    const response = await userService.getFilteredUsers(filters);

    if (isSuccessResponse(response)) {
      setUsers(response.data || []);
    } else {
      toast.error(`Failed to fetch users: ${response.message}`);
    }

    setIsLoading(false);
  }, [filters]);

  // Fetch users when filters change
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  return {
    users,
    isLoading,
    refreshUsers: fetchUsers,
  };
};

export default useUserData;

import { userService } from '@/services/users';
import { isSuccessResponse } from '@/types/api-responses.type';
import { useCallback } from 'react';
import toast from 'react-hot-toast';

/**
 * Hook for user actions like create and delete
 * @param refreshCallback - Function to call after successful actions to refresh data
 * @returns Action handler functions
 */
export const useUserActions = (refreshCallback: () => Promise<void>) => {
  // Handle user deletion
  const handleDeleteUser = useCallback(
    async (id: string | number) => {
      const response = await userService.deleteUser(id);

      if (isSuccessResponse(response)) {
        toast.success(response.message ?? `User ID ${id} deleted successfully`);
        await refreshCallback();
      } else {
        toast.error(response.message ?? 'Failed to delete user');
      }
    },
    [refreshCallback]
  );

  // Handle user creation (placeholder for future implementation)
  const handleCreateUser = useCallback(() => {
    toast.success('Create user functionality would be implemented here');
  }, []);

  return {
    handleDeleteUser,
    handleCreateUser,
  };
};

export default useUserActions;

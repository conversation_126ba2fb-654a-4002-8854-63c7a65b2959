import { FilterActionButtons, FilterCheckbox } from '@/components/application-ui/filter-section';
import { Box } from '@mui/material';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { USER_ROLE_OPTIONS, USER_STATUS_OPTIONS } from './UserFilterPanel.constant';
import { type UserFilterPanelProps } from './UserFilterPanel.type';
import { toggleFilterValue } from './UserFilterPanel.util';

/**
 * UserFilterPanel component displays filter options for users
 *
 * @example
 * ```tsx
 * <UserFilterPanel
 *   filters={filterState}
 *   onFilterChange={handleFilterChange}
 *   onSearch={handleApplyFilters}
 *   onClear={handleResetFilters}
 * />
 * ```
 */
export const UserFilterPanel = ({
  filters,
  onFilterChange,
  onSearch,
  onClear,
}: UserFilterPanelProps) => {
  const { t } = useTranslation();

  // Handle status filter change
  const handleStatusChange = useCallback(
    (value: string) => {
      const newStatuses = toggleFilterValue(filters.filters.status, value);
      onFilterChange('filters', { ...filters.filters, status: newStatuses });
    },
    [filters.filters, onFilterChange]
  );

  // Handle role filter change
  const handleRoleChange = useCallback(
    (value: string) => {
      const newRoles = toggleFilterValue(filters.filters.role, value);
      onFilterChange('filters', { ...filters.filters, role: newRoles });
    },
    [filters.filters, onFilterChange]
  );

  return (
    <Box>
      <FilterActionButtons
        onSearch={onSearch}
        onClear={onClear}
      />

      <FilterCheckbox
        label={t('common.label.status')}
        options={USER_STATUS_OPTIONS.map((data: { value: string; labelKey: string }) => ({
          ...data,
          labelKey: t(data.labelKey),
        }))}
        selectedValues={filters.filters.status}
        onChange={handleStatusChange}
      />

      <FilterCheckbox
        label={t('common.label.role')}
        options={USER_ROLE_OPTIONS}
        selectedValues={filters.filters.role}
        onChange={handleRoleChange}
      />
    </Box>
  );
};

export default UserFilterPanel;

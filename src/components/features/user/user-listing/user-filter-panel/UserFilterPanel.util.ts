/**
 * Creates a new array of values with the given value toggled (added or removed)
 *
 * @param currentValues - The current array of values
 * @param value - The value to toggle
 * @returns A new array with the value toggled
 */
export const toggleFilterValue = (currentValues: string[], value: string): string[] => {
  return currentValues.includes(value)
    ? currentValues.filter((v) => v !== value)
    : [...currentValues, value];
};

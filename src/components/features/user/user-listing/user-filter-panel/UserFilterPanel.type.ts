import { type FilterState } from '@/hooks/data/use-filter.hook';

/**
 * Props for the UserFilterPanel component
 */
export interface UserFilterPanelProps {
  /**
   * The current filter state
   */
  filters: FilterState<{
    status: string[];
    role: string[];
  }>;

  /**
   * Callback fired when a filter is changed
   */
  onFilterChange: (key: string, value: any) => void;

  /**
   * Callback fired when the search button is clicked
   */
  onSearch: () => void;

  /**
   * Callback fired when the clear button is clicked
   */
  onClear: () => void;
}

// FilterSectionProps has been moved to application-ui/filter-section/CheckboxFilterSection.types.ts

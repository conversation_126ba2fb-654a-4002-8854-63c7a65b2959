import { type User } from '@/services/users';

/**
 * Props for the UserDataGrid component
 */
export interface UserDataGridProps {
  /**
   * Array of user data to display in the grid
   */
  users: User[];

  /**
   * Whether the data is currently loading
   */
  isLoading: boolean;

  /**
   * Callback fired when a user is deleted
   *
   * If not provided, the delete action will do nothing.
   * In a real application, this would typically make an API call to delete the user.
   *
   * @param id - The ID of the user to delete
   */
  onDeleteUser?: (id: string | number) => void;
}

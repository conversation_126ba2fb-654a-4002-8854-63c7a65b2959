import { DataGrid } from '@/components/base/data-display/datagrid';
import { useTheme } from '@mui/material';
import { type GridRowId } from '@mui/x-data-grid-premium';
import { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { INITIAL_STATE, TOOLBAR_PROPS } from './UserDataGrid.constant';
import { type UserDataGridProps } from './UserDataGrid.type';
import { createColumns } from './UserDataGrid.util';

/**
 * UserDataGrid component displays user data in a grid with filtering, sorting, and pagination.
 *
 * @example
 * ```tsx
 * <UserDataGrid
 *   users={users}
 *   isLoading={isLoading}
 *   onDeleteUser={handleDeleteUser}
 * />
 * ```
 */
export const UserDataGrid = ({ users, isLoading, onDeleteUser }: UserDataGridProps) => {
  const theme = useTheme();
  const { t } = useTranslation();

  const deleteUser = useCallback(
    (id: GridRowId) => () => {
      if (onDeleteUser) {
        onDeleteUser(id);
      }
    },
    [onDeleteUser]
  );

  // Memoize columns definition to prevent recreation on every render
  const columns = useMemo(() => createColumns(deleteUser, theme, t), [deleteUser, theme, t]);

  // Memoize initial state to prevent recreation
  const initialState = useMemo(() => INITIAL_STATE, []);

  return (
    <DataGrid
      loading={isLoading}
      rows={users}
      columns={columns}
      toolbarProps={TOOLBAR_PROPS}
      pagination
      initialState={initialState}
      rowHeight={65}
      getRowId={(row) => row.id}
      disableAutosize
    />
  );
};

export default UserDataGrid;

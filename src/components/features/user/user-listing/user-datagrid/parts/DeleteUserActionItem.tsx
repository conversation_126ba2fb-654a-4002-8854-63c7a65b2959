import { AlertDialog } from '@/components/base/feedback/dialog';
import { Button } from '@/components/base/inputs/button';
import { useDialog } from '@/hooks/ui/use-dialog.hook';
import { GridActionsCellItem, type GridActionsCellItemProps } from '@mui/x-data-grid-premium';

/**
 * A grid action cell item that shows a confirmation dialog when clicked
 * and calls the deleteUser function when confirmed.
 *
 * @example
 * ```tsx
 * <DeleteUserActionItem
 *   label="Delete"
 *   showInMenu
 *   deleteUser={() => handleDelete(userId)}
 * />
 * ```
 */
export const DeleteUserActionItem = ({
  deleteUser,
  ...props
}: GridActionsCellItemProps & { deleteUser: () => void }) => {
  const { open, handleOpen, handleClose } = useDialog();

  return (
    <>
      <GridActionsCellItem
        {...props}
        onClick={handleOpen}
      />
      <AlertDialog
        open={open}
        onClose={handleClose}
        title="You are about to permanently delete selected items"
        message="Deleting these items will also remove all associated data."
        note="This action cannot be undone."
        actions={
          <>
            <Button
              variant="text"
              color="secondary"
              onClick={handleClose}
            >
              cancel
            </Button>
            <Button
              color="error"
              variant="text"
              onClick={() => {
                handleClose();
                deleteUser();
              }}
              autoFocus
            >
              delete
            </Button>
          </>
        }
      />
    </>
  );
};

export default DeleteUserActionItem;

/**
 * Represents an OAuth provider used for social login.
 */
export interface OAuthProvider {
  id: 'google';
  name: string;
  logo: string;
}

/**
 * A list of available OAuth providers supported in the app.
 */
export const oAuthProviders: OAuthProvider[] = [
  {
    id: 'google',
    name: 'Google',
    logo: '/placeholders/logo/google-icon.svg',
  },
];

/**
 * Represents the state of an alert message shown to the user.
 * Can be used for success, error, warning, or informational messages.
 */
export type AlertState = {
  severity: 'error' | 'success' | 'info' | 'warning';
  title?: string;
  message?: string;
} | null;

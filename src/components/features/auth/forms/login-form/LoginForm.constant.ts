import type { AlertData } from '@/constants/alert.constant';
import { type TFunction } from 'i18next';

/**
 * A mapping of authentication error codes to their corresponding alert data.
 * This is used to provide user-friendly error messages based on specific authentication errors.
 */
const alertMap: Record<string, AlertData> = {
  '20001': { severity: 'error', title: 'error.auth.sentence.invalidCredentials' },
  '20002': { severity: 'error', title: 'error.auth.sentence.sessionExpired' },
  '20003': { severity: 'error', title: 'error.auth.sentence.accountInactive' },
  '20004': { severity: 'error', title: 'error.auth.sentence.suspiciousActivityDetected' },
  '20005': { severity: 'error', title: 'error.auth.sentence.accountLockedManyAttempts' },
  '20006': { severity: 'error', title: 'error.auth.sentence.accountLocked' },
  '20007': { severity: 'info', title: 'error.auth.sentence.2faSetupRequired' },
  '20008': { severity: 'info', title: 'error.auth.sentence.2faRequired' },
};

/**
 * Resolves authentication-related error codes to AlertData objects for displaying error messages to the user.
 *
 * @param errorCode - The error code received from the authentication service.
 * @param message - An optional custom error message to display to the user.
 *
 * @returns An AlertData object containing the severity, title, and message to be displayed to the user.
 *          If the errorCode is not recognized, a default error AlertData object is returned.
 *          If the message parameter is provided, it will be used as the fallback message.
 *          Otherwise, a default fallback message will be used.
 */
export const authAlertResolver =
  (t: TFunction) =>
  (errorCode?: string, message?: string): AlertData | null => {
    const alertInfo = errorCode ? alertMap[errorCode] : undefined;

    return {
      severity: alertInfo?.severity ?? 'error',
      title: alertInfo?.title ? t(alertInfo?.title) : t('error.label.failed'),
      message: message ? t(message) : t('error.auth.sentence.somethingWentWrongPleaseTryAgain'),
    };
  };

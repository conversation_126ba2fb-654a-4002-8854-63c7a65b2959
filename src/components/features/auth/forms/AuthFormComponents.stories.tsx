// AuthFormComponents.stories.tsx
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import React from 'react';
import { useForm } from 'react-hook-form';
import {
  AccessIdField,
  EmailField,
  FormHeader,
  OAuthButton,
  PasswordField,
  SubmitButton,
  UsernameField,
} from './AuthFormComponents';
import { type InputFieldProps } from './AuthFormComponents.type';

const meta: Meta = {
  title: 'Components/features/auth/forms/AuthFormComponent',
  component: FormHeader,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    title: {
      control: 'text',
      description: 'Title of the form header',
    },
    subtitle: {
      control: 'text',
      description: 'Subtitle of the form header',
    },
  },
} satisfies Meta<typeof FormHeader>;

export default meta;

type Story = StoryObj<typeof meta>;

/**
 * A higher-order component that wraps form fields with a common form structure.
 * It provides a form context using `react-hook-form` and handles form state.
 *
 * @param props - The component props.
 * @param props.children - The form fields to be wrapped.
 *
 * @returns A React component that renders a form with the provided form fields.
 */
const FormWrapper = ({ Component }: { Component: React.ComponentType<InputFieldProps> }) => {
  const {
    register,
    formState: { errors },
  } = useForm({
    defaultValues: {
      accessId: '',
      username: '',
      email: '',
      password: '',
    },
  });

  return (
    <form>
      <Component
        register={register}
        errors={errors}
        isPending={false}
      />
    </form>
  );
};

/**
 * Default title of the form header.
 */
export const Default: Story = {
  args: {
    title: 'Sign in',
    subtitle: 'Access your account and continue your journey',
  },
};

export const AccessId: Story = {
  render: () => <FormWrapper Component={AccessIdField} />,
};

export const Username: Story = {
  render: () => <FormWrapper Component={UsernameField} />,
};

export const Email: Story = {
  render: () => <FormWrapper Component={EmailField} />,
};

export const Password: Story = {
  render: () => <FormWrapper Component={PasswordField} />,
};

export const Submit: Story = {
  render: () => (
    <SubmitButton
      label="Sign In"
      isPending={false}
    />
  ),
};

export const OAuth: Story = {
  render: () => (
    <OAuthButton
      label="Sign in with Google"
      logo="/placeholders/logo/google-icon.svg"
      altText="Google"
      isPending={false}
    />
  ),
};

import { Button } from '@/components/base/inputs/button';
import { Typography } from '@/components/base/typography';
import { useToggle } from '@/hooks/ui/use-toggle.hook';
import { transformText } from '@/utils/text-transform.util';
import MailOutlineRoundedIcon from '@mui/icons-material/MailOutlineRounded';
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';
import {
  Alert,
  AlertTitle,
  Box,
  Container,
  FormControl,
  FormHelperText,
  Grid,
  InputAdornment,
  InputLabel,
  OutlinedInput,
  Stack,
} from '@mui/material';
import Image from 'next/image';
import { type ReactNode } from 'react';
import { useTranslation } from 'react-i18next';
import { type InputFieldProps } from './AuthFormComponents.type';

/**
 * Form header component for auth forms
 */
export const FormHeader = ({ title, subtitle }: { title: string; subtitle: string }) => (
  <Container maxWidth="sm">
    <Typography
      caseTransform="sentenceCase"
      align="center"
      variant="h3"
      gutterBottom
    >
      {title}
    </Typography>
    <Typography
      caseTransform="sentenceCase"
      align="center"
      variant="h6"
      fontWeight={400}
    >
      {subtitle}
    </Typography>
  </Container>
);

/**
 * Form container component for auth forms
 */
export const FormContainer = ({ children }: { children: ReactNode }) => (
  <Stack
    mt={{ xs: 2, sm: 3 }}
    justifyContent="center"
    alignItems="center"
    spacing={{ xs: 2, sm: 3 }}
  >
    {children}
  </Stack>
);

/**
 * Access ID field component for auth forms
 */
export const AccessIdField = ({ register, errors, isPending, dataTestId }: InputFieldProps) => {
  const { t } = useTranslation();
  const fullLabelText = `${t('common.label.accessId')} (${t('common.label.optional')})`;

  return (
    <Grid size={12}>
      <FormControl
        fullWidth
        error={Boolean(errors.accessId)}
      >
        <InputLabel htmlFor="accessId">{fullLabelText}</InputLabel>
        <OutlinedInput
          {...register('accessId')}
          id="accessId"
          label={fullLabelText}
          name="accessId"
          type="text"
          placeholder={fullLabelText}
          inputProps={{ maxLength: 12, 'data-testid': dataTestId ?? 'accessId' }}
          disabled={isPending}
          onInput={(e) => {
            const input = e.target as HTMLInputElement;
            input.value = input.value.replace(/\D/g, '');
          }}
        />
        {errors.accessId && <FormHelperText>{errors.accessId.message}</FormHelperText>}
      </FormControl>
    </Grid>
  );
};

/**
 * Username field component for auth forms
 */
export const UsernameField = ({ register, errors, isPending }: InputFieldProps) => {
  const { t } = useTranslation();
  const fullLabelText = transformText(t('common.label.username'), 'sentenceCase');

  return (
    <Grid size={12}>
      <FormControl
        fullWidth
        error={Boolean(errors.username)}
      >
        <InputLabel htmlFor="username">{fullLabelText}</InputLabel>
        <OutlinedInput
          {...register('username')}
          id="username"
          label={fullLabelText}
          name="username"
          type="text"
          placeholder={fullLabelText}
          disabled={isPending}
          inputProps={{ minLength: 3, maxLength: 50, 'data-testid': 'username' }}
        />
        {errors.username && <FormHelperText>{errors.username.message}</FormHelperText>}
      </FormControl>
    </Grid>
  );
};

/**
 * Email field component for auth forms
 */
export const EmailField = ({ register, errors, isPending }: InputFieldProps) => (
  <Grid size={12}>
    <FormControl
      fullWidth
      error={Boolean(errors.email)}
    >
      <InputLabel>Email</InputLabel>
      <OutlinedInput
        {...register('email')}
        label="Email"
        type="email"
        id="email-input"
        placeholder="Write your email"
        disabled={isPending}
        startAdornment={
          <InputAdornment position="start">
            <MailOutlineRoundedIcon fontSize="small" />
          </InputAdornment>
        }
      />
      {errors.email && <FormHelperText>{errors.email.message}</FormHelperText>}
    </FormControl>
  </Grid>
);

/**
 * Password field component for auth forms
 */
export const PasswordField = ({ register, errors, isPending }: InputFieldProps) => {
  const [showPassword, toggleShowPassword] = useToggle(false);
  const { t } = useTranslation();
  const fullLabelText = transformText(t('common.label.password'), 'sentenceCase');

  return (
    <Grid size={12}>
      <FormControl
        fullWidth
        error={Boolean(errors.password)}
      >
        <InputLabel htmlFor="password">{fullLabelText}</InputLabel>
        <OutlinedInput
          {...register('password')}
          id="password"
          label={fullLabelText}
          name="password"
          type={showPassword ? 'text' : 'password'}
          placeholder={fullLabelText}
          disabled={isPending}
          endAdornment={
            <InputAdornment position="end">
              <Button
                variant="outlined"
                color="secondary"
                sx={{ mr: -0.8 }}
                onClick={toggleShowPassword}
                customStyle="icon"
                data-testid="passwordToggle"
                disabled={isPending}
              >
                {showPassword ? (
                  <VisibilityOff fontSize="small" />
                ) : (
                  <Visibility fontSize="small" />
                )}
              </Button>
            </InputAdornment>
          }
          inputProps={{ 'data-testid': 'password' }}
        />
        {errors.password && <FormHelperText>{errors.password.message}</FormHelperText>}
      </FormControl>
    </Grid>
  );
};

/**
 * Submit button component for auth normal login forms
 */
export const SubmitButton = ({ label, isPending }: { label: string; isPending: boolean }) => (
  <Grid size={12}>
    <Button
      data-testid="submit"
      disabled={isPending}
      variant="contained"
      type="submit"
      size="large"
      fullWidth
    >
      {label}
    </Button>
  </Grid>
);

/**
 * Demo credentials component for login form
 * This component displays for testing purposes, will be removed later.
 */
export const DemoCredentials = () => (
  <Grid size={12}>
    <Alert severity="warning">
      <AlertTitle>Sign in credentials</AlertTitle>
      Username <b><EMAIL></b> and password <b>DemoPass123</b>
    </Alert>
  </Grid>
);

/**
 * Sign in button component for OAuth providers
 */
export const OAuthButton = ({
  label,
  logo,
  altText,
  isPending,
}: {
  label: string;
  logo: string;
  altText: string;
  isPending: boolean;
}) => (
  <Box
    borderRadius={1}
    padding={3}
    display="inline-flex"
    bgcolor="divider"
    justifyContent="center"
    width="100%"
  >
    <Button
      type="submit"
      disabled={isPending}
      sx={{
        whiteSpace: { xs: 'normal', sm: 'nowrap' },
        px: { xs: 1, sm: 5 },
        textAlign: 'left',
      }}
      variant="outlined"
      color="secondary"
      startIcon={
        <Image
          height={24}
          width={24}
          alt={altText}
          src={logo}
        />
      }
    >
      {label}
    </Button>
  </Box>
);

import { FormAlert } from '@/components/base/feedback/alert/FormAlert';
import { Button } from '@/components/base/inputs/button';
import { RouterLink } from '@/components/base/navigation/router-link';
import ROUTES from '@/router/routes';
import {
  defaultResetPasswordValues,
  resetPasswordSchema,
  type ResetPasswordFormValues,
} from '@/schemas/auth';
import { authService } from '@/services/auth/auth.service';
import { isSuccessResponse } from '@/types/api-responses.type';
import { zodResolver } from '@hookform/resolvers/zod';
import KeyboardBackspaceRoundedIcon from '@mui/icons-material/KeyboardBackspaceRounded';
import { Container, Grid } from '@mui/material';
import { useCallback, useState, type JSX } from 'react';
import { useForm } from 'react-hook-form';
import { EmailField, FormContainer, FormHeader, SubmitButton } from '../AuthFormComponents';

/**
 * ResetPasswordForm is a component that renders a form for users to reset their password.
 *
 * It includes:
 * - Email input field
 * - Form validation
 * - Error handling
 * - Link to return to sign in page
 *
 * @example
 * ```tsx
 * <ResetPasswordForm />
 * ```
 */
export function ResetPasswordForm(): JSX.Element {
  const [isPending, setIsPending] = useState<boolean>(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm<ResetPasswordFormValues>({
    defaultValues: defaultResetPasswordValues,
    resolver: zodResolver(resetPasswordSchema),
  });

  const onSubmit = useCallback(
    async (values: ResetPasswordFormValues): Promise<void> => {
      setIsPending(true);

      const response = await authService.resetPassword(values);

      if (!isSuccessResponse(response)) {
        setError('root', {
          type: 'server',
          message: response.message ?? 'Password reset failed',
        });
        setIsPending(false);
        return;
      }

      // Show success message
      setSuccessMessage(response.message ?? 'Password reset link has been sent to your email');
      setIsPending(false);
    },
    [setError]
  );

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <FormHeader
        title="Recover password"
        subtitle="Enter your email to reset your password"
      />
      <FormContainer>
        <Container maxWidth="sm">
          <Grid
            container
            spacing={2}
          >
            {/* Email Field */}
            <EmailField
              register={register}
              errors={errors}
              isPending={isPending}
            />

            {/* Submit Button */}
            <SubmitButton
              label="Send reset link"
              isPending={isPending}
            />

            {errors.root && (
              <Grid size={12}>
                <FormAlert
                  severity="error"
                  message={errors.root.message}
                />
              </Grid>
            )}

            {/* Success Message - This will be handled later in reset password task  */}
            {successMessage && (
              <Grid size={12}>
                <FormAlert
                  severity="error"
                  message={successMessage}
                />
              </Grid>
            )}

            {/* Back to Login */}
            <Grid size={12}>
              <Button
                component={RouterLink}
                href={ROUTES.AUTH['LOGIN']}
                size="large"
                startIcon={<KeyboardBackspaceRoundedIcon />}
              >
                Go to sign in
              </Button>
            </Grid>
          </Grid>
        </Container>
      </FormContainer>
    </form>
  );
}

export default ResetPasswordForm;

import { useAuth } from '@/hooks/auth/use-auth.hook';
import { useRouter } from '@/hooks/navigation/use-router.hook';
import ROUTES from '@/router/routes';
import { Alert } from '@mui/material';
import { useEffect, useState, type JSX } from 'react';
import { type GuestGuardProps } from './GuestGuard.type';

/**
 * GuestGuard is a component that protects routes from authenticated users.
 *
 * It checks if the user is authenticated and redirects to the dashboard if they are.
 * This component should be used to wrap authentication-related routes like login or signup.
 *
 * @example
 * ```tsx
 * // Direct usage
 * <GuestGuard>
 *   <LoginComponent />
 * </GuestGuard>
 *
 * // With HOC
 * const PublicPage = withGuestGuard(PageComponent);
 * ```
 */
export function GuestGuard({ children }: Readonly<GuestGuardProps>): JSX.Element | null {
  const router = useRouter();
  const { user, error, isLoading, accessId } = useAuth();
  const [isChecking, setIsChecking] = useState<boolean>(true);

  const checkPermissions = async (): Promise<void> => {
    if (isLoading) {
      return;
    }

    if (error) {
      setIsChecking(false);
      return;
    }

    if (user && accessId) {
      // User is logged in, redirecting to dashboard
      const dashboardRoute = ROUTES.DASHBOARD.replace('[accessId]', accessId);
      router.push(dashboardRoute);
      return;
    }

    setIsChecking(false);
  };

  useEffect(() => {
    checkPermissions().catch(() => {});
    // eslint-disable-next-line react-hooks/exhaustive-deps -- Expected
  }, [user, error, isLoading]);

  if (isChecking) {
    return null;
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  return <>{children}</>;
}

export default GuestGuard;

import { useAuth } from '@/hooks/auth/use-auth.hook';
import { useRouter } from '@/hooks/navigation/use-router.hook';
import ROUTES from '@/router/routes';
import { Alert } from '@mui/material';
import { useEffect, useState, type JSX } from 'react';
import { type AuthGuardProps } from './AuthGuard.type';

/**
 * AuthGuard is a component that protects routes from unauthenticated users.
 *
 * It checks if the user is authenticated and redirects to the login page if not.
 * This component should be used to wrap any routes that require authentication.
 *
 * @example
 * ```tsx
 * // Direct usage
 * <AuthGuard>
 *   <ProtectedComponent />
 * </AuthGuard>
 *
 * // With HOC
 * const ProtectedPage = withAuthGuard(PageComponent);
 * ```
 */
export function AuthGuard({ children }: Readonly<AuthGuardProps>): JSX.Element | null {
  const router = useRouter();
  const { user, error, isLoading } = useAuth();
  const [isChecking, setIsChecking] = useState<boolean>(true);

  const checkPermissions = async (): Promise<void> => {
    if (isLoading) {
      return;
    }

    if (error) {
      setIsChecking(false);
      return;
    }

    if (!user) {
      // User is not logged in, redirecting to sign in
      router.push(ROUTES.AUTH.LOGIN);
      return;
    }

    setIsChecking(false);
  };

  useEffect(() => {
    checkPermissions().catch(() => {});
    // eslint-disable-next-line react-hooks/exhaustive-deps -- Expected
  }, [user, error, isLoading]);

  if (isChecking) {
    return null;
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  return <>{children}</>;
}

export default AuthGuard;

import { useFilter } from '@/hooks/data/use-filter.hook';
import type { DepartmentTemplateFilterParams } from '@/services/department-templates';
import { useCallback, useState } from 'react';

const DEPARTMENT_TEMPLATE_DEFAULT_FILTER_STATE = {
  searchName: '',
  scope: [],
  status: [],
  createdDate: null,
  updatedDate: null,
};

export const getDepartmentTemplateDefaultFilteState = () =>
  structuredClone(DEPARTMENT_TEMPLATE_DEFAULT_FILTER_STATE);

/**
 * Hook for managing department filters
 * @returns Filter state and handlers
 */
export const useDepartmentFilters = () => {
  // Filter state management using the useFilter hook
  const { filterState, updateFilter, resetFilters } = useFilter<
    DepartmentTemplateFilterParams['filters']
  >(getDepartmentTemplateDefaultFilteState());

  // Applied filters (what's actually used for filtering)
  const [appliedFilters, setAppliedFilters] = useState<DepartmentTemplateFilterParams>({
    filters: getDepartmentTemplateDefaultFilteState(),
  });

  // Apply the current filter state to the applied filters
  const handleApplyFilters = useCallback(() => {
    setAppliedFilters({
      filters: {
        searchName: filterState.filters.searchName,
        scope: filterState.filters.scope,
        status: [...filterState.filters.status],
        createdDate: filterState.filters.createdDate,
        updatedDate: filterState.filters.updatedDate,
      },
    });
  }, [filterState]);

  // Reset all filters
  const handleResetFilters = useCallback(() => {
    // Reset the filter state
    resetFilters();

    // Also reset the applied filters
    setAppliedFilters({
      filters: getDepartmentTemplateDefaultFilteState(),
    });
  }, [resetFilters]);

  // Handle filter changes from components
  const handleFilterChange = useCallback(
    (key: string, value: any) => {
      if (key === 'filters') {
        // When the entire filters object is updated from the filter component
        updateFilter('status', value.status);
        updateFilter('scope', value.scope);
        updateFilter('searchName', value.search);
        updateFilter('createdDate', value.createdDate);
        updateFilter('updatedDate', value.updatedDate);
      }
    },
    [updateFilter]
  );

  return {
    filterState,
    appliedFilters,
    handleApplyFilters,
    handleResetFilters,
    handleFilterChange,
  };
};

export default useDepartmentFilters;

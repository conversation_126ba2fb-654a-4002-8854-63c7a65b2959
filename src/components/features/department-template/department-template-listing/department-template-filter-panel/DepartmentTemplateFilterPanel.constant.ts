/**
 * Constants for the DepartmentFilterPanel component
 */

import { DEPARTMENT_STATUS_OPTIONS } from '@/components/features/department/department-listing/department-filter-panel/DepartmentFilterPanel.constant';

/**
 * Status filter options
 */
export const DEPARTMENT_TEMPLATE_STATUS_OPTIONS = DEPARTMENT_STATUS_OPTIONS;

/**
 * Scope filter option type
 */
export type DepartmentTemplateStatusOption = (typeof DEPARTMENT_STATUS_OPTIONS)[number];

/**
 * Scope filter options
 */
export const DEPARTMENT_TEMPLATE_SCOPE_OPTIONS = [
  {
    value: 'organisation',
    labelKey: 'common.label.organisation',
    testId: 'filterScopeOrganisation',
  },
  { value: 'merchant', labelKey: 'common.label.merchant', testId: 'filterScopeMerchant' },
];

/**
 * Scope filter option type
 */
export type DepartmentTemplateScopeOption = (typeof DEPARTMENT_TEMPLATE_SCOPE_OPTIONS)[number];

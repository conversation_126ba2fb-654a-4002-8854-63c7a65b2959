import type { FilterState } from '@/hooks/data/use-filter.hook';
import type { DepartmentTemplateFilterParams } from '@/services/department-templates';

/**
 * Props for the DepartmentTemplateFilterPanel component
 */
export interface DepartmentTemplateFilterPanelProps {
  /**
   * The current filter state
   */
  filters: FilterState<DepartmentTemplateFilterParams['filters']>;

  /**
   * Callback fired when a filter is changed
   */
  onFilterChange: (key: string, value: any) => void;

  /**
   * Callback fired when the search button is clicked
   */
  onSearch: () => void;

  /**
   * Callback fired when the clear button is clicked
   */
  onClear: () => void;
}

// FilterSectionProps has been moved to application-ui/filter-section/CheckboxFilterSection.types.ts

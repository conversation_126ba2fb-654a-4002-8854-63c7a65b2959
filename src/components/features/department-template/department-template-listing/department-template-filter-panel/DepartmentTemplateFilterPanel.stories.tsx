// AuthFormComponents.stories.tsx
import i18n from '@/providers/i18n/i18n';
import { LocalizationProvider } from '@mui/x-date-pickers-pro';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { t } from 'i18next';
import { expect, fn, userEvent, waitFor, within } from 'storybook/internal/test';
import useDepartmentTemplateFilters from '../../hooks/use-department-template-filters.hook';
import DepartmentTemplateFilterPanel from './DepartmentTemplateFilterPanel';
import {
  DEPARTMENT_TEMPLATE_SCOPE_OPTIONS,
  DEPARTMENT_TEMPLATE_STATUS_OPTIONS,
} from './DepartmentTemplateFilterPanel.constant';

const mockHandleApplyFilters = fn();

const meta: Meta<typeof DepartmentTemplateFilterPanel> = {
  title: 'Components/features/department-template/DepartmentTemplateFilterPanel',
  component: DepartmentTemplateFilterPanel,
  parameters: {
    layout: 'fullscreen',
  },
  decorators: [
    (Story, context) => {
      const locale = context.globals.locale || 'en';
      i18n.changeLanguage(locale);

      const { filterState, handleResetFilters, handleFilterChange } =
        useDepartmentTemplateFilters();

      return (
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <Story
            args={{
              filters: { filters: filterState.filters },
              onFilterChange: handleFilterChange,
              onSearch: mockHandleApplyFilters,
              onClear: handleResetFilters,
            }}
          />
        </LocalizationProvider>
      );
    },
  ],
};

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);

    await step('Should render search, scope, status fields', async () => {
      expect(canvas.getByTestId('filterSearch')).toBeInTheDocument();

      DEPARTMENT_TEMPLATE_SCOPE_OPTIONS.forEach((option) => {
        expect(canvas.getByTestId(option.testId)).toBeInTheDocument();
      });

      DEPARTMENT_TEMPLATE_STATUS_OPTIONS.forEach((option) => {
        expect(canvas.getByTestId(option.testId)).toBeInTheDocument();
      });
    });

    await step('Should able to input search name', async () => {
      const searchInput = canvas.getByTestId('filterSearch');
      expect(searchInput).toBeInTheDocument();
      await userEvent.type(searchInput, 'test');
      await waitFor(() => {
        expect(searchInput).toHaveValue('test');
      });
    });

    await step('Should able to select scope', async () => {
      const organisationCheckboxWrapper = canvas.getByTestId('filterScopeOrganisation');
      const merchantCheckboxWrapper = canvas.getByTestId('filterScopeMerchant');

      const organisationCheckbox = organisationCheckboxWrapper.querySelector('input');
      const merchantCheckbox = merchantCheckboxWrapper.querySelector('input');

      if (!organisationCheckbox) {
        throw new Error('Organisation checkbox not found');
      }
      if (!merchantCheckbox) {
        throw new Error('Merchant checkbox not found');
      }

      // check scopes
      await userEvent.click(organisationCheckbox);
      await waitFor(() => {
        expect(organisationCheckbox).toBeChecked();
      });
      await userEvent.click(merchantCheckbox);
      await waitFor(() => {
        expect(merchantCheckbox).toBeChecked();
      });
      // uncheck scopes
      await userEvent.click(organisationCheckbox);
      await waitFor(() => {
        expect(organisationCheckbox).not.toBeChecked();
      });
      await userEvent.click(merchantCheckbox);
      await waitFor(() => {
        expect(merchantCheckbox).not.toBeChecked();
      });
    });

    await step('Should able to select status', async () => {
      const activeCheckboxWrapper = canvas.getByTestId('filterStatusActive');
      const inactiveCheckboxWrapper = canvas.getByTestId('filterStatusInactive');

      const activeCheckbox = activeCheckboxWrapper.querySelector('input');
      if (!activeCheckbox) {
        throw new Error('Active checkbox not found');
      }
      const inactiveCheckbox = inactiveCheckboxWrapper.querySelector('input');
      if (!inactiveCheckbox) {
        throw new Error('Inactive checkbox not found');
      }
      // check statuses
      await userEvent.click(activeCheckbox);
      await waitFor(() => {
        expect(activeCheckbox).toBeChecked();
      });
      await userEvent.click(activeCheckbox);
      await waitFor(() => {
        expect(activeCheckbox).not.toBeChecked();
      });

      // uncheck statuses
      await userEvent.click(inactiveCheckbox);
      await waitFor(() => {
        expect(inactiveCheckbox).toBeChecked();
      });
      await userEvent.click(inactiveCheckbox);
      await waitFor(() => {
        expect(inactiveCheckbox).not.toBeChecked();
      });
    });

    await step('Should fire apply filters event', async () => {
      const searchButton = canvas.getByText(t('common.label.search'));
      expect(searchButton).toBeInTheDocument();

      await userEvent.click(searchButton);
      await waitFor(() => {
        expect(mockHandleApplyFilters).toBeCalled();
      });
    });

    await step('Should clear all filters', async () => {
      const clearAllButton = canvas.getByText(t('common.action.clearAll'));
      expect(clearAllButton).toBeInTheDocument();

      await userEvent.click(clearAllButton);

      expect(canvas.getByTestId('filterSearch')).toHaveValue('');
      expect(canvas.getByTestId('filterScopeOrganisation')).not.toBeChecked();
      expect(canvas.getByTestId('filterScopeMerchant')).not.toBeChecked();
      expect(canvas.getByTestId('filterStatusActive')).not.toBeChecked();
      expect(canvas.getByTestId('filterStatusInactive')).not.toBeChecked();
    });
  },
};

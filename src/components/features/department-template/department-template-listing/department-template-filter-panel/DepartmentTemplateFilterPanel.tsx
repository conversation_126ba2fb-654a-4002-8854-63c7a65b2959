import BaseFilterPanel from '@/components/features/department/department-listing/department-filter-panel/BaseDepartmentFilterPanel';
import {
  DEPARTMENT_TEMPLATE_SCOPE_OPTIONS,
  DEPARTMENT_TEMPLATE_STATUS_OPTIONS,
} from './DepartmentTemplateFilterPanel.constant';
import type { DepartmentTemplateFilterPanelProps } from './DepartmentTemplateFilterPanel.type';

/**
 * DepartmentTemplateFilterPanel component displays filter options for department templates
 *
 * @example
 * ```tsx
 * <DepartmentTemplateFilterPanel
 *   filters={filterState}
 *   onFilterChange={handleFilterChange}
 *   onSearch={handleApplyFilters}
 *   onClear={handleResetFilters}
 * />
 * ```
 */
const DepartmentTemplateFilterPanel = ({
  filters,
  onFilterChange,
  onSearch,
  onClear,
}: DepartmentTemplateFilterPanelProps) => {
  return (
    <BaseFilterPanel
      filters={filters}
      onFilterChange={onFilterChange}
      onSearch={onSearch}
      onClear={onClear}
      statusOptions={DEPARTMENT_TEMPLATE_STATUS_OPTIONS}
      scopeOptions={DEPARTMENT_TEMPLATE_SCOPE_OPTIONS}
      showScope
    />
  );
};

export default DepartmentTemplateFilterPanel;

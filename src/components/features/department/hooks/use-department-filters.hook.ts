import { useFilter } from '@/hooks/data/use-filter.hook';
import type { DepartmentFilterParams } from '@/services/departments';
import { useCallback, useState } from 'react';

const DEPARTMENT_DEFAULT_FILTER_STATE = {
  searchName: '',
  status: [],
  createdDate: null,
  updatedDate: null,
};

export const getDepartmentDefaultFilteState = () =>
  structuredClone(DEPARTMENT_DEFAULT_FILTER_STATE);

/**
 * Hook for managing department filters
 * @returns Filter state and handlers
 */
export const useDepartmentFilters = () => {
  // Filter state management using the useFilter hook
  const { filterState, updateFilter, resetFilters } = useFilter<DepartmentFilterParams['filters']>(
    getDepartmentDefaultFilteState()
  );

  // Applied filters (what's actually used for filtering)
  const [appliedFilters, setAppliedFilters] = useState<DepartmentFilterParams>({
    filters: getDepartmentDefaultFilteState(),
  });

  // Apply the current filter state to the applied filters
  const handleApplyFilters = useCallback(() => {
    setAppliedFilters({
      filters: {
        status: [...filterState.filters.status],
        searchName: filterState.filters.searchName,
        createdDate: filterState.filters.createdDate,
        updatedDate: filterState.filters.updatedDate,
      },
    });
  }, [filterState]);

  // Reset all filters
  const handleResetFilters = useCallback(() => {
    // Reset the filter state
    resetFilters();

    // Also reset the applied filters
    setAppliedFilters({
      filters: getDepartmentDefaultFilteState(),
    });
  }, [resetFilters]);

  // Handle filter changes from components
  const handleFilterChange = useCallback(
    (key: string, value: any) => {
      if (key === 'filters') {
        // When the entire filters object is updated from the filter component
        updateFilter('status', value.status);
        updateFilter('searchName', value.search);
        updateFilter('createdDate', value.createdDate);
        updateFilter('updatedDate', value.updatedDate);
      }
    },
    [updateFilter]
  );

  return {
    filterState,
    appliedFilters,
    handleApplyFilters,
    handleResetFilters,
    handleFilterChange,
  };
};

export default useDepartmentFilters;

import { FilterActionButtons, FilterCheckbox } from '@/components/application-ui/filter-section';
import FilterInput from '@/components/application-ui/filter-section/filter-input/FilterInput';
import type { DepartmentTemplateScopeOption } from '@/components/features/department-template/department-template-listing/department-template-filter-panel/DepartmentTemplateFilterPanel.constant';
import type { DepartmentTemplateFilterParams } from '@/services/department-templates';
import type { DepartmentFilterParams } from '@/services/departments';
import { Box } from '@mui/material';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import type { DepartmentStatusOption } from './DepartmentFilterPanel.constant';
import { toggleFilterValue } from './DepartmentFilterPanel.util';

type FilterParamsByScope<HasScope extends boolean> = HasScope extends true
  ? DepartmentTemplateFilterParams
  : DepartmentFilterParams;

interface BaseFilterPanelProps<HasScope extends boolean = boolean> {
  filters: FilterParamsByScope<HasScope>;
  onFilterChange: (field: string, value: any) => void;
  onSearch: () => void;
  onClear: () => void;
  showScope?: HasScope;
  scopeOptions?: DepartmentTemplateScopeOption[];
  statusOptions?: DepartmentStatusOption[];
}

function BaseDapartmentFilterPanel({
  filters,
  onFilterChange,
  onSearch,
  onClear,
  showScope,
  scopeOptions = [],
  statusOptions = [],
}: Readonly<BaseFilterPanelProps>) {
  const { t } = useTranslation();

  const handleSearchNameChange = useCallback(
    (value: string) => {
      onFilterChange('filters', { ...filters.filters, searchName: value });
    },
    [filters.filters, onFilterChange]
  );

  const handleStatusChange = useCallback(
    (value: string) => {
      const newStatuses = toggleFilterValue(filters.filters.status, value);
      onFilterChange('filters', { ...filters.filters, status: newStatuses });
    },
    [filters.filters, onFilterChange]
  );

  const handleScopeChange = useCallback(
    (value: string) => {
      if ('scope' in filters.filters) {
        const newScopes = toggleFilterValue(filters.filters.scope, value);
        onFilterChange('filters', { ...filters.filters, scope: newScopes });
      }
    },
    [filters.filters, onFilterChange]
  );

  return (
    <Box>
      <FilterActionButtons
        onSearch={onSearch}
        onClear={onClear}
      />

      <FilterInput
        label={t('common.label.name')}
        id="searchName"
        name="searchName"
        type="text"
        fullWidth
        inputProps={{ 'data-testid': 'filterSearch' }}
        value={filters.filters.searchName}
        onChange={(e) => handleSearchNameChange(e.target.value)}
      />

      {showScope && scopeOptions?.length > 0 && (
        <FilterCheckbox
          label={t('common.label.scope')}
          options={scopeOptions.map((item) => ({
            ...item,
            labelKey: t(item.labelKey),
          }))}
          selectedValues={'scope' in filters.filters ? filters.filters.scope : []}
          onChange={handleScopeChange}
        />
      )}

      {statusOptions.length > 0 && (
        <FilterCheckbox
          label={t('common.label.status')}
          options={statusOptions.map((item) => ({
            ...item,
            labelKey: t(item.labelKey),
          }))}
          selectedValues={filters.filters.status}
          onChange={handleStatusChange}
        />
      )}
    </Box>
  );
}

export default BaseDapartmentFilterPanel;

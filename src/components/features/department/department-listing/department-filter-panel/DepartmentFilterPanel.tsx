import BaseFilterPanel from './BaseDepartmentFilterPanel';
import { DEPARTMENT_STATUS_OPTIONS } from './DepartmentFilterPanel.constant';
import type { DepartmentFilterPanelProps } from './DepartmentFilterPanel.type';

/**
 * DepartmentFilterPanel component displays filter options for departments
 *
 * @example
 * ```tsx
 * <DepartmentFilterPanel
 *   filters={filterState}
 *   onFilterChange={handleFilterChange}
 *   onSearch={handleApplyFilters}
 *   onClear={handleResetFilters}
 * />
 * ```
 */
const DepartmentFilterPanel = ({
  filters,
  onFilterChange,
  onSearch,
  onClear,
}: DepartmentFilterPanelProps) => {
  return (
    <BaseFilterPanel
      filters={filters}
      onFilterChange={onFilterChange}
      onSearch={onSearch}
      onClear={onClear}
      statusOptions={DEPARTMENT_STATUS_OPTIONS}
    />
  );
};

export default DepartmentFilterPanel;

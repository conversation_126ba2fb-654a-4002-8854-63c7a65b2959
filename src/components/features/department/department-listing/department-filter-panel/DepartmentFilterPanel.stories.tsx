// AuthFormComponents.stories.tsx
import i18n from '@/providers/i18n/i18n';
import { LocalizationProvider } from '@mui/x-date-pickers-pro';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { t } from 'i18next';
import { expect, fn, userEvent, waitFor, within } from 'storybook/internal/test';
import useDepartmentFilters, {
  getDepartmentDefaultFilteState,
} from '../../hooks/use-department-filters.hook';
import DepartmentFilterPanel from './DepartmentFilterPanel';

const mockHandleApplyFilters = fn();

const meta: Meta<typeof DepartmentFilterPanel> = {
  title: 'Components/features/department/DepartmentFilterPanel',
  component: DepartmentFilterPanel,
  parameters: {
    layout: 'fullscreen',
  },
  args: {
    filters: {
      filters: getDepartmentDefaultFilteState(),
    },
    onClear: () => {},
    onFilterChange: () => {},
    onSearch: () => {},
  },
  decorators: [
    (Story, context) => {
      const locale = context.globals.locale || 'en';
      i18n.changeLanguage(locale);
      const { filterState, handleResetFilters, handleFilterChange } = useDepartmentFilters();

      return (
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <Story
            args={{
              filters: { filters: filterState.filters },
              onFilterChange: handleFilterChange,
              onSearch: mockHandleApplyFilters,
              onClear: handleResetFilters,
            }}
          />
        </LocalizationProvider>
      );
    },
  ],
};

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);

    await step('Should render search, scope, status fields', async () => {
      expect(canvas.getByTestId('filterSearch')).toBeInTheDocument();
      expect(canvas.getByTestId('filterStatusActive')).toBeInTheDocument();
      expect(canvas.getByTestId('filterStatusInactive')).toBeInTheDocument();
    });

    await step('Should able to input search name', async () => {
      const searchInput = canvas.getByTestId('filterSearch');
      expect(searchInput).toBeInTheDocument();
      await userEvent.type(searchInput, 'test');
      await waitFor(() => {
        expect(searchInput).toHaveValue('test');
      });
    });

    await step('Should able to select status', async () => {
      const activeCheckboxWrapper = canvas.getByTestId('filterStatusActive');
      const inactiveCheckboxWrapper = canvas.getByTestId('filterStatusInactive');

      const activeCheckbox = activeCheckboxWrapper.querySelector('input');
      if (!activeCheckbox) {
        throw new Error('Active checkbox not found');
      }
      const inactiveCheckbox = inactiveCheckboxWrapper.querySelector('input');
      if (!inactiveCheckbox) {
        throw new Error('Inactive checkbox not found');
      }
      // check statuses
      await userEvent.click(activeCheckbox);
      await waitFor(() => {
        expect(activeCheckbox).toBeChecked();
      });
      await userEvent.click(activeCheckbox);
      await waitFor(() => {
        expect(activeCheckbox).not.toBeChecked();
      });

      // uncheck statuses
      await userEvent.click(inactiveCheckbox);
      await waitFor(() => {
        expect(inactiveCheckbox).toBeChecked();
      });
      await userEvent.click(inactiveCheckbox);
      await waitFor(() => {
        expect(inactiveCheckbox).not.toBeChecked();
      });
    });

    await step('Should fire apply filters event', async () => {
      const searchButton = canvas.getByText(t('common.label.search'));
      expect(searchButton).toBeInTheDocument();

      userEvent.click(searchButton);
      await waitFor(() => {
        expect(mockHandleApplyFilters).toBeCalledTimes(1);
      });
    });

    await step('Should clear all filters', async () => {
      const clearAllButton = canvas.getByText(t('common.action.clearAll'));
      expect(clearAllButton).toBeInTheDocument();

      await userEvent.click(clearAllButton);

      expect(canvas.getByTestId('filterSearch')).toHaveValue('');
      expect(canvas.getByTestId('filterStatusActive')).not.toBeChecked();
      expect(canvas.getByTestId('filterStatusInactive')).not.toBeChecked();
    });
  },
};

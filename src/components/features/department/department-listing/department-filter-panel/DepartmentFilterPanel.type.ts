import type { FilterState } from '@/hooks/data/use-filter.hook';
import type { DepartmentFilterParams } from '@/services/departments';

/**
 * Props for the DepartmentFilterPanel component
 */
export interface DepartmentFilterPanelProps {
  /**
   * The current filter state
   */
  filters: FilterState<DepartmentFilterParams['filters']>;

  /**
   * Callback fired when a filter is changed
   */
  onFilterChange: (key: string, value: any) => void;

  /**
   * Callback fired when the search button is clicked
   */
  onSearch: () => void;

  /**
   * Callback fired when the clear button is clicked
   */
  onClear: () => void;
}

// FilterSectionProps has been moved to application-ui/filter-section/CheckboxFilterSection.types.ts

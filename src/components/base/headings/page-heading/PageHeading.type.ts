import type { SxP<PERSON>, Theme } from '@mui/material';
import type { ReactNode } from 'react';

/**
 * Props for the PageHeading component
 */
export interface PageHeadingProps {
  /** Optional icon box to display before the title */
  iconBox?: ReactNode;

  /** The title of the page */
  title: string;

  /** An optional description to display below the title */
  description?: string;

  /** Optional top section to display below the title and description */
  topSection?: ReactNode;

  /** Optional bottom section to display below the top section */
  bottomSection?: ReactNode;

  /** Optional actions to display within the page heading section */
  actions?: ReactNode;

  /** The system prop that allows defining system overrides as well as additional CSS styles */
  sx?: SxProps<Theme>;
}

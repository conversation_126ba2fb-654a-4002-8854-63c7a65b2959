import { Typography } from '@/components/base/typography';
import { alpha, Divider, Stack, useMediaQuery } from '@mui/material';
import { type PageHeadingProps } from './PageHeading.type';

/**
 * PageHeading component renders a styled heading section with optional icon, title, description,
 * top and bottom sections, and actions.
 *
 * @example
 * ```tsx
 * <PageHeading
 *   title="Dashboard"
 *   description="Welcome to your dashboard"
 *   actions={<Button>New Item</Button>}
 * />
 * ```
 */
export const PageHeading = ({
  iconBox,
  title,
  description,
  topSection,
  bottomSection,
  actions,
  sx,
}: PageHeadingProps) => {
  const mdUp = useMediaQuery((theme) => theme.breakpoints.up('md'));

  return (
    <Stack
      p={{ xs: 2, sm: 3 }}
      sx={{
        backgroundColor: (theme) =>
          theme.palette.mode === 'dark' ? alpha(theme.palette.neutral[25], 0.02) : 'neutral.25',
        ...sx,
      }}
    >
      <Stack
        direction={{ xs: 'column', md: 'row' }}
        spacing={2}
        display="flex"
        justifyContent="space-between"
        width="100%"
      >
        <Stack
          direction="row"
          className="PageTitleContent"
          spacing={2}
          display="flex"
          alignItems="center"
          overflow="hidden"
          divider={
            <Divider
              flexItem
              orientation="vertical"
            />
          }
        >
          {iconBox}
          <Stack
            textAlign="left"
            spacing={0.3}
            overflow="hidden"
          >
            {topSection}
            <Typography
              variant="h3"
              noWrap
            >
              {title}
            </Typography>
            {description && (
              <Typography
                variant="h5"
                color="text.secondary"
                fontWeight={400}
                noWrap={mdUp}
              >
                {description}
              </Typography>
            )}
          </Stack>
        </Stack>
        {actions && (
          <Stack
            direction={{ xs: 'column', md: 'row' }}
            display="flex"
            alignItems="center"
            spacing={1}
          >
            {actions}
          </Stack>
        )}
      </Stack>
      {bottomSection}
    </Stack>
  );
};

export default PageHeading;

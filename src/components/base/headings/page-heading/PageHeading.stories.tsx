import { Button } from '@/components/base/inputs/button';
import BaseButtonTab from '@/components/base/inputs/button/variants/button-tab/ButtonTab';
import MenuRoundedIcon from '@mui/icons-material/MenuRounded';
import NavigateNextIcon from '@mui/icons-material/NavigateNext';
import { Box, Breadcrumbs, Link, Tabs, Typography } from '@mui/material';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { fn } from 'storybook/test';
import { PageHeading } from './PageHeading';

const meta = {
  title: 'Components/base/headings/PageHeading',
  component: PageHeading,
  parameters: {
    layout: 'fullscreen',
  },
  argTypes: {
    actions: {
      control: false,
    },
    iconBox: {
      control: false,
    },
    topSection: {
      control: false,
    },
    bottomSection: {
      control: false,
    },
  },
} satisfies Meta<typeof PageHeading>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    title: 'Page title',
    description: 'A description of the page.',
  },
  render: (args) => (
    <PageHeading
      {...args}
      actions={
        <Button
          sx={{
            mt: { xs: 2, md: 0 },
          }}
          variant="contained"
        >
          button
        </Button>
      }
    />
  ),
};

export const WithIcon: Story = {
  args: {
    title: 'Page title',
    description: 'This page heading without an icon.',
  },
  render: (args) => (
    <PageHeading
      {...args}
      iconBox={
        <Button
          color="secondary"
          variant="outlined"
          customStyle="icon"
          sx={{ width: 44, height: 44 }}
          title="Menu"
        >
          <MenuRoundedIcon />
        </Button>
      }
      actions={
        <Button
          sx={{
            mt: { xs: 2, md: 0 },
          }}
          variant="contained"
        >
          button
        </Button>
      }
    />
  ),
};

export const CustomActions: Story = {
  args: {
    title: 'Create New User',
    description: 'Create a new user.',
  },
  render: (args) => (
    <PageHeading
      {...args}
      actions={
        <Box
          display="flex"
          gap={1}
        >
          <Button
            sx={{
              mt: { xs: 2, md: 0 },
            }}
            variant="text"
          >
            reset
          </Button>
          <Button
            sx={{
              mt: { xs: 2, md: 0 },
            }}
            variant="contained"
          >
            save
          </Button>
        </Box>
      }
    />
  ),
};

export const WithTopSection: Story = {
  args: {
    title: 'Page with breadcrumbs',
    description: 'This page heading includes top section.',
  },
  render: (args) => (
    <PageHeading
      {...args}
      topSection={
        <Box
          display="flex"
          alignItems="flex-start"
        >
          <Breadcrumbs
            separator={
              <NavigateNextIcon
                sx={{
                  color: 'neutral.500',
                }}
                fontSize="small"
              />
            }
            aria-label="breadcrumb"
          >
            <Link
              underline="hover"
              href="/home"
              fontWeight={500}
            >
              Home
            </Link>
            <Link
              underline="hover"
              href="/dashboard"
              fontWeight={500}
            >
              Page
            </Link>
            <Typography
              fontWeight={500}
              color="neutral.700"
            >
              Subpage
            </Typography>
          </Breadcrumbs>
        </Box>
      }
      actions={
        <Button
          sx={{
            mt: { xs: 2, md: 0 },
          }}
          variant="contained"
        >
          button
        </Button>
      }
    />
  ),
};

export const WithBottomSection: Story = {
  args: {
    title: 'Page with tabs',
    description: 'This page heading includes bottom section.',
  },
  render: (args) => (
    <PageHeading
      {...args}
      bottomSection={
        <Box pt={2}>
          <Tabs
            value={0}
            onChange={fn()}
            sx={{
              overflow: 'visible',
              '& .MuiTabs-indicator': {
                display: 'none',
              },
              '& .MuiTabs-scroller': {
                overflow: 'visible !important',
              },
            }}
          >
            <BaseButtonTab
              componentType="tab"
              label="Tab 1"
            />
            <BaseButtonTab
              componentType="tab"
              label="Tab 2"
            />
            <BaseButtonTab
              componentType="tab"
              label="Tab 3"
            />
            <BaseButtonTab
              componentType="tab"
              label="Tab 4"
            />
          </Tabs>
        </Box>
      }
    />
  ),
};

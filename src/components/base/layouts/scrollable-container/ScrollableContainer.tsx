import { Scrollbar } from '@/components/base/data-display/scrollbar';
import { Box, useTheme } from '@mui/material';
import { type ScrollableContainerProps } from './ScrollableContainer.type';

/**
 * A component that wraps content in a Box with `overflow: auto`.
 *
 * By default, it uses the native scrollbar, but if you pass `useCustomScrollbar`
 * as true, it will use the custom scrollbar from {@link Scrollbar}.
 *
 * @example
 * ```tsx
 * // Basic usage with default height (256px)
 * <ScrollableContainer>
 *   Content here
 * </ScrollableContainer>
 *
 * // With custom height
 * <ScrollableContainer height={400}>
 *   Content here
 * </ScrollableContainer>
 *
 * // With custom scrollbar
 * <ScrollableContainer useCustomScrollbar>
 *   Content here
 * </ScrollableContainer>
 *
 * // With custom styles
 * <ScrollableContainer sx={{ borderRadius: 2, border: '1px solid #eee' }}>
 *   Content here
 * </ScrollableContainer>
 * ```
 */
export const ScrollableContainer = ({
  height,
  children,
  useCustomScrollbar,
  ...otherProps
}: ScrollableContainerProps) => {
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  // Determine content based on whether to use custom scrollbar
  const content = useCustomScrollbar ? (
    <Scrollbar dark={isDarkMode}>{children}</Scrollbar>
  ) : (
    children
  );

  return (
    <Box
      overflow="auto"
      height={height ?? 256}
      {...otherProps}
    >
      {content}
    </Box>
  );
};

export default ScrollableContainer;

import { type SxProps, type Theme } from '@mui/material';
import { type ReactNode } from 'react';

/**
 * Props for the ScrollableContainer component
 */
export interface ScrollableContainerProps {
  /**
   * Optional height of the container.
   * @default 256
   */
  height?: number;

  /**
   * The content to be displayed inside the scrollable container.
   */
  children?: ReactNode;

  /**
   * If true, a custom scrollbar is used instead of the native one.
   * @default false
   */
  useCustomScrollbar?: boolean;

  /**
   * Optional styling properties to apply to the Box component.
   */
  sx?: SxProps<Theme>;

  /**
   * Any other props to pass to the Box component
   */
  [key: string]: any;
}

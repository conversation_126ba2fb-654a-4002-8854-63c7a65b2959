import createCache from '@emotion/cache';
import { CacheProvider } from '@emotion/react';
import { useEffect } from 'react';
import stylisRTLPlugin from 'stylis-plugin-rtl';
import { type RtlDirectionProps } from './RtlDirection.type';

/**
 * Creates an Emotion cache with RTL support
 * @returns Emotion cache configured for RTL
 */
const createRtlCache = () =>
  createCache({
    key: 'rtl',
    prepend: true,
    // @ts-ignore - stylisPlugins type is not properly defined in the library
    stylisPlugins: [stylisRTLPlugin],
  });

/**
 * A component that sets the text direction of the document and applies
 * right-to-left (RTL) styling if specified.
 *
 * This component should be used at a high level in your application to
 * enable RTL support for all child components.
 *
 * @example
 * ```tsx
 * // For LTR (default)
 * <RtlDirection>
 *   <App />
 * </RtlDirection>
 *
 * // For RTL
 * <RtlDirection direction="rtl">
 *   <App />
 * </RtlDirection>
 * ```
 */
export const RtlDirection = ({ children, direction = 'ltr' }: RtlDirectionProps) => {
  useEffect(() => {
    // Set the document direction attribute
    document.dir = direction;
  }, [direction]);

  // Only use the CacheProvider when in RTL mode
  if (direction === 'rtl') {
    return <CacheProvider value={createRtlCache()}>{children}</CacheProvider>;
  }

  // In LTR mode, just render the children
  return <>{children}</>;
};

export default RtlDirection;

import { Accordion as MuiAccordion } from '@mui/material';
import type { AccordionProps } from './Accordion.type';
import {
  AccordionAlternate,
  AccordionMinimal,
  AccordionPlus,
  AccordionPrimary,
} from './styles/Accordion.style';

/**
 * A customizable accordion component that wraps MUI's Accordion with different styles.
 */
export const Accordion = ({ customStyle, ...props }: AccordionProps) => {
  switch (customStyle) {
    case 'alternate':
      return <AccordionAlternate {...props} />;
    case 'minimal':
      return <AccordionMinimal {...props} />;
    case 'plus':
      return <AccordionPlus {...props} />;
    case 'primary':
      return <AccordionPrimary {...props} />;
    default:
      return <MuiAccordion {...props} />;
  }
};

export default Accordion;

import { transformText } from '@/utils';
import { Typography as MuiTypography } from '@mui/material';
import React from 'react';
import type { TypographyProps } from './Typography.type';

/**
 * A Material UI Typography component that can transform its children string
 * value to another case format.
 */
export const Typography = ({ children, caseTransform, ...props }: TypographyProps) => {
  // Apply the change-case transformation if specified
  let transformedChildren = children;

  if (caseTransform && typeof children === 'string') {
    // Apply the transformation
    switch (caseTransform) {
      case 'camelCase':
        transformedChildren = transformText(children, 'camelCase');
        break;
      case 'capitalCase':
        transformedChildren = transformText(children, 'capitalCase');
        break;
      case 'constantCase':
        transformedChildren = transformText(children, 'constantCase');
        break;
      case 'dotCase':
        transformedChildren = transformText(children, 'dotCase');
        break;
      case 'kebabCase':
        transformedChildren = transformText(children, 'kebabCase');
        break;
      case 'lowercase':
        transformedChildren = children.toLowerCase();
        break;
      case 'noCase':
        transformedChildren = transformText(children, 'noCase');
        break;
      case 'none':
        transformedChildren = children;
        break;
      case 'pascalCase':
        transformedChildren = transformText(children, 'pascalCase');
        break;
      case 'pascalSnakeCase':
        transformedChildren = transformText(children, 'pascalSnakeCase');
        break;
      case 'pathCase':
        transformedChildren = transformText(children, 'pathCase');
        break;
      case 'sentenceCase':
        transformedChildren = transformText(children, 'sentenceCase');
        break;
      case 'snakeCase':
        transformedChildren = transformText(children, 'snakeCase');
        break;
      case 'trainCase':
        transformedChildren = transformText(children, 'trainCase');
        break;
      case 'uppercase':
        transformedChildren = children.toUpperCase();
        break;
      default:
        transformedChildren = children;
        break;
    }
  }

  return <MuiTypography {...props}>{transformedChildren}</MuiTypography>;
};

export default Typography;

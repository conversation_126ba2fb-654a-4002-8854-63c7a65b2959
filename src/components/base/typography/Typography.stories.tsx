import { Box, Stack } from '@mui/material';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { Typography } from './Typography';
import type { ChangeCaseTransform } from './Typography.type';

const meta = {
  title: 'Components/base/typography/Typography',
  component: Typography,
  parameters: {
    layout: 'centered',
  },
} satisfies Meta<typeof Typography>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: 'Hello World',
    fontSize: 24,
    fontFamily: 'Inter',
    fontWeight: 600,
    color: 'primary',
  },
};

/**
 * Combined story showing all case transformation options
 */
export const CaseTransforms: Story = {
  render: () => {
    const caseTransforms: ChangeCaseTransform[] = [
      'camelCase',
      'capitalCase',
      'constantCase',
      'dotCase',
      'kebabCase',
      'lowercase',
      'noCase',
      'none',
      'pascalCase',
      'pascalSnakeCase',
      'pathCase',
      'sentenceCase',
      'snakeCase',
      'trainCase',
      'uppercase',
    ];

    return (
      <Stack spacing={2}>
        {caseTransforms.map((transform) => (
          <Box
            key={transform}
            display="flex"
            alignItems="center"
          >
            <Typography
              width={150}
              variant="subtitle2"
            >
              {transform}:
            </Typography>
            <Typography caseTransform={transform}>Hello World</Typography>
          </Box>
        ))}
      </Stack>
    );
  },
};

/**
 * Combined story showing all typography variants
 */
export const Variants: Story = {
  render: () => {
    const variants = [
      'h1',
      'h2',
      'h3',
      'h4',
      'h5',
      'h6',
      'subtitle1',
      'subtitle2',
      'body1',
      'body2',
      'button',
      'caption',
      'overline',
    ];

    return (
      <Stack spacing={2}>
        {variants.map((variant) => (
          <Box
            key={variant}
            display="flex"
            alignItems="center"
          >
            <Typography
              width={150}
              variant="subtitle2"
            >
              {variant}:
            </Typography>
            <Typography variant={variant as any}>Hello World</Typography>
          </Box>
        ))}
      </Stack>
    );
  },
};

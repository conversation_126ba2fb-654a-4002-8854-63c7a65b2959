import { type TypographyProps as MuiTypographyProps } from '@mui/material';

// Define the available change-case transformations
export type ChangeCaseTransform =
  | 'camelCase'
  | 'capitalCase'
  | 'constantCase'
  | 'dotCase'
  | 'kebabCase'
  | 'lowercase'
  | 'noCase'
  | 'none'
  | 'pascalCase'
  | 'pascalSnakeCase'
  | 'pathCase'
  | 'sentenceCase'
  | 'snakeCase'
  | 'trainCase'
  | 'uppercase';

// Extend MUI's TypographyProps to include our custom caseTransform prop
export interface TypographyProps extends Omit<MuiTypographyProps, 'textTransform'> {
  /**
   * The case transformation to apply to the children string value using change-case
   */
  caseTransform?: ChangeCaseTransform;
  /**
   * The text to transform using original MUI's textTransform
   */
  textTransform?: MuiTypographyProps['textTransform'];
}

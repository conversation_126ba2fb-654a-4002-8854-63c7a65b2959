import { useTheme } from '@mui/material';
import { type ScrollbarProps } from './Scrollbar.type';
import { getScrollbarStyles, ScrollbarWrapper } from './styles/Scrollbar.styles';

/**
 * A wrapper around `SimpleBar` with a custom scrollbar style.
 *
 * The scrollbar color is determined by the `dark` prop. If `dark` is true, the
 * scrollbar will be a dark color. If `dark` is false, the scrollbar will be a
 * light color.
 *
 * The scrollbar background color will change on hover.
 *
 * @example
 * ```tsx
 * <Scrollbar dark>Content here</Scrollbar>
 * ```
 */
export const Scrollbar = ({ dark, children, ...otherProps }: ScrollbarProps) => {
  const theme = useTheme();

  return (
    <ScrollbarWrapper
      autoHide
      sx={getScrollbarStyles(theme, dark)}
      {...otherProps}
    >
      {children}
    </ScrollbarWrapper>
  );
};

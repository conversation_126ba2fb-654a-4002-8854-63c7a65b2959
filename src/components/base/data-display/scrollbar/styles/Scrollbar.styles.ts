import { alpha, styled, type SxProps, type Theme } from '@mui/material';
import SimpleBar from 'simplebar-react';

/**
 * Styled wrapper for SimpleBar
 */
export const ScrollbarWrapper = styled(SimpleBar)(() => ({}));

/**
 * Get the scrollbar background color for dark mode
 */
export const getScrollbarBackgroundDark = (theme: Theme) => {
  return theme.palette.mode === 'dark'
    ? alpha(theme.palette.neutral[800], 0.8)
    : alpha(theme.palette.neutral[500], 0.8);
};

/**
 * Get the scrollbar background color for light mode
 */
export const getScrollbarBackgroundLight = (theme: Theme) => {
  return theme.palette.mode === 'dark'
    ? alpha(theme.palette.neutral[800], 0.3)
    : alpha(theme.palette.neutral[300], 0.7);
};

/**
 * Get the scrollbar hover background color for dark mode
 */
export const getScrollbarHoverBackgroundDark = (theme: Theme) => {
  return theme.palette.mode === 'dark'
    ? alpha(theme.palette.neutral[400], 0.3)
    : theme.palette.neutral[400];
};

/**
 * Get the complete styles for the scrollbar component
 *
 * @param theme - The MUI theme
 * @param dark - Whether to use dark mode styling
 * @returns The sx prop styles object
 */
export const getScrollbarStyles = (theme: Theme, dark?: boolean): SxProps<Theme> => ({
  height: '100%',
  '.simplebar-scrollbar': {
    '&::before': {
      background: dark ? getScrollbarBackgroundDark(theme) : getScrollbarBackgroundLight(theme),
      borderRadius: theme.shape.borderRadius,
    },
    '&.simplebar-hover': {
      '&::before': {
        background: dark
          ? getScrollbarHoverBackgroundDark(theme)
          : getScrollbarBackgroundLight(theme),
      },
    },
  },
});

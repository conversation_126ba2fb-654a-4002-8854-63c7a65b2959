import type { PaletteColorKey } from '@/theme/colors';
import { alpha, type PaletteColor, type SxProps, type Theme } from '@mui/material';
import { lighten } from '@mui/material/styles';
import { SHADOW_STRENGTHS } from './Avatar.constant';
import type { AvatarStateProps } from './Avatar.type';

/**
 * Calculates the styles for an avatar based on its state, shadow, and softness.
 *
 * @param theme - The MUI theme
 * @param state - The state color of the avatar
 * @param useShadow - Whether to apply a shadow
 * @param isSoft - Whether to use a soft color variant
 * @returns An object containing backgroundColor, boxShadow, and color
 */
export const getStateStyles = (
  theme: Theme,
  state?: AvatarStateProps['state'],
  useShadow?: boolean,
  isSoft?: boolean
) => {
  let backgroundColor: string | undefined;
  let boxShadow: string | undefined;
  let color: string | undefined;

  // Determine if we're in dark mode
  const isDarkMode = theme.palette.mode === 'dark';

  const getColorAndShadow = (colorName: PaletteColorKey, shadowStrength?: number) => {
    // Access the palette color safely, handling both standard and custom colors
    const paletteColor = theme.palette[colorName as keyof typeof theme.palette] as PaletteColor;

    // Adjust background based on theme mode and soft setting
    if (isSoft) {
      if (isDarkMode) {
        // In dark mode, use alpha for a subtle background
        backgroundColor = alpha(paletteColor.main, 0.08);
      } else {
        // In light mode, use lighten for better contrast with dark text
        backgroundColor = lighten(paletteColor.main, 0.85);
      }
      color = paletteColor.main;
    } else {
      // For solid backgrounds
      backgroundColor = paletteColor.main;
      color = paletteColor.contrastText;
    }

    // Use shadow strength if provided, otherwise use a default of 1
    const actualShadowStrength = shadowStrength ?? 1;
    boxShadow = useShadow && !isSoft ? theme.shadows[actualShadowStrength] : undefined;
  };

  // Check if the state is a valid palette color key
  const isValidPaletteColor =
    state &&
    (state === 'primary' ||
      state === 'secondary' ||
      state === 'success' ||
      state === 'error' ||
      state === 'warning' ||
      state === 'info' ||
      state in SHADOW_STRENGTHS);

  if (isValidPaletteColor && state) {
    // Handle all palette colors (standard and custom)
    getColorAndShadow(state as PaletteColorKey, SHADOW_STRENGTHS[state as PaletteColorKey]);
  } else if (state === 'light') {
    // Adjust light state based on theme mode
    if (isDarkMode) {
      backgroundColor = alpha(theme.palette.common.white, 0.1);
      color = theme.palette.common.white;
    } else {
      // In light mode, use a very light gray that works well with dark text
      backgroundColor = lighten(theme.palette.grey[300], 0.5);
      color = theme.palette.text.primary;
    }
    boxShadow = undefined;
  } else {
    // Default state
    backgroundColor = theme.palette.background.paper;
    color = theme.palette.text.secondary;
    boxShadow = undefined;
  }

  return { backgroundColor, boxShadow, color };
};

/**
 * Generates a color based on a string.
 * Useful for creating consistent avatar background colors based on names.
 */
/**
 * Generates a color based on a string, with adjustments for theme mode.
 *
 * @param string - The string to generate a color from (typically a name)
 * @param theme - The MUI theme object
 * @returns A color string in hex format
 */
export const stringToColor = (string: string, theme: Theme) => {
  // Determine if we're in dark mode
  const isDarkMode = theme.palette.mode === 'dark';
  let hash = 0;
  let i: number;

  /* eslint-disable no-bitwise */
  for (i = 0; i < string.length; i += 1) {
    hash = string.charCodeAt(i) + ((hash << 5) - hash);
  }

  let color = '#';

  for (i = 0; i < 3; i += 1) {
    const value = (hash >> (i * 8)) & 0xff;
    color += `00${value.toString(16)}`.slice(-2);
  }
  /* eslint-enable no-bitwise */

  // For light theme, lighten the color to ensure good contrast with dark text
  if (!isDarkMode) {
    // Use MUI's lighten function to create a lighter version of the color
    // The second parameter is the coefficient (0-1), where higher values make the color lighter
    return lighten(color, 0.7);
  }

  return color;
};

/**
 * Creates props for an avatar based on a string (typically a name).
 * Generates a background color and initials from the string.
 *
 * @param string - The string to generate avatar props from (typically a name)
 * @param theme - The MUI theme object
 * @returns Props for the Avatar component
 */
export const stringAvatar = (string: string, theme?: Theme, sx?: SxProps<Theme>) => {
  // If no theme is provided, we can't generate a color
  if (!theme) {
    return {
      sx,
      children: `${string?.[0]?.toUpperCase() ?? ''}${string?.[string.length - 1]?.toUpperCase() ?? ''}`,
    };
  }

  // Determine if we're in dark mode
  const isDarkMode = theme.palette.mode === 'dark';

  return {
    sx: {
      bgcolor: stringToColor(string, theme),
      // Set text color based on theme mode
      color: isDarkMode ? 'white' : 'text.primary',
      ...sx,
    },
    children: `${string?.[0]?.toUpperCase() ?? ''}${string?.[string.length - 1]?.toUpperCase() ?? ''}`,
  };
};

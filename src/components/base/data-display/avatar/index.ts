// Constants
export { AVATAR_SIZES, SHADOW_STRENGTHS } from './Avatar.constant';

// Types
export type {
  AvatarGradientProps,
  AvatarIconProps,
  AvatarStateProps,
  AvatarTitleDescriptionStackedProps,
} from './Avatar.type';

// Utilities
export { getStateStyles, stringAvatar, stringToColor } from './Avatar.util';

// Variants
export { default as AvatarGradient } from './variants/avatar-gradient/AvatarGradient';
export {
  default as AvatarIcon,
  ErrorAvatar,
  InfoAvatar,
  SuccessAvatar,
  WarningAvatar,
} from './variants/avatar-icon/AvatarIcon';
export { default as AvatarState } from './variants/avatar-state/AvatarState';
export { default as AvatarTitleDescriptionStacked } from './variants/avatar-title-description-stacked/AvatarTitleDescriptionStacked';

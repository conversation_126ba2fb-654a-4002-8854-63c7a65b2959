import { PALETTE_COLOR_OPTIONS, type <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/theme/colors';

/**
 * Default avatar sizes in pixels
 */
export const AVATAR_SIZES = {
  SMALL: 32,
  MEDIUM: 40,
  LARGE: 56,
  <PERSON>LAR<PERSON>: 72,
};

/**
 * Shadow strength mapping for different avatar states
 *
 * Note: This is a partial record that only includes standard palette colors.
 * Custom colors will use a default shadow strength of 1.
 */
export const SHADOW_STRENGTHS: Partial<Record<PaletteColorKey, number>> = {
  success: 2,
  error: 3,
  warning: 4,
  info: 5,
  primary: 1,
  secondary: 16,
  // Custom colors
  honeyGold: 2,
  livingCoral: 3,
  emerald: 2,
  monacoBlue: 1,
  ultraViolet: 1,
  tangerineTango: 3,
  greenery: 2,
  roseQuartz: 3,
  radiantOrchid: 1,
  darkViolet: 1,
  royalBlue: 1,
  neutral: 16,
};

/**
 * All available color options for avatar components
 * This list is used in Storybook controls to provide a dropdown of color options
 * It includes all palette colors plus the special 'light' option for AvatarState
 */
export const AVATAR_COLOR_OPTIONS = [...PALETTE_COLOR_OPTIONS, 'light'] as const;

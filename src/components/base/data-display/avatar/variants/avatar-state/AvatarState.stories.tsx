import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/theme/colors';
import { Box, Grid, Stack, Typography } from '@mui/material';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { AVATAR_COLOR_OPTIONS } from '../../Avatar.constant';
import { AvatarState } from './AvatarState';

/**
 * A customized Avatar component that can be styled based on state colors.
 * This is a variant that extends the base Avatar with state-based styling.
 */
const meta = {
  title: 'Components/base/data-display/Avatar/AvatarState',
  component: AvatarState,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    state: {
      control: 'select',
      options: AVATAR_COLOR_OPTIONS,
      description: 'The state color of the avatar',
    },
    useShadow: { control: 'boolean', description: 'Whether to apply a shadow to the avatar' },
    isSoft: { control: 'boolean', description: 'Whether to use a soft color variant' },
    isUpload: {
      control: 'boolean',
      description: 'Whether the avatar is used for upload functionality',
    },
    alt: { control: 'text', description: 'Alternative text for the avatar image' },
    src: { control: 'text', description: 'Source URL for the avatar image' },
    imgProps: { table: { disable: true } },
  },
} satisfies Meta<typeof AvatarState>;

export default meta;
type Story = StoryObj<typeof meta>;

// Sample avatar image URL used across stories
const avatarSrc = '/avatars/1.png';

/**
 * Default AvatarState with primary color
 */
export const Default: Story = {
  args: {
    state: 'primary',
    useShadow: true,
    isSoft: true,
    alt: 'User Avatar',
    src: avatarSrc,
  },
};

/**
 * AvatarState with shadow effect
 */
export const WithShadow: Story = {
  args: {
    state: 'primary',
    useShadow: true,
    isSoft: false,
    alt: 'User Avatar',
    src: avatarSrc,
  },
};

/**
 * AvatarState with soft color variant
 */
export const Soft: Story = {
  args: {
    state: 'primary',
    useShadow: false,
    isSoft: true,
    alt: 'User Avatar',
    src: avatarSrc,
  },
};

/**
 * AvatarState with different colors
 */
export const Colors: Story = {
  render: () => {
    const colors: PaletteColorKey[] = [
      'primary',
      'secondary',
      'success',
      'error',
      'warning',
      'info',
      'ultraViolet',
      'livingCoral',
      'emerald',
    ];

    return (
      <Box>
        <Typography
          variant="body2"
          sx={{ mb: 2 }}
        >
          AvatarState with different colors
        </Typography>
        <Grid
          container
          spacing={2}
        >
          {colors.map((color) => (
            <Grid key={`state-${color}`}>
              <Stack
                spacing={1}
                alignItems="center"
              >
                <AvatarState
                  state={color}
                  useShadow
                  isSoft
                  alt={`${color} Avatar`}
                  src={avatarSrc}
                />
                <Typography variant="caption">{color}</Typography>
              </Stack>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  },
};

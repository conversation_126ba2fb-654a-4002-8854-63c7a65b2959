import { Avatar, styled } from '@mui/material';
import { type AvatarStateProps } from '../../Avatar.type';
import { getStateStyles } from '../../Avatar.util';

/**
 * A customized Avatar component that can be styled based on state colors.
 * This is a variant that extends the base Avatar with state-based styling.
 *
 * @example
 * ```tsx
 * <AvatarState
 *   state="primary"
 *   useShadow
 *   alt="User Avatar"
 *   src="/path/to/avatar.jpg"
 * />
 * ```
 */
export const AvatarState = styled(Avatar, {
  shouldForwardProp: (prop) => prop !== 'state' && prop !== 'useShadow' && prop !== 'isSoft',
})<AvatarStateProps>(({ theme, state, useShadow, isSoft }) => {
  const { backgroundColor, boxShadow, color } = getStateStyles(theme, state, useShadow, isSoft);
  return {
    backgroundColor,
    boxShadow,
    color,
  };
});

export default AvatarState;

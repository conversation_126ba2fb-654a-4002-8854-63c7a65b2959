import { Typography } from '@/components/base/typography';
import { Avatar, Badge, Box, useTheme } from '@mui/material';
import { type AvatarTitleDescriptionStackedProps } from '../../Avatar.type';
import { stringAvatar } from '../../Avatar.util';

/**
 * A component that displays an avatar with a title and description stacked vertically.
 *
 * @example
 * ```tsx
 * <AvatarTitleDescriptionStacked
 *   title="John Doe"
 *   description="Software Engineer"
 *   avatarSrc="/path/to/avatar.jpg"
 * />
 * ```
 */
export const AvatarTitleDescriptionStacked = ({
  title,
  description,
  avatarSrc,
}: AvatarTitleDescriptionStackedProps) => {
  const theme = useTheme();
  return (
    <Box
      display="flex"
      alignItems="center"
      flexDirection="column"
      justifyContent="center"
    >
      <Badge
        overlap="rectangular"
        color="success"
        badgeContent="8"
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <Avatar
          variant="rounded"
          alt={title}
          src={avatarSrc ?? ''}
          {...stringAvatar(title, theme, {
            width: 52,
            height: 52,
            mb: 1,
          })}
        />
      </Badge>
      <Typography
        variant="h6"
        component="div"
      >
        {title}
      </Typography>
      <Typography
        variant="subtitle2"
        color="text.secondary"
        noWrap
      >
        {description}
      </Typography>
    </Box>
  );
};

export default AvatarTitleDescriptionStacked;

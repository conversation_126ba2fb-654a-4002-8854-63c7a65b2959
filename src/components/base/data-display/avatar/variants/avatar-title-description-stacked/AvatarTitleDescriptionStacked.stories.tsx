import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { AvatarTitleDescriptionStacked } from './AvatarTitleDescriptionStacked';

/**
 * A component that displays an avatar with a title and description stacked vertically.
 * This variant is useful for user profile displays.
 */
const meta = {
  title: 'Components/base/data-display/Avatar/AvatarTitleDescriptionStacked',
  component: AvatarTitleDescriptionStacked,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    title: { control: 'text', description: 'The title to display below the avatar' },
    description: { control: 'text', description: 'The description to display below the title' },
    avatarSrc: { control: 'text', description: 'The source URL for the avatar image' },
  },
} satisfies Meta<typeof AvatarTitleDescriptionStacked>;

export default meta;
type Story = StoryObj<typeof meta>;

// Sample avatar image URL used across stories
const avatarSrc = '/avatars/1.png';

/**
 * Default AvatarTitleDescriptionStacked with title and description
 */
export const Default: Story = {
  args: {
    title: '<PERSON>',
    description: 'Software Engineer',
    avatarSrc: avatarSrc,
  },
};

/**
 * AvatarTitleDescriptionStacked with title only
 */
export const TitleOnly: Story = {
  args: {
    title: 'John Doe',
    avatarSrc: avatarSrc,
  },
};

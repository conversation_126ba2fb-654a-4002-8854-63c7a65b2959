import { PALETTE_COLOR_OPTIONS, type Palette<PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/theme/colors';
import { Box, Grid, Stack, Typography } from '@mui/material';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { AvatarGradient } from './AvatarGradient';

/**
 * Avatar component with gradient background and colored border.
 * This variant provides a visually appealing gradient effect.
 */
const meta = {
  title: 'Components/base/data-display/Avatar/AvatarGradient',
  component: AvatarGradient,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    color: {
      control: 'select',
      options: PALETTE_COLOR_OPTIONS,
      description: 'The color to use for the gradient and border',
    },
    size: {
      control: { type: 'range', min: 24, max: 120, step: 4 },
      description: 'The size of the avatar in pixels',
    },
    borderRadius: {
      control: { type: 'range', min: 0, max: 40, step: 2 },
      description: 'Custom border radius in pixels',
    },
    alt: { control: 'text', description: 'Alternative text for the avatar image' },
    src: { control: 'text', description: 'Source URL for the avatar image' },
    imgProps: { table: { disable: true } },
    variant: { table: { disable: true } },
  },
} satisfies Meta<typeof AvatarGradient>;

export default meta;
type Story = StoryObj<typeof meta>;

// Sample avatar image URL used across stories
const avatarSrc = '/avatars/1.png';

/**
 * Default AvatarGradient with primary color
 */
export const Default: Story = {
  args: {
    color: 'primary',
    size: 40,
    borderRadius: 8,
    alt: 'User Avatar',
    src: avatarSrc,
  },
};

/**
 * AvatarGradient with custom size
 */
export const CustomSize: Story = {
  args: {
    color: 'primary',
    size: 64,
    borderRadius: 8,
    alt: 'User Avatar',
    src: avatarSrc,
  },
};

/**
 * AvatarGradient with custom border radius
 */
export const CustomBorderRadius: Story = {
  args: {
    color: 'primary',
    size: 40,
    borderRadius: 20,
    alt: 'User Avatar',
    src: avatarSrc,
  },
};

/**
 * AvatarGradient with different colors
 */
export const Colors: Story = {
  parameters: {
    controls: { disable: true },
  },
  render: () => {
    const colors: PaletteColorKey[] = [
      'primary',
      'secondary',
      'success',
      'error',
      'warning',
      'info',
      'honeyGold',
      'radiantOrchid',
    ];

    return (
      <Box>
        <Typography
          variant="body2"
          sx={{ mb: 2 }}
        >
          AvatarGradient with different colors
        </Typography>
        <Grid
          container
          spacing={2}
        >
          {colors.map((color) => (
            <Grid key={`gradient-${color}`}>
              <Stack
                spacing={1}
                alignItems="center"
              >
                <AvatarGradient
                  color={color}
                  size={40}
                  alt={`${color} Avatar`}
                  src={avatarSrc}
                />
                <Typography variant="caption">{color}</Typography>
              </Stack>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  },
};

import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/theme/colors';
import PersonIcon from '@mui/icons-material/Person';
import { Box, Grid, Stack, Typography } from '@mui/material';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { AVATAR_COLOR_OPTIONS } from '../../Avatar.constant';
import { AvatarIcon, ErrorAvatar, InfoAvatar, SuccessAvatar, WarningAvatar } from './AvatarIcon';

/**
 * A standardized avatar component that displays an icon with consistent styling.
 * This variant is used for displaying icons instead of images.
 */
const meta = {
  title: 'Components/base/data-display/Avatar/AvatarIcon',
  component: AvatarIcon,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    state: {
      control: 'select',
      options: AVATAR_COLOR_OPTIONS,
      description: 'The state color of the avatar',
    },
    isSoft: { control: 'boolean', description: 'Whether to use a soft color variant' },
    size: {
      control: { type: 'range', min: 24, max: 120, step: 4 },
      description: 'The size of the avatar in pixels',
    },
    useShadow: { control: 'boolean', description: 'Whether to apply a shadow to the avatar' },
    imgProps: { table: { disable: true } },
  },
} satisfies Meta<typeof AvatarIcon>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default AvatarIcon with primary color
 */
export const Default: Story = {
  args: {
    state: 'primary',
    isSoft: true,
    size: 40,
    useShadow: false,
    // We need to provide a dummy value for icon in args
    // The actual icon will be set in the render function
    icon: {} as any,
  },
  render: (args) => (
    <AvatarIcon
      {...args}
      icon={<PersonIcon />}
    />
  ),
};

/**
 * AvatarIcon with shadow effect
 */
export const WithShadow: Story = {
  args: {
    state: 'primary',
    isSoft: true,
    size: 40,
    useShadow: true,
    // We need to provide a dummy value for icon in args
    // The actual icon will be set in the render function
    icon: {} as any,
  },
  render: (args) => (
    <AvatarIcon
      {...args}
      icon={<PersonIcon />}
    />
  ),
};

/**
 * AvatarIcon with solid color (not soft)
 */
export const Solid: Story = {
  args: {
    state: 'primary',
    isSoft: false,
    size: 40,
    useShadow: false,
    // We need to provide a dummy value for icon in args
    // The actual icon will be set in the render function
    icon: {} as any,
  },
  render: (args) => (
    <AvatarIcon
      {...args}
      icon={<PersonIcon />}
    />
  ),
};

/**
 * AvatarIcon with different colors
 */
export const Colors: Story = {
  args: {
    // We need to provide a dummy value for icon in args to satisfy TypeScript
    icon: {} as any,
  },
  render: () => {
    const colors: PaletteColorKey[] = [
      'primary',
      'secondary',
      'success',
      'error',
      'warning',
      'info',
    ];

    return (
      <Box>
        <Typography
          variant="body2"
          sx={{ mb: 2 }}
        >
          AvatarIcon with different colors
        </Typography>
        <Grid
          container
          spacing={2}
        >
          {colors.map((color) => (
            <Grid key={`icon-${color}`}>
              <Stack
                spacing={1}
                alignItems="center"
              >
                <AvatarIcon
                  icon={<PersonIcon />}
                  state={color}
                  isSoft
                  size={40}
                />
                <Typography variant="caption">{color}</Typography>
              </Stack>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  },
};

/**
 * Predefined icon avatars
 */
export const PredefinedIcons: Story = {
  args: {
    // We need to provide a dummy value for icon in args to satisfy TypeScript
    icon: {} as any,
  },
  render: () => (
    <Stack
      direction="row"
      spacing={2}
    >
      <SuccessAvatar size={40} />
      <ErrorAvatar size={40} />
      <WarningAvatar size={40} />
      <InfoAvatar size={40} />
    </Stack>
  ),
};

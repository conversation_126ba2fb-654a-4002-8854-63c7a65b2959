import type { Pa<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/theme/colors';
import type { AvatarProps, SvgIconProps } from '@mui/material';
import type { ReactElement } from 'react';

/**
 * Props for the AvatarState component
 */
export interface AvatarStateProps extends AvatarProps {
  /**
   * The state color of the avatar.
   */
  state?: PaletteColorKey | 'light';

  /**
   * Whether to apply a shadow to the avatar.
   */
  useShadow?: boolean;

  /**
   * Whether to use a soft color variant.
   */
  isSoft?: boolean;

  /**
   * Whether the avatar is used for upload functionality.
   */
  isUpload?: boolean;
}

/**
 * Props for the AvatarTitleDescriptionStacked component
 */
export interface AvatarTitleDescriptionStackedProps {
  /**
   * The title to display below the avatar.
   */
  title: string;

  /**
   * The description to display below the title.
   */
  description?: string;

  /**
   * The source URL for the avatar image.
   */
  avatarSrc?: string;
}

/**
 * Props for the AvatarIcon component
 */
export interface AvatarIconProps extends Omit<AvatarStateProps, 'children'> {
  /**
   * The icon to display inside the avatar.
   * Should be a MUI icon component or similar SVG icon.
   */
  icon: ReactElement<SvgIconProps>;

  /**
   * The state color of the avatar.
   * @default 'primary'
   */
  state?: PaletteColorKey | 'light';

  /**
   * Whether to use a soft color variant.
   * @default true
   */
  isSoft?: boolean;

  /**
   * The size of the avatar in pixels.
   * @default 40
   */
  size?: number;
}

/**
 * Props for the AvatarGradient component
 */
export interface AvatarGradientProps extends Omit<AvatarProps, 'variant'> {
  /**
   * The color to use for the gradient and border
   * Uses standard palette colors
   * @default 'primary'
   */
  color?: PaletteColorKey;

  /**
   * The size of the avatar in pixels
   * @default 40
   */
  size?: number;

  /**
   * Custom border radius in pixels
   * @default theme.shape.borderRadius * 2
   */
  borderRadius?: number;
}

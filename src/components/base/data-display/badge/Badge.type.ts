import type { BadgeProps } from '@mui/material';
import type { ReactNode } from 'react';

/**
 * Common color types used across badge components
 */
export type BadgeColor = 'success' | 'error' | 'primary' | 'secondary' | 'warning' | 'info';

/**
 * Props for the PulseBadge component
 */
export interface PulseBadgeProps extends BadgeProps {
  /**
   * The color of the badge
   * @default 'success'
   */
  color?: BadgeColor;

  /**
   * The content of the badge
   */
  children?: ReactNode;
}

/**
 * Props for the InlineBadge component
 */
export interface InlineBadgeProps {
  /**
   * The content of the badge
   */
  children?: ReactNode;
}

/**
 * Color variants for the RingBadge component
 */
export type BadgeVariant =
  | 'colorPrimary'
  | 'colorSecondary'
  | 'colorWarning'
  | 'colorSuccess'
  | 'colorError'
  | 'colorInfo';

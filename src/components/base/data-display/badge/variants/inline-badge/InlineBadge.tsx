import { Box, styled } from '@mui/material';
import { type InlineBadgeProps } from '../../Badge.type';

/**
 * A styled Box component that displays badges inline.
 * This is useful for displaying badges in a row.
 *
 * @example
 * ```tsx
 * <InlineBadge>
 *   <Badge badgeContent={4} color="primary">
 *     <MailIcon />
 *   </Badge>
 * </InlineBadge>
 * ```
 */
export const InlineBadge = styled(Box)<InlineBadgeProps>(() => ({
  display: 'flex',
  alignItems: 'center',
  '& .MuiBadge-badge': {
    position: 'static',
    transform: 'none',
  },
}));

export default InlineBadge;

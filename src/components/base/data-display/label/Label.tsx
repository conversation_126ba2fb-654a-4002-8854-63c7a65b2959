import Chip from '@mui/material/Chip';
import type { ReactElement } from 'react';
import type { LabelProps } from './Label.type';

/**
 * Returns a ReactElement that displays a Chip with the appropriate label and color,
 * based on the given value and label map.
 *
 * @example
 * ```tsx
 * <Label
 *   value="active"
 *   map={{
 *     active: { text: 'Active', color: 'success' },
 *     inactive: { text: 'Inactive', color: 'error' }
 *   }}
 * />
 * ```
 */
export const Label = ({ value, map }: LabelProps): ReactElement => {
  const { text, color } = map[value] ?? { text: 'Unknown', color: 'default' };

  return (
    <Chip
      label={text}
      color={color}
    />
  );
};

export default Label;

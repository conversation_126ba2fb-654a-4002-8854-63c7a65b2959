import STATUS_LABEL_MAP from '@/constants/status-labels.constant';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { Label } from './Label';

const meta = {
  component: Label,
  parameters: {
    layout: 'centered',
  },
} satisfies Meta<typeof Label>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    value: 'Unknown',
    map: {
      default: {
        text: 'Unknown',
        color: 'default',
      },
    },
  },
};

export const StatusLabels: Story = {
  args: {
    value: 'active',
    map: STATUS_LABEL_MAP,
  },
};

import type { ChipProps } from '@mui/material';

/**
 * Props for the Label component
 */
export interface LabelProps {
  /** The value to look up in the label map */
  value: string;

  /** The label map to look up the value in */
  map: LabelMap;
}

/**
 * A map of values to label configurations
 */
export interface LabelMap {
  [key: string]: {
    /** The text to display in the chip */
    text: string;

    /** The color of the chip */
    color: ChipProps['color'];
  };
}

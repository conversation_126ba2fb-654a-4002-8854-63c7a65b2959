import type {
  GridLoadingOverlayVariant,
  GridLocaleText,
  GridPremiumSlotsComponent,
  GridSlotsComponentsProps,
} from '@mui/x-data-grid-premium';
import { useTranslation } from 'react-i18next';
import { DEFAULT_TOOLBAR_PROPS, PAGINATION_PAGE_SIZE_OPTIONS } from './DataGrid.constant';
import { type DataGridProps } from './DataGrid.type';
import { useLocaleText } from './DataGrid.util';
import { CustomNoResultsOverlay, CustomNoRowsOverlay } from './parts/CustomGridOverlay';
import CustomGridToolbar from './parts/CustomGridToolbar';
import { DataGridWrapper } from './styles/DataGrid.style';

/**
 * A customizable version of the DataGrid component from @mui/x-data-grid-premium.
 * This component allows for the integration of custom toolbar and overlay components,
 * as well as additional configuration options for the toolbar.
 *
 * @example
 * ```tsx
 * <DataGrid
 *   rows={rows}
 *   columns={columns}
 *   autoPageSize
 *   disableRowSelectionOnClick
 *   disableAggregation
 *   toolbarProps={{
 *     showQuickFilter: true,
 *     showDensityButton: true,
 *     showColumnsButton: true,
 *     showFilterButton: true,
 *     showExportButton: true,
 *     csvOptions: {
 *       fileName: 'export',
 *     },
 *     quickFilterProps: {
 *       debounceMs: 500,
 *     },
 *   }}
 * />
 * ```
 */
export const DataGrid = ({
  toolbarProps = DEFAULT_TOOLBAR_PROPS,
  slots,
  slotProps,
  ...otherProps
}: DataGridProps) => {
  const defaultSlots: Partial<GridPremiumSlotsComponent> = {
    toolbar: CustomGridToolbar,
    noRowsOverlay: CustomNoRowsOverlay,
    noResultsOverlay: CustomNoResultsOverlay,
    ...slots,
  };

  const defaultSlotProps: Partial<GridSlotsComponentsProps> = {
    toolbar: toolbarProps,
    loadingOverlay: {
      variant: 'linear-progress' as GridLoadingOverlayVariant,
      noRowsVariant: 'linear-progress' as GridLoadingOverlayVariant,
    },
    ...slotProps,
  };

  const { i18n, t } = useTranslation();
  const localeText: Partial<GridLocaleText> = useLocaleText(i18n.language as Language, t);

  // "params" related error is mui datagrid problem
  // https://github.com/mui/mui-x/issues/17679
  return (
    <DataGridWrapper
      slots={defaultSlots}
      slotProps={defaultSlotProps}
      pageSizeOptions={PAGINATION_PAGE_SIZE_OPTIONS}
      disableRowSelectionOnClick
      disableAggregation
      showToolbar
      localeText={localeText}
      disablePivoting
      {...otherProps}
    />
  );
};

export default DataGrid;

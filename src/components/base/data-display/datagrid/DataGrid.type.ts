import type {
  DataGridPremiumProps,
  GridCsvExportOptions,
  GridToolbarProps,
  GridToolbarQuickFilterProps,
  ToolbarPropsOverrides,
} from '@mui/x-data-grid-premium';

declare module '@mui/x-data-grid-premium' {
  interface ToolbarPropsOverrides {
    showQuickFilter?: boolean;
    showDensityButton?: boolean;
    showColumnsButton?: boolean;
    showFilterButton?: boolean;
    showExportButton?: boolean;
  }
}

export interface CustomGridToolbarProps
  extends Omit<GridToolbarProps, 'excelOptions' | 'printOptions' | 'slotProps'>,
    ToolbarPropsOverrides {
  /** Determines if the quick filter input should be displayed.
   * @default false
   */
  showQuickFilter?: boolean;

  /** Determines if the density button should be displayed.
   * @default false
   */
  showDensityButton?: boolean;

  /** Determines if the columns button should be displayed.
   * @default false
   */
  showColumnsButton?: boolean;

  /** Determines if the filter button should be displayed.
   * @default false
   */
  showFilterButton?: boolean;

  /** Determines if the export button should be displayed.
   * @default false
   */
  showExportButton?: boolean;

  /** Custom CSV export options for controlling the export behavior */
  csvOptions?: GridCsvExportOptions;

  /** Custom quick filter input props for controlling the filter behavior */
  quickFilterProps?: GridToolbarQuickFilterProps;
}

export interface DataGridProps extends DataGridPremiumProps {
  /**
   * Configuration options for the toolbar.
   * Allows customization of toolbar features such as quick filter, density button, etc.
   */
  toolbarProps?: CustomGridToolbarProps;
}

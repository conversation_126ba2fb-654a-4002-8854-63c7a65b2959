import { Button } from '@/components/base/inputs/button';
import { transformText } from '@/utils/text-transform.util';
import FilterList from '@mui/icons-material/FilterList';
import { Badge } from '@mui/material';
import { FilterPanelTrigger, ToolbarButton } from '@mui/x-data-grid-premium';
import { useTranslation } from 'react-i18next';

/**
 * CustomFilterButton component renders a filter button for DataGrid
 * with a badge indicating the number of active filters
 */
const CustomFilterButton = () => {
  const { t } = useTranslation();

  return (
    <FilterPanelTrigger
      render={(props, state) => (
        <ToolbarButton
          {...props}
          render={
            <Button
              arrow={true}
              color="primary"
              title={transformText(t('common.action.selectFilters'), 'sentenceCase')}
              size="small"
              tooltipProps={{
                placement: 'bottom',
              }}
              variant="text"
              startIcon={
                <Badge
                  overlap="rectangular"
                  color="primary"
                  badgeContent={state.filterCount}
                  anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
                >
                  <FilterList />
                </Badge>
              }
            >
              {t('common.label.filters')}
            </Button>
          }
        />
      )}
    />
  );
};

export default CustomFilterButton;

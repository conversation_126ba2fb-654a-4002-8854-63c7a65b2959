import i18n from '@/providers/i18n/i18n';
import { transformText } from '@/utils/text-transform.util';
import { getByXPath } from '@/utils/xpath-selector.util';
import { enUS, zhCN } from '@mui/x-data-grid/locales';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { expect, screen, waitFor } from 'storybook/test';
import CustomFilterButton from './CustomFilterButton';
import { withDataGrid } from '.storybook/decorators';

const meta = {
  component: CustomFilterButton,
  title: 'Components/base/data-display/datagrid/CustomFilterButton',
  tags: ['autodocs'],
  decorators: [(Story) => withDataGrid(Story)],
} satisfies Meta<typeof CustomFilterButton>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Story for the default CustomFilterButton component.
 */
export const Default: Story = {
  play: async ({ canvas, userEvent, step }) => {
    const localeText: Record<string, any> = {
      en: enUS.components.MuiDataGrid.defaultProps.localeText,
      zh: zhCN.components.MuiDataGrid.defaultProps.localeText,
    };

    await step('Able to show tooltip', async () => {
      await userEvent.hover(canvas.getByText(i18n.t('common.label.filters')));

      const tooltip = await waitFor(() =>
        getByXPath(
          `//div[@role="tooltip" and contains(@class, "MuiTooltip-popper") and .="${transformText(i18n.t('common.action.selectFilters'), 'sentenceCase')}"]`,
          document
        )
      );

      expect(tooltip).toBeInTheDocument();
    });

    await step('Click filters button', async () => {
      await userEvent.click(canvas.getByText(i18n.t('common.label.filters')));
    });

    await step('Filter is shown when clicked', async () => {
      const addIcon = await waitFor(() => screen.getByTestId('AddIcon'));
      expect(addIcon).toBeInTheDocument();

      const removeIcon = await waitFor(() => screen.getByTestId('DeleteIcon'));
      expect(removeIcon).toBeInTheDocument();
    });

    await step('Filter badge count is shown', async () => {
      await userEvent.type(
        screen.getByPlaceholderText(localeText[i18n.language].filterPanelInputPlaceholder),
        'Test'
      );

      const badge = await waitFor(() =>
        getByXPath('//span[contains(@class, "MuiBadge-badge") and text()="1"]', document)
      );
      expect(badge).toBeInTheDocument();

      await userEvent.click(screen.getByTestId('DeleteIcon'));
    });

    await step('Filter can be closed with escape button', async () => {
      await userEvent.keyboard('{Escape}');

      await waitFor(() => {
        expect(
          screen.queryByPlaceholderText(localeText[i18n.language].filterPanelInputPlaceholder)
        ).not.toBeInTheDocument();
      });
    });
  },
};

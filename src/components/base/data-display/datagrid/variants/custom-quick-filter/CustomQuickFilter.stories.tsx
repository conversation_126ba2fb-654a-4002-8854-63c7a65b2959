import i18n from '@/providers/i18n/i18n';
import { transformText } from '@/utils/text-transform.util';
import { getAllByXPath } from '@/utils/xpath-selector.util';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { expect, waitFor } from 'storybook/test';
import CustomQuickFilter from './CustomQuickFilter';
import { withDataGrid } from '.storybook/decorators';

const meta = {
  component: CustomQuickFilter,
  title: 'Components/base/data-display/datagrid/CustomQuickFilter',
  tags: ['autodocs'],
  decorators: [(Story) => withDataGrid(Story)],
} satisfies Meta<typeof CustomQuickFilter>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Story for the default CustomQuickFilter component.
 */
export const Default: Story = {
  args: {
    quickFilterProps: {
      debounceMs: 100,
    },
  },
  play: async ({ canvas, userEvent, step }) => {
    await step('Should default expanded', () => {
      expect(
        canvas.getByPlaceholderText(
          transformText(i18n.t('common.action.searchKeywords'), 'sentenceCase')
        )
      ).toBeInTheDocument();
    });

    await step('Should show clear button when typed', async () => {
      // slow down for initialise
      await new Promise((resolve) => setTimeout(resolve, 200));

      await userEvent.type(canvas.getByRole('searchbox'), 'Doe');

      expect(canvas.getByTestId('CancelIcon')).toBeInTheDocument();
    });

    await step('Should filter rows correctly', () => {
      const result = getAllByXPath('//div[@role="gridcell" and contains(text(), "Doe")]', document);

      expect(result).toHaveLength(2);
    });

    await step('Should clear all text with escape button', async () => {
      // slow down for event finish
      await new Promise((resolve) => setTimeout(resolve, 200));

      await userEvent.keyboard('{Escape}');
      await waitFor(() => {
        expect(canvas.queryByTestId('CancelIcon')).not.toBeInTheDocument();
      });
    });
  },
};

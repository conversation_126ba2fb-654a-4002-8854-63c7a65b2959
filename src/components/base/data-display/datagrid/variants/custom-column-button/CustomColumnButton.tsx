import { Button } from '@/components/base/inputs/button';
import { transformText } from '@/utils/text-transform.util';
import ViewColumn from '@mui/icons-material/ViewColumn';
import { ColumnsPanelTrigger, ToolbarButton } from '@mui/x-data-grid-premium';
import { useTranslation } from 'react-i18next';

/**
 * CustomColumnButton component provides column visibility control for DataGrid
 * with a button that triggers the columns selection panel
 */
const CustomColumnButton = () => {
  const { t } = useTranslation();

  return (
    <ColumnsPanelTrigger
      render={
        <ToolbarButton
          render={
            <Button
              arrow={true}
              color="primary"
              title={transformText(t('common.action.selectColumns'), 'sentenceCase')}
              size="small"
              tooltipProps={{
                placement: 'bottom',
              }}
              variant="text"
              startIcon={<ViewColumn />}
            >
              {t('common.label.columns')}
            </Button>
          }
        />
      }
    />
  );
};

export default CustomColumnButton;

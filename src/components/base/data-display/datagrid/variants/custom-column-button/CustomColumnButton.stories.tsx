import i18n from '@/providers/i18n/i18n';
import { transformText } from '@/utils/text-transform.util';
import { getByXPath } from '@/utils/xpath-selector.util';
import { enUS, zhCN } from '@mui/x-data-grid/locales';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { expect, screen, waitFor } from 'storybook/test';
import CustomColumnButton from './CustomColumnButton';
import { withDataGrid } from '.storybook/decorators';

const meta = {
  component: CustomColumnButton,
  title: 'Components/base/data-display/datagrid/CustomColumnButton',
  tags: ['autodocs'],
  decorators: [(Story) => withDataGrid(Story)],
} satisfies Meta<typeof CustomColumnButton>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Story for the default CustomColumnButton component.
 */
export const Default: Story = {
  play: async ({ canvas, userEvent, step }) => {
    const localeText: Record<string, any> = {
      en: enUS.components.MuiDataGrid.defaultProps.localeText,
      zh: zhCN.components.MuiDataGrid.defaultProps.localeText,
    };

    await step('Able to show tooltip', async () => {
      await userEvent.hover(canvas.getByText(i18n.t('common.label.columns')));

      const tooltip = await waitFor(() =>
        getByXPath(
          `//div[@role="tooltip" and contains(@class, "MuiTooltip-popper") and .="${transformText(i18n.t('common.action.selectColumns'), 'sentenceCase')}"]`,
          document
        )
      );

      expect(tooltip).toBeInTheDocument();
    });

    await step('Click columns button', async () => {
      await userEvent.click(canvas.getByText(i18n.t('common.label.columns')));
    });

    await step('Menu is shown when clicked', async () => {
      const menu = await waitFor(() =>
        screen.getByPlaceholderText(localeText[i18n.language].columnsManagementSearchTitle)
      );
      expect(menu).toBeInTheDocument();
    });

    await step('Menu can be closed with escape button', async () => {
      await userEvent.keyboard('{Escape}');

      await waitFor(() => {
        expect(
          screen.queryByPlaceholderText(i18n.t('common.label.search'))
        ).not.toBeInTheDocument();
      });
    });
  },
};

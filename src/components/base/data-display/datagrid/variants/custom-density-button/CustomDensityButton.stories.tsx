import i18n from '@/providers/i18n/i18n';
import { transformText } from '@/utils/text-transform.util';
import { getByXPath } from '@/utils/xpath-selector.util';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { expect, screen, waitFor, within } from 'storybook/test';
import CustomDensityButton from './CustomDensityButton';
import { withDataGrid } from '.storybook/decorators';

const meta = {
  component: CustomDensityButton,
  title: 'Components/base/data-display/datagrid/CustomDensityButton',
  tags: ['autodocs'],
  decorators: [(Story) => withDataGrid(Story)],
} satisfies Meta<typeof CustomDensityButton>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Story for the default CustomDensityButton component.
 */
export const Default: Story = {
  play: async ({ canvas, userEvent, step }) => {
    await step('Able to show tooltip', async () => {
      await userEvent.hover(canvas.getByText(i18n.t('common.label.density')));

      const tooltip = await waitFor(() =>
        getByXPath(
          `//div[@role="tooltip" and contains(@class, "MuiTooltip-popper") and .="${transformText(i18n.t('common.action.selectDensity'), 'sentenceCase')}"]`,
          document
        )
      );

      expect(tooltip).toBeInTheDocument();
    });

    await step('Click density button', async () => {
      await userEvent.click(canvas.getByText(i18n.t('common.label.density')));
    });

    const menu = await waitFor(() => screen.getByRole('menu'));
    await step('Menu is shown when clicked', () => {
      expect(menu).toBeInTheDocument();
    });

    await step('Menu item is correct', () => {
      const menuItems = screen.getAllByRole('menuitem');
      expect(menuItems).toHaveLength(3);

      expect(
        within(menu).getByText(transformText(i18n.t('common.label.compact'), 'sentenceCase'))
      ).toBeInTheDocument();
      expect(
        within(menu).getByText(transformText(i18n.t('common.label.standard'), 'sentenceCase'))
      ).toBeInTheDocument();
      expect(
        within(menu).getByText(transformText(i18n.t('common.label.comfortable'), 'sentenceCase'))
      ).toBeInTheDocument();

      expect(within(menu).getByTestId('ViewHeadlineIcon')).toBeInTheDocument();
      expect(within(menu).getByTestId('TableRowsIcon')).toBeInTheDocument();
      expect(within(menu).getByTestId('ViewStreamIcon')).toBeInTheDocument();
    });

    await step('Menu item default should select standard', () => {
      const selected = getByXPath(
        `//li[contains(@class, "Mui-selected") and contains(.,"${transformText(i18n.t('common.label.standard'), 'sentenceCase')}")]`,
        document
      );

      expect(selected).toBeInTheDocument();
    });

    await step('Menu can be closed with escape button', async () => {
      await userEvent.keyboard('{Escape}');

      await waitFor(() => {
        expect(screen.queryByRole('menu')).not.toBeInTheDocument();
      });
    });

    await step('Icon should change based on selected density', async () => {
      const actions: [string, string][] = [
        ['common.label.compact', 'ViewHeadlineIcon'],
        ['common.label.comfortable', 'ViewStreamIcon'],
        ['common.label.standard', 'TableRowsIcon'],
      ];

      for (const [label, icon] of actions) {
        await userEvent.click(canvas.getByText(i18n.t('common.label.density')));
        await waitFor(() => screen.getByRole('menu'));
        await userEvent.click(screen.getByText(transformText(i18n.t(label), 'sentenceCase')));
        await waitFor(() => {
          expect(canvas.getByTestId(icon)).toBeInTheDocument();
        });
        await userEvent.keyboard('{Escape}');
      }
    });
  },
};

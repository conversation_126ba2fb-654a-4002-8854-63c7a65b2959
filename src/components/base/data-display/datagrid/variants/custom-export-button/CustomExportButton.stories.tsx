import i18n from '@/providers/i18n/i18n';
import { transformText } from '@/utils/text-transform.util';
import { getByXPath } from '@/utils/xpath-selector.util';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { expect, screen, waitFor, within } from 'storybook/test';
import CustomExportButton from './CustomExportButton';
import { withDataGrid } from '.storybook/decorators';

const meta = {
  component: CustomExportButton,
  title: 'Components/base/data-display/datagrid/CustomExportButton',
  tags: ['autodocs'],
  decorators: [(Story) => withDataGrid(Story)],
} satisfies Meta<typeof CustomExportButton>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Story for the default CustomExportButton component.
 */
export const Default: Story = {
  args: {
    csvOptions: {
      fileName: 'export',
    },
  },
  play: async ({ canvas, userEvent, step }) => {
    await step('Able to show tooltip', async () => {
      await userEvent.hover(canvas.getByText(i18n.t('common.label.export')));

      const tooltip = await waitFor(() =>
        getByXPath(
          `//div[@role="tooltip" and contains(@class, "MuiTooltip-popper") and .="${transformText(i18n.t('common.action.selectExport'), 'sentenceCase')}"]`,
          document
        )
      );

      expect(tooltip).toBeInTheDocument();
    });

    await step('Click export button', async () => {
      await userEvent.click(canvas.getByText(i18n.t('common.label.export')));
    });

    const menu = await waitFor(() => screen.getByRole('menu'));
    await step('Menu is shown when clicked', () => {
      expect(menu).toBeInTheDocument();
    });

    await step('Menu item is correct', () => {
      const menuItems = screen.getAllByRole('menuitem');
      expect(menuItems).toHaveLength(3);

      expect(
        within(menu).getByText(
          transformText(i18n.t('common.action.exportCurrentPage'), 'sentenceCase')
        )
      ).toBeInTheDocument();
      expect(
        within(menu).getByText(transformText(i18n.t('common.action.exportAll'), 'sentenceCase'))
      ).toBeInTheDocument();
      expect(
        within(menu).getByText(
          transformText(i18n.t('common.action.exportFiltered'), 'sentenceCase')
        )
      ).toBeInTheDocument();
    });

    await step('Menu can be closed with escape button', async () => {
      await userEvent.keyboard('{Escape}');

      await waitFor(() => {
        expect(screen.queryByRole('menu')).not.toBeInTheDocument();
      });
    });
  },
};

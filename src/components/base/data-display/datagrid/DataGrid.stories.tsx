import { PAGINATION_PAGE_SIZE } from '@/components/base/data-display/datagrid';
import { getSecureRandomIndex, getSecureRandomInt } from '@/utils/random.util';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import DataGrid from './DataGrid';

const meta = {
  title: 'Components/base/data-display/DataGrid',
  component: DataGrid,
  tags: ['autodocs'],
} satisfies Meta<typeof DataGrid>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Generates an array of sample data objects for use in DataGrid stories.
 */
const generateSampleData = (count = 20) => {
  const statuses = ['Active', 'Inactive', 'Pending', 'Suspended', 'Archived'];
  const names = [
    '<PERSON>',
    '<PERSON> <PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    '<PERSON>',
  ];

  /* eslint-disable sonarjs/pseudo-random */
  return Array.from({ length: count }, (_, i) => {
    const id = (i + 1).toString();
    const nameIndex = getSecureRandomIndex(names.length);
    const statusIndex = getSecureRandomIndex(statuses.length);
    const age = getSecureRandomInt(18, 67).toString();

    // Generate random date within the last 2 years
    const date = new Date();
    date.setFullYear(date.getFullYear() - getSecureRandomInt(0, 1));
    date.setMonth(getSecureRandomInt(0, 11));
    date.setDate(getSecureRandomInt(1, 28));
    date.setHours(getSecureRandomInt(0, 23));
    date.setMinutes(getSecureRandomInt(0, 59));
    date.setSeconds(getSecureRandomInt(0, 59));

    const createdAt = date.toISOString().replace('T', ' ').substring(0, 19);

    return {
      id,
      name: names[nameIndex],
      age,
      status: statuses[statusIndex],
      createdAt,
      email: `${names[nameIndex]!.toLowerCase()}${id}@example.com`,
      department: ['HR', 'IT', 'Finance', 'Marketing', 'Operations'][getSecureRandomInt(0, 4)],
      location: ['New York', 'Singapore', 'London', 'Tokyo', 'Mumbai', 'Sydney', 'Berlin'][
        getSecureRandomInt(0, 6)
      ],
    };
  });
};

const sampleRows = generateSampleData();

/**
 * Story for the default DataGrid component with sample data.
 */
export const Default: Story = {
  args: {
    rows: sampleRows,
    columns: [
      { field: 'id', headerName: 'ID', width: 70 },
      { field: 'name', headerName: 'Name', width: 150 },
      { field: 'age', headerName: 'Age', width: 80, type: 'number' },
      { field: 'email', headerName: 'Email', width: 220 },
      { field: 'status', headerName: 'Status', width: 120 },
      { field: 'department', headerName: 'Department', width: 150 },
      { field: 'location', headerName: 'Location', width: 150 },
      { field: 'createdAt', headerName: 'Created at', width: 180 },
    ],
    pagination: true,
    toolbarProps: {
      showQuickFilter: true,
      showDensityButton: true,
      showColumnsButton: true,
      showFilterButton: true,
      showExportButton: true,
      csvOptions: {
        fileName: 'users',
      },
      quickFilterProps: {
        debounceMs: 500,
      },
    },
    initialState: {
      pagination: {
        paginationModel: {
          pageSize: PAGINATION_PAGE_SIZE,
        },
      },
    },
  },
};

/**
 * Story for the DataGrid component when loading.
 */
export const Loading: Story = {
  args: {
    ...Default.args,
    loading: true,
    rows: [],
  },
};

/**
 * Story for the DataGrid component when there is no data.
 */
export const NoData: Story = {
  args: {
    ...Loading.args,
    loading: false,
  },
};

/**
 * Story for the DataGrid component when no record from search result.
 */
export const NoResult: Story = {
  args: {
    ...Default.args,
    initialState: {
      ...Default.args.initialState,
      filter: {
        filterModel: {
          items: [],
          quickFilterValues: ['No result search'],
        },
      },
    },
  },
};

/**
 * Story for the DataGrid component without toolbar button.
 */
export const NoToolbar: Story = {
  args: {
    ...Default.args,
    toolbarProps: {
      ...Default.args.toolbarProps,
      showQuickFilter: false,
      showDensityButton: false,
      showColumnsButton: false,
      showFilterButton: false,
      showExportButton: false,
    },
  },
};

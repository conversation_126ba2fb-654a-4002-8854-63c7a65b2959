'use client';

import NProgressLib from 'nprogress';
import { useEffect } from 'react';
import { type NProgressProps } from './NProgress.type';

/**
 * NProgress component that shows a loading bar at the top of the page
 * when navigating between pages or when links are clicked.
 *
 * Uses NProgress library under the hood.
 *
 * @example
 * ```tsx
 * // In your layout or app component
 * <NProgress />
 * ```
 */
export const NProgress = ({ showSpinner = false }: NProgressProps) => {
  useEffect(() => {
    // Configure NProgress
    NProgressLib.configure({ showSpinner });

    const handleAnchorClick = (event: MouseEvent) => {
      const targetUrl = (event.currentTarget as HTMLAnchorElement).href;
      const currentUrl = window.location.href;
      if (targetUrl !== currentUrl) {
        NProgressLib.start();
      }
    };

    const handleMutation: MutationCallback = () => {
      const anchorElements: NodeListOf<HTMLAnchorElement> = document.querySelectorAll('a[href]');
      anchorElements.forEach((anchor) => anchor.addEventListener('click', handleAnchorClick));
    };

    // Observe DOM changes to attach click handlers to new links
    const mutationObserver = new MutationObserver(handleMutation);
    mutationObserver.observe(document, { childList: true, subtree: true });

    // Override history.pushState to complete progress when navigation is done
    type PushStateInput = [data: unknown, unused: string, url?: string | URL | null | undefined];
    window.history.pushState = new Proxy(window.history.pushState, {
      apply: (target, thisArg, argArray: PushStateInput) => {
        NProgressLib.done();
        return target.apply(thisArg, argArray);
      },
    });

    // Clean up
    return () => {
      mutationObserver.disconnect();
      // We can't easily restore the original pushState, but that's usually not a problem
    };
  }, [showSpinner]);

  return null;
};

export default NProgress;

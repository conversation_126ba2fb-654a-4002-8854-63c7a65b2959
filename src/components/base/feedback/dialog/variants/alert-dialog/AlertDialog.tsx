import { useIsMobile } from '@/hooks/ui/use-is-mobile.hook';
import { Dialog, DialogContent, Stack } from '@mui/material';
import type { AlertDialogProps } from '../../Dialog.type';
import { DialogActionButtons, DialogBody } from '../../parts/DialogParts';

/**
 * A reusable alert dialog component with title, message, and actions.
 *
 * @example
 * ```tsx
 * <AlertDialog
 *   open={isOpen}
 *   onClose={handleClose}
 *   title="Delete Items"
 *   message="This action cannot be undone."
 *   note="All associated data will be removed."
 *   actions={
 *     <>
 *       <Button onClick={handleClose}>Cancel</Button>
 *       <Button onClick={handleConfirm}>Confirm</Button>
 *     </>
 *   }
 * />
 * ```
 */
export const AlertDialog = ({
  title,
  message,
  note,
  actions,
  children,
  ...dialogProps
}: AlertDialogProps) => {
  const isMobile = useIsMobile();
  return (
    <Dialog
      aria-labelledby="alert-dialog-title"
      aria-describedby="alert-dialog-description"
      maxWidth="sm"
      sx={{
        '.MuiDialog-container': {
          alignItems: isMobile ? 'flex-end' : 'center',
        },
      }}
      {...dialogProps}
    >
      <DialogContent>
        <Stack
          spacing={2}
          justifyContent="center"
          direction={isMobile ? 'column' : 'row'}
          alignItems={isMobile ? 'center' : 'flex-start'}
        >
          <DialogBody
            title={title}
            message={message}
            note={note}
          >
            {children}
          </DialogBody>
        </Stack>
      </DialogContent>
      {actions && <DialogActionButtons>{actions}</DialogActionButtons>}
    </Dialog>
  );
};

export default AlertDialog;

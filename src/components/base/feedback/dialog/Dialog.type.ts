import type { DialogProps } from '@mui/material';
import type { ReactNode } from 'react';

/**
 * Props for the AlertDialog component
 */
export interface AlertDialogProps extends Omit<DialogProps, 'title'> {
  /** The title of the dialog. */
  title?: string;
  /** The message body of the dialog. */
  message?: string;
  /** The optional note of the dialog. */
  note?: string;
  /** The optional children to be displayed inside the dialog. */
  children?: ReactNode;
  /** The actions to be displayed at the bottom of the dialog. */
  actions?: ReactNode;
}

/**
 * Props for the DialogBody component
 */
export interface DialogBodyProps {
  /** The title of the dialog. */
  title?: string;
  /** The message body of the dialog. */
  message?: string;
  /** The optional note of the dialog. */
  note?: string;
  /** The optional children to be displayed inside the dialog. */
  children?: ReactNode;
}

/**
 * Props for the DialogActionButtons component
 */
export interface DialogActionButtonsProps {
  /** The children to be displayed inside the dialog actions. */
  children: ReactNode;
}

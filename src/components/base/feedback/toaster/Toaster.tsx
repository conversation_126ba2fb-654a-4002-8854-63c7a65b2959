import { alpha, useTheme } from '@mui/material/styles';
import { Toaster as HotToaster } from 'react-hot-toast';
import { type ToasterProps } from './Toaster.type';

/**
 * Toaster component to display toast notifications
 *
 * @remarks
 * This component is a wrapper of `react-hot-toast` Toaster component.
 * It provides a set of default styles and options for displaying toast notifications.
 *
 * @example
 * ```tsx
 * <Toaster />
 * ```
 */
export const Toaster = ({
  position = 'top-right',
  gutter = 24,
  reverseOrder = true,
  ...otherProps
}: ToasterProps) => {
  const theme = useTheme();

  return (
    <HotToaster
      position={position}
      gutter={gutter}
      reverseOrder={reverseOrder}
      toastOptions={{
        style: {
          backdropFilter: 'blur(3px)',
          fontWeight: 500,
          padding: theme.spacing(2),
          background: alpha(theme.palette.background.paper, 0.9),
          color:
            theme.palette.mode === 'dark' ? theme.palette.neutral[100] : theme.palette.neutral[900],
          boxShadow: theme.shadows[21],
          border: 0,
        },
        ...otherProps.toastOptions,
      }}
      containerStyle={{
        top: 24,
        left: 24,
        bottom: 24,
        right: 24,
        ...otherProps.containerStyle,
      }}
      {...otherProps}
    />
  );
};

export default Toaster;

import { Stack } from '@mui/material';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { FormAlert } from './FormAlert';

/**
 * Stories for the FormAlert component
 */
const meta = {
  title: 'Components/base/feedback/FormAlert',
  component: FormAlert,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    severity: {
      control: 'select',
      options: ['error', 'warning', 'info', 'success'],
      description: 'The alert severity level',
    },
    title: {
      control: 'text',
      description: 'Optional title of the alert',
    },
    message: {
      control: 'text',
      description: 'The alert message content',
    },
  },
} satisfies Meta<typeof FormAlert>;

export default meta;

type Story = StoryObj<typeof meta>;

/**
 * Default usage of FormAlert
 */
export const Default: Story = {
  args: {
    severity: 'error',
    title: 'Alert Title',
    message: 'This is an alert message.',
  },
};

/**
 * Variants of different alert severities
 */
export const Severities: Story = {
  parameters: {
    controls: { disable: true },
  },
  render: () => (
    <Stack spacing={2}>
      <FormAlert
        severity="error"
        title="Error"
        message="Something went wrong. Please try again."
      />
      <FormAlert
        severity="warning"
        title="Warning"
        message="Be careful! This action is irreversible."
      />
      <FormAlert
        severity="info"
        title="Information"
        message="You have unread notifications."
      />
      <FormAlert
        severity="success"
        title="Success"
        message="Your action has been successfully completed."
      />
    </Stack>
  ),
};

/**
 * Alert without title
 */
export const WithoutTitle: Story = {
  args: {
    severity: 'warning',
    message: 'This alert has no title, only a message.',
  },
};

/**
 * Alert without message
 */
export const WithoutMessage: Story = {
  args: {
    severity: 'warning',
    title: 'Warning',
    message: '',
  },
};

import { Button as <PERSON><PERSON><PERSON><PERSON><PERSON>, Tooltip } from '@mui/material';
import React, { type ReactElement } from 'react';
import { type ButtonProps } from './Button.type';
import { ButtonIcon, ButtonLight, ButtonSoft } from './styles';

/**
 * Core button component that wrapped around MUI's Button component that allows for custom styling.
 */
export const Button = ({
  customStyle,
  title,
  arrow = true,
  tooltipProps,
  ...props
}: ButtonProps) => {
  let ButtonComponent: ReactElement;
  switch (customStyle) {
    case 'soft':
      ButtonComponent = <ButtonSoft {...props} />;
      break;
    case 'icon':
      ButtonComponent = <ButtonIcon {...props} />;
      break;
    case 'light':
      ButtonComponent = <ButtonLight {...props} />;
      break;
    default:
      ButtonComponent = <MuiButton {...props} />;
      break;
  }

  return (
    <Tooltip
      arrow={arrow}
      title={title}
      {...tooltipProps}
    >
      {ButtonComponent}
    </Tooltip>
  );
};

export default Button;

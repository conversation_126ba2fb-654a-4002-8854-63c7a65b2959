import { PALETTE_COLOR_OPTIONS, type Pa<PERSON><PERSON><PERSON><PERSON><PERSON>ey } from '@/theme/colors';
import NotificationsNoneRoundedIcon from '@mui/icons-material/NotificationsNoneRounded';
import { Box, Stack, Typography } from '@mui/material';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { Button } from './Button';

/**
 * Button component stories.
 * Showcases the base MUI Button and our custom styled button variants.
 */
const meta = {
  title: 'Components/base/inputs/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    variant: {
      control: 'select',
      options: ['text', 'outlined', 'contained'],
      description: 'The variant to use',
    },
    color: {
      control: 'select',
      options: PALETTE_COLOR_OPTIONS,
    },
    size: {
      control: 'select',
      options: ['small', 'medium', 'large'],
      description: 'The size of the component',
    },
    disabled: {
      control: 'boolean',
      description: 'If true, the component is disabled',
    },
  },
} satisfies Meta<typeof Button>;

export default meta;

type Story = StoryObj<typeof meta>;

/**
 * Default But<PERSON> with basic styling
 */
export const Default: Story = {
  args: {
    children: 'Button',
    title: 'Default Button',
    color: 'primary',
    variant: 'contained',
  },
};

/**
 * ButtonIcon optimized for icon display with minimal padding and no minimum width
 */
export const Icon: Story = {
  args: {
    children: <NotificationsNoneRoundedIcon />,
    // variant: 'text',
    color: 'inherit',
    size: 'small',
    title: 'Notifications',
    customStyle: 'icon',
    sx: { width: 50, height: 50, borderRadius: 50 },
  },
};

/**
 * ButtonSoft with a soft appearance using alpha transparency
 */
export const Soft: Story = {
  args: {
    children: 'Soft Button',
    color: 'primary',
    customStyle: 'soft',
  },
};

/**
 * ButtonLight with a light appearance for dark backgrounds
 */
export const Light: Story = {
  args: {
    children: 'Light',
    color: 'primary',
    variant: 'contained',
    customStyle: 'light',
  },
  parameters: {
    backgrounds: { default: 'dark' },
  },
};

/**
 * Buttons with different variants
 */
export const Variants: Story = {
  parameters: {
    controls: { disable: true },
  },
  render: () => {
    const variants = ['text', 'outlined', 'contained'];

    return (
      <Stack spacing={3}>
        {variants.map((variant) => (
          <Box key={variant}>
            <Typography
              variant="subtitle2"
              sx={{ mb: 1 }}
            >
              {variant}
            </Typography>
            <Stack
              direction="row"
              spacing={1}
            >
              <Button
                variant={variant as any}
                color="primary"
              >
                Primary
              </Button>
              <Button
                variant={variant as any}
                color="secondary"
              >
                Secondary
              </Button>
              <Button
                variant={variant as any}
                color="success"
              >
                Success
              </Button>
              <Button
                variant={variant as any}
                color="error"
              >
                Error
              </Button>
            </Stack>
          </Box>
        ))}
      </Stack>
    );
  },
};

/**
 * Buttons with different colors
 */
export const Colors: Story = {
  parameters: {
    controls: { disable: true },
  },
  render: () => {
    const colors: PaletteColorKey[] = [
      'primary',
      'secondary',
      'success',
      'error',
      'warning',
      'info',
      'ultraViolet',
      'livingCoral',
      'emerald',
      'honeyGold',
    ];

    return (
      <Box>
        <Typography
          variant="body2"
          sx={{ mb: 2 }}
        >
          Buttons with different colors
        </Typography>
        <Stack
          direction="row"
          spacing={2}
          flexWrap="wrap"
          useFlexGap
        >
          {colors.map((color) => (
            <Box key={`button-${color}`}>
              <Button
                variant="contained"
                color={color as any}
              >
                {color}
              </Button>
            </Box>
          ))}
        </Stack>
      </Box>
    );
  },
};

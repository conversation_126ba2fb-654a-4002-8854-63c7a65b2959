import type { ButtonProps as MuiButtonProps, TooltipProps } from '@mui/material';
import type { ReactNode } from 'react';

export interface ButtonProps extends Omit<MuiButtonProps, 'title'> {
  /**
   * The custom style of button to render
   * - `'soft'`: A button with a soft appearance.
   * - `'icon'`: A button optimized for icon display.
   * - `'light'`: A button with a light appearance, useful for buttons on dark backgrounds.
   */
  customStyle?: 'soft' | 'icon' | 'light';

  /**
   * The tooltip title
   */
  title?: ReactNode;

  /**
   * Whether to show an arrow on the tooltip
   * @default true
   */
  arrow?: boolean;

  /**
   * Additional props to pass to the Tooltip component
   */
  tooltipProps?: Omit<TooltipProps, 'title' | 'children' | 'arrow'>;
}

/**
 * Base props for the ButtonTab component
 */
export interface BaseButtonTabProps {
  /**
   * The type of component to render
   */
  componentType: 'tab' | 'button';

  /**
   * The content of the button
   */
  children?: ReactNode;
}

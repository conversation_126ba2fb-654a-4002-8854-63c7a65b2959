import { alpha, Button, styled } from '@mui/material';

/**
 * A styled Button component with a soft appearance.
 * Uses alpha transparency for a subtle look.
 *
 * @example
 * ```tsx
 * <ButtonSoft color="primary">
 *   Soft Button
 * </ButtonSoft>
 * ```
 */
export const ButtonSoft = styled(Button)(({ theme, color }) => {
  // Since we've extended ButtonPropsColorOverrides, we can access the color directly
  // But we still need to handle special cases like 'inherit' which aren't in the palette
  let computedColor: string;

  if (!color || color === 'inherit') {
    // Use primary color for 'inherit' or undefined
    computedColor = theme.palette.primary.main;
  } else {
    // For all other colors, access the palette
    // We need to use type assertion to handle the complex palette type
    const paletteColor = theme.palette[color as keyof typeof theme.palette] as any;
    computedColor = paletteColor.main;
  }

  return {
    backgroundColor: alpha(computedColor, 0.08),
    color: computedColor,

    '&:hover': {
      backgroundColor: alpha(computedColor, 0.12),
      color: computedColor,
    },

    '&:disabled': {
      backgroundColor: alpha(theme.palette.action.disabledBackground, 0.3),
    },
  };
});

export default ButtonSoft;

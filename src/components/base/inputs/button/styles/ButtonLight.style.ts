import { alpha, Button, styled } from '@mui/material';

/**
 * A styled Button component with a light appearance.
 * Useful for buttons on dark backgrounds.
 *
 * @example
 * ```tsx
 * <ButtonLight variant="contained">
 *   Light Button
 * </ButtonLight>
 * ```
 */
export const ButtonLight = styled(Button)(({ theme }) => ({
  background: alpha(theme.palette.common.white, 0.08),
  color: alpha(theme.palette.common.white, 0.9),
  borderColor: alpha(theme.palette.common.white, 0.12),

  '&:hover': {
    background: alpha(theme.palette.common.white, 0.1),
    color: theme.palette.common.white,
  },

  '&.MuiButton-outlined': {
    borderColor: alpha(theme.palette.common.white, 0.12),

    '&:hover': {
      borderColor: alpha(theme.palette.common.white, 0.16),
    },
  },
}));

export default ButtonLight;

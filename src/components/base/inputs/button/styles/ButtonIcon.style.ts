import { Button, styled } from '@mui/material';

/**
 * A styled Button component optimized for icon display.
 * Has minimal padding and no minimum width.
 *
 * @example
 * ```tsx
 * <ButtonIcon color="primary" variant="contained">
 *   <AddIcon />
 * </ButtonIcon>
 * ```
 */
export const ButtonIcon = styled(Button)(({ theme }) => ({
  minWidth: 0,
  padding: theme.spacing(1),

  '.MuiButton-startIcon': {
    margin: 0,
  },

  '&.MuiButton-sizeSmall': {
    padding: theme.spacing(0.638),
  },
}));

export default ButtonIcon;

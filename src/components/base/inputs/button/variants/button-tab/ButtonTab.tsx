import { type ButtonProps, type TabProps } from '@mui/material';
import React from 'react';
import { type BaseButtonTabProps } from '../../Button.type';
import { StyledButton, StyledTab } from '../../styles/ButtonTab.style';

/**
 * Props for the ButtonTab component
 */
type ButtonTabProps =
  | (BaseButtonTabProps & TabProps & { componentType: 'tab' })
  | (BaseButtonTabProps & ButtonProps & { componentType: 'button' });

/**
 * A component that can render either as a Tab or a Button with tab-like styling.
 *
 * @example
 * ```tsx
 * <ButtonTab componentType="button" onClick={handleClick}>
 *   Tab Button
 * </ButtonTab>
 * ```
 */
export const ButtonTab = ({ componentType, children, ...otherProps }: ButtonTabProps) => {
  return componentType === 'tab' ? (
    <StyledTab {...(otherProps as TabProps)} />
  ) : (
    <StyledButton {...(otherProps as ButtonProps)}>{children}</StyledButton>
  );
};

export default ButtonTab;

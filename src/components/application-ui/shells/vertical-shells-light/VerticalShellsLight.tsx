import { useMobileNav } from '@/hooks/navigation/use-mobile-nav.hook';
import { useSidebar } from '@/hooks/ui/use-sidebar.hook';
import { HEADER_HEIGHT, SIDEBAR_WIDTH_COLLAPSED } from '@/theme/utils';
import { Box } from '@mui/material';
import { Header } from './parts/header';
import { Sidebar } from './parts/sidebar';
import type { VerticalShellsLightProps } from './VerticalShellsLight.type';

/**
 * A vertical layout shell with a light theme.
 * Includes a header, sidebar, and content area.
 *
 * @example
 * ```tsx
 * <VerticalShellsLight menuItems={menuItems}>
 *   <PageContent />
 * </VerticalShellsLight>
 * ```
 */
export const VerticalShellsLight = ({ children, menuItems }: VerticalShellsLightProps) => {
  const mobileNav = useMobileNav();
  const { isSidebarCollapsed } = useSidebar();

  return (
    <>
      <Sidebar
        menuItems={menuItems}
        onClose={mobileNav.handleClose}
        open={mobileNav.open}
        onOpen={mobileNav.handleOpen}
      />
      <Box
        flex={1}
        overflow="hidden"
        sx={{
          display: 'flex',
          flexDirection: 'column',
          paddingTop: `${HEADER_HEIGHT * 1.5}px`,
          backgroundColor: 'background.paper',
          ml: {
            xs: 0,
            lg: isSidebarCollapsed ? `${SIDEBAR_WIDTH_COLLAPSED}px` : 0,
          },
        }}
      >
        <Header onMobileNav={mobileNav.handleOpen} />
        {children}
      </Box>
    </>
  );
};

export default VerticalShellsLight;

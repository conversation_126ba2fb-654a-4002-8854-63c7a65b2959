import { LanguageDropdown } from '@/components/application-ui/app-header';
import {
  MobileMenuButton,
  NotificationsButton,
  UserProfileButton,
} from '@/components/application-ui/app-header/buttons';
import { getCommonButtonStyles } from '@/components/application-ui/app-header/buttons/Buttons.style';
import { Divider, Stack, useTheme } from '@mui/material';
import type { RightSectionProps } from '../Header.type';

/**
 * Right section of the header containing notifications, language dropdown, user profile, and mobile menu
 */
export const RightSection = ({
  smUp,
  lgUp,
  notifications,
  popover,
  onMobileNav,
}: RightSectionProps) => {
  const theme = useTheme();

  return (
    <Stack
      direction="row"
      divider={
        <Divider
          orientation="vertical"
          flexItem
        />
      }
      alignItems="center"
      spacing={{ xs: 1, sm: 2 }}
    >
      <Stack
        display="flex"
        spacing={0.5}
        direction="row"
        alignItems="center"
      >
        <NotificationsButton onClick={notifications.handleOpen} />
        {smUp && (
          <LanguageDropdown
            color="inherit"
            sx={getCommonButtonStyles(theme)}
          />
        )}
      </Stack>

      <UserProfileButton popover={popover} />
      {!lgUp && <MobileMenuButton onClick={onMobileNav} />}
    </Stack>
  );
};

export default RightSection;

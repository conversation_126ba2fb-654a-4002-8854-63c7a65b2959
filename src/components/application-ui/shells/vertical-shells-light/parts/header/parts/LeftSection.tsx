import { SearchButton } from '@/components/application-ui/app-header/buttons';
import { Logo } from '@/components/application-ui/app-sidebar/logo/Logo';
import { Divider, Stack, useMediaQuery, type Theme } from '@mui/material';
import type { LeftSectionProps } from '../Header.type';

/**
 * Left section of the header containing logo and search button
 */
export const LeftSection = ({ dialog }: LeftSectionProps) => {
  const lgUp = useMediaQuery((theme: Theme) => theme.breakpoints.up('lg'));

  return (
    <Stack
      direction="row"
      divider={
        <Divider
          orientation="vertical"
          flexItem
        />
      }
      alignItems="center"
      spacing={{ xs: 1, sm: 2 }}
    >
      {!lgUp && <Logo isLinkStatic />}
      {lgUp && <SearchButton onClick={dialog.handleOpen} />}
    </Stack>
  );
};

export default LeftSection;

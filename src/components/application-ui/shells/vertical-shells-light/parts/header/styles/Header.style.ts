import { HEADER_HEIGHT } from '@/theme/utils';
import { alpha, AppBar, styled } from '@mui/material';

/**
 * Styled AppBar component for the header
 */
export const HeaderWrapper = styled(AppBar)(({ theme }) => ({
  height: HEADER_HEIGHT,
  background: alpha(theme.palette.background.paper, 0.9),
  backdropFilter: 'blur(8px)',
  color: 'inherit',
  right: 0,
  left: 'auto',
  display: 'flex',
  transition: theme.transitions.create(['height']),
}));

import { useDialog } from '@/hooks/ui/use-dialog.hook';
import { usePopover } from '@/hooks/ui/use-popover.hook';

/**
 * Props for the Header component
 */
export interface HeaderProps {
  /**
   * Callback fired when the mobile navigation button is clicked
   */
  onMobileNav?: () => void;
}

/**
 * Props for the LeftSection component
 */
export interface LeftSectionProps {
  /**
   * Dialog hook return value
   */
  dialog: ReturnType<typeof useDialog>;
}

/**
 * Props for the RightSection component
 */
export interface RightSectionProps {
  /**
   * Whether the viewport is at least small size
   */
  smUp: boolean;

  /**
   * Whether the viewport is at least large size
   */
  lgUp: boolean;

  /**
   * Notifications dialog hook return value
   */
  notifications: ReturnType<typeof useDialog>;

  /**
   * User profile popover hook return value
   */
  popover: ReturnType<typeof usePopover>;

  /**
   * Callback fired when the mobile navigation button is clicked
   */
  onMobileNav?: () => void;
}

import { HeaderUserDropdown } from '@/components/application-ui/app-header';
import { useDialog } from '@/hooks/ui/use-dialog.hook';
import { usePopover } from '@/hooks/ui/use-popover.hook';
import useScrollDirection from '@/hooks/ui/use-scroll-direction.hook';
import { useSidebar } from '@/hooks/ui/use-sidebar.hook';
import { HEADER_HEIGHT, SIDEBAR_WIDTH, SIDEBAR_WIDTH_COLLAPSED } from '@/theme/utils';
import { Stack, useMediaQuery, type Theme } from '@mui/material';
import type { HeaderProps } from './Header.type';
import { LeftSection, RightSection } from './parts';
import { HeaderWrapper } from './styles/Header.style';

/**
 * Header component for the vertical shell layout
 * Contains search, notifications, language dropdown, and user profile
 */
export const Header = ({ onMobileNav }: HeaderProps) => {
  const scroll = useScrollDirection();
  const lgUp = useMediaQuery((theme: Theme) => theme.breakpoints.up('lg'));
  const smUp = useMediaQuery((theme: Theme) => theme.breakpoints.up('sm'));

  const { isSidebarCollapsed } = useSidebar();
  const dialog = useDialog();
  const popover = usePopover<HTMLButtonElement>();
  const notifications = useDialog();

  return (
    <HeaderWrapper
      sx={{
        height: scroll === 'down' ? HEADER_HEIGHT : HEADER_HEIGHT * 1.5,
        boxShadow: scroll === 'down' ? (theme) => theme.shadows[10] : (theme) => theme.shadows[7],
        width: {
          xs: '100%',
          lg: `calc(100% - ${isSidebarCollapsed ? SIDEBAR_WIDTH_COLLAPSED : SIDEBAR_WIDTH}px)`,
        },
      }}
    >
      <Stack
        px={2}
        flex={1}
        direction="row"
        justifyContent="space-between"
        alignItems="center"
      >
        <LeftSection dialog={dialog} />
        <RightSection
          smUp={smUp}
          lgUp={lgUp}
          notifications={notifications}
          popover={popover}
          onMobileNav={onMobileNav}
        />
      </Stack>
      <HeaderUserDropdown
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        transformOrigin={{ vertical: 'top', horizontal: 'center' }}
        anchorEl={popover.anchorRef.current}
        onClose={popover.handleClose}
        open={popover.open}
      />
    </HeaderWrapper>
  );
};

export default Header;

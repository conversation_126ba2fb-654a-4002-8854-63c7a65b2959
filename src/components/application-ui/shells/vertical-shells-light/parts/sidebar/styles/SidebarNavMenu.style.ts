import { alpha, List, ListItemButton, ListSubheader, styled, type ListProps } from '@mui/material';

/**
 * Styled ListSubheader for the sidebar navigation menu
 */
export const ListSubheaderWrapper = styled(ListSubheader)<ListProps<'div', { component: 'div' }>>(
  ({ theme }) => ({
    background: theme.palette.background.default,
    textTransform: 'uppercase',
    fontWeight: 600,
    fontSize: 13,
    color: theme.palette.mode === 'dark' ? theme.palette.neutral[300] : theme.palette.neutral[800],
    lineHeight: theme.spacing(5),
    padding: theme.spacing(0, 2),
  })
);

/**
 * Styled ListItemButton for the sidebar navigation menu
 */
export const ListItemButtonWrapper = styled(ListItemButton)(({ theme }) => ({
  color: theme.palette.mode === 'dark' ? theme.palette.neutral[700] : theme.palette.neutral[500],
  borderRadius: (theme.shape.borderRadius as number) * 4,
  transition: 'none',
  fontWeight: 600,
  fontSize: 14,
  marginBottom: '3px',
  padding: theme.spacing(0.8, 1, 0.8, 2),

  '& .MuiListItemIcon-root': {
    color: theme.palette.mode === 'dark' ? theme.palette.neutral[700] : theme.palette.neutral[800],
    minWidth: 38,
  },

  '& .MuiListItemText-root': {
    color: theme.palette.mode === 'dark' ? theme.palette.neutral[500] : theme.palette.neutral[800],
  },

  '&:hover': {
    color: theme.palette.mode === 'dark' ? theme.palette.neutral[500] : theme.palette.primary.dark,
    background:
      theme.palette.mode === 'dark'
        ? alpha(theme.palette.common.white, 0.04)
        : alpha(theme.palette.common.white, 0.8),

    '& .MuiListItemIcon-root': {
      color:
        theme.palette.mode === 'dark' ? theme.palette.neutral[300] : theme.palette.primary.main,
    },

    '& .MuiListItemText-root': {
      color: theme.palette.mode === 'dark' ? theme.palette.neutral[50] : theme.palette.primary.main,
    },
  },

  '&.Mui-selected, &.Mui-selected:hover': {
    color: theme.palette.common.white,
    background: theme.palette.primary.main,

    '& .MuiListItemIcon-root': {
      color: alpha(theme.palette.common.white, 0.95),
    },

    '& .MuiListItemText-root': {
      color: alpha(theme.palette.common.white, 0.95),
    },
  },
}));

/**
 * Styled SubMenu for the sidebar navigation menu
 */
export const SubMenu = styled(List)<ListProps<'div', { component: 'div' }>>(({ theme }) => ({
  '& .MuiListItemButton-root': {
    padding: theme.spacing(0.8, 1, 0.8, 2),
    fontWeight: 500,

    '&::before': {
      background: theme.palette.primary.main,
      opacity: 0,
      position: 'absolute',
      borderRadius: 4,
      top: '50%',
      height: '6px',
      width: '6px',
      transform: 'scale(0)',
      marginTop: '-3px',
      transition: theme.transitions.create(['transform', 'opacity']),
    },

    '&.Mui-selected, &.Mui-selected:hover': {
      color: theme.palette.primary.dark,
      background: alpha(theme.palette.common.white, 0.8),

      '& .MuiListItemIcon-root': {
        color: theme.palette.primary.main,
      },

      '& .MuiListItemText-root': {
        color: theme.palette.primary.main,
      },
    },

    '&.Mui-selected, &:hover': {
      '&::before': {
        opacity: 1,
        transform: 'scale(1)',
      },
    },

    '& .MuiListItemText-root': {
      margin: 0,
    },
  },
}));

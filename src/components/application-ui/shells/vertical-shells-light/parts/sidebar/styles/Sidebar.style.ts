import { neutral } from '@/theme/colors';
import { alpha, Box, styled, type Theme } from '@mui/material';

/**
 * Styled wrapper for the sidebar content
 */
export const SidebarWrapper = styled(Box)({
  height: '100vh',
  color: neutral[700],
  display: 'flex',
  flexDirection: 'column',
});

/**
 * Get the box shadow for the sidebar based on its state
 */
export const getBoxShadow = (
  theme: Theme,
  isSidebarCollapsed: boolean,
  isSidebarHovered: boolean
) => {
  if (isSidebarCollapsed) {
    return isSidebarHovered ? theme.shadows[21] : theme.shadows[7];
  }
  return theme.shadows[7];
};

/**
 * Get the styles for the persistent drawer paper
 */
export const getPersistentDrawerPaperStyles = (
  theme: Theme,
  sidebarWidth: number,
  isSidebarCollapsed: boolean,
  getBoxShadow: (theme: Theme) => string
) => ({
  overflow: 'hidden',
  border: 0,
  background: theme.palette.background.default,
  width: sidebarWidth,
  boxShadow: getBoxShadow,
  position: isSidebarCollapsed ? 'fixed' : 'sticky',
  height: '100vh',
  transition: theme.transitions.create(['width', 'box-shadow']),
});

/**
 * Get the styles for the temporary drawer paper
 */
export const getTemporaryDrawerPaperStyles = (theme: Theme) => ({
  background: theme.palette.background.default,
  overflow: 'hidden',
  boxShadow: theme.shadows[24],
});

/**
 * Get the styles for the logo container
 */
export const getLogoContainerStyles = (theme: Theme, headerHeight: number) => ({
  height: headerHeight * 1.53,
  borderBottom: `2px solid ${alpha(theme.palette.neutral[700], 0.08)}`,
});

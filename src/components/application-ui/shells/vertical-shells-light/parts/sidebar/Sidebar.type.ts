import type { Merchant } from '@/components/application-ui/app-sidebar/merchant-switcher/MerchantSwitcher.type';
import type { MenuItem } from '@/router/menu-items';

/**
 * Props for the Sidebar component
 */
export interface SidebarProps {
  /**
   * Callback fired when the drawer is closed
   */
  onClose?: () => void;

  /**
   * Callback fired when the drawer is opened
   */
  onOpen?: () => void;

  /**
   * Whether the drawer is open
   */
  open?: boolean;

  /**
   * Menu items to be displayed in the sidebar
   */
  menuItems?: MenuItem[];
}

/**
 * Props for the SidebarNavMenu component
 */
export interface SidebarNavMenuProps {
  /**
   * Menu items to be displayed in the sidebar
   */
  menuItems?: MenuItem[];
}

/**
 * Props for the NavItem component
 */
export interface NavItemProps {
  /**
   * Menu item data
   */
  item: MenuItem;
}

/**
 * Props for the SidebarContent component
 */
export interface SidebarContentProps {
  /**
   * Width of the sidebar
   */
  sidebarWidth: number;

  /**
   * Justify content value for the logo container
   */
  justifyContentValue: string;

  /**
   * Whether the sidebar is collapsed
   */
  isSidebarCollapsed: boolean;

  /**
   * Whether the sidebar is hovered
   */
  isSidebarHovered: boolean;

  /**
   * Menu items to be displayed in the sidebar
   */
  menuItems?: MenuItem[];

  /**
   * Current merchant
   */
  currentMerchant: Merchant | null;

  /**
   * List of merchants
   */
  merchants: Merchant[];

  /**
   * Callback fired when a merchant is selected
   */
  onMerchantSwitch: (merchant: Merchant | null) => void;

  /**
   * Whether the viewport is at least medium size
   */
  mdUp: boolean;
}

/**
 * Props for the SidebarFooter component
 */
export interface SidebarFooterProps {
  /**
   * Optional custom version string
   */
  version?: string;
}

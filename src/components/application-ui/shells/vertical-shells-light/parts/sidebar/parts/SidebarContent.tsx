import { Logo } from '@/components/application-ui/app-sidebar/logo/Logo';
import { MerchantSwitcher } from '@/components/application-ui/app-sidebar/merchant-switcher/MerchantSwitcher';
import { Scrollbar } from '@/components/base/data-display/scrollbar';
import { HEADER_HEIGHT } from '@/theme/utils';
import { Box, useTheme } from '@mui/material';
import type { SidebarContentProps } from '../Sidebar.type';
import { getLogoContainerStyles, SidebarWrapper } from '../styles/Sidebar.style';
import { SidebarFooter } from './SidebarFooter';
import { SidebarNavMenu } from './SidebarNavMenu';

/**
 * Content of the sidebar
 */
export const SidebarContent = ({
  sidebarWidth,
  justifyContentValue,
  isSidebarCollapsed,
  isSidebarHovered,
  menuItems,
  currentMerchant,
  merchants,
  onMerchantSwitch,
  mdUp,
}: SidebarContentProps) => {
  const theme = useTheme();

  return (
    <SidebarWrapper
      as="nav"
      sx={{
        width: sidebarWidth,
      }}
    >
      <Box
        px={2}
        display="flex"
        justifyContent={{
          xs: 'flex-start',
          lg: justifyContentValue,
        }}
        alignItems="center"
        sx={getLogoContainerStyles(theme, HEADER_HEIGHT)}
      >
        <Logo isLinkStatic />
      </Box>
      <MerchantSwitcher
        sidebarCollapsed={isSidebarCollapsed}
        isHovered={isSidebarHovered}
        merchants={merchants}
        currentMerchant={currentMerchant}
        onSwitch={onMerchantSwitch}
      />
      <Box
        flex={1}
        overflow="auto"
        position="relative"
        zIndex={6}
      >
        <Scrollbar dark>
          <SidebarNavMenu menuItems={menuItems} />
        </Scrollbar>
      </Box>
      {mdUp && isSidebarCollapsed ? isSidebarHovered && <SidebarFooter /> : <SidebarFooter />}
    </SidebarWrapper>
  );
};

export default SidebarContent;

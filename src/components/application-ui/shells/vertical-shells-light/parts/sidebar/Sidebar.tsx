import { useSidebar } from '@/hooks/ui/use-sidebar.hook';
import { SIDEBAR_WIDTH, SIDEBAR_WIDTH_COLLAPSED } from '@/theme/utils';
import { alpha, Drawer, SwipeableDrawer, useMediaQuery, useTheme, type Theme } from '@mui/material';
import { useState } from 'react';
import type { Merchant } from '../../../../app-sidebar/merchant-switcher/MerchantSwitcher.type';
import { SidebarContent } from './parts';
import type { SidebarProps } from './Sidebar.type';
import {
  getBoxShadow,
  getPersistentDrawerPaperStyles,
  getTemporaryDrawerPaperStyles,
} from './styles/Sidebar.style';

/**
 * Sidebar component for the vertical shell layout
 */
export const Sidebar = ({ onClose, onOpen, menuItems, open, ...otherProps }: SidebarProps) => {
  const lgUp = useMediaQuery((theme: Theme) => theme.breakpoints.up('lg'));
  const mdUp = useMediaQuery((theme: Theme) => theme.breakpoints.up('md'));
  const theme = useTheme();

  // Sample merchants data
  const sampleMerchants = [
    {
      id: 1,
      name: 'IBC22',
      logo: '/avatars/4.png',
      prefix: 'I22',
    },
    {
      id: 2,
      name: 'KING333',
      logo: '/avatars/3.png',
      prefix: 'K333',
    },
    {
      id: 3,
      name: 'ACE66',
      logo: '/avatars/2.png',
      prefix: 'A66',
    },
  ];

  // State for the current merchant
  const [currentMerchant, setCurrentMerchant] = useState<Merchant | null>(null);
  const handleMerchantSwitch = (merchant: Merchant | null) => {
    setCurrentMerchant(merchant);
  };

  // Sidebar context for collapsed state and hover
  const { isSidebarCollapsed, isSidebarHovered, toggleSidebarHover } = useSidebar();
  const handleMouseEnter = () => {
    toggleSidebarHover(true);
  };

  const handleMouseLeave = () => {
    toggleSidebarHover(false);
  };

  // Calculate sidebar width based on state
  const getSidebarWidth = (forMobile = false) => {
    // For mobile view, always use full width
    if (forMobile) {
      return SIDEBAR_WIDTH;
    }

    // For desktop view, respect collapsed state
    if (mdUp && isSidebarCollapsed) {
      return isSidebarHovered ? SIDEBAR_WIDTH : SIDEBAR_WIDTH_COLLAPSED;
    }
    return SIDEBAR_WIDTH;
  };

  // Calculate justify content value for the logo container
  const getJustifyContentValue = () => {
    if (mdUp && isSidebarCollapsed) {
      return isSidebarHovered ? 'space-between' : 'center';
    }
    return 'space-between';
  };

  const sidebarWidth = getSidebarWidth();
  const mobileSidebarWidth = getSidebarWidth(true);
  const justifyContentValue = getJustifyContentValue();

  // Get box shadow function for the drawer
  const boxShadowFn = (theme: Theme) => getBoxShadow(theme, isSidebarCollapsed, isSidebarHovered);

  // The open state is controlled by the mobileNav hook in VerticalShellsLight

  // Render persistent drawer for large screens
  if (lgUp) {
    return (
      <Drawer
        anchor="left"
        open
        ModalProps={{
          keepMounted: true,
        }}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        slotProps={{
          paper: {
            sx: getPersistentDrawerPaperStyles(
              theme,
              sidebarWidth,
              isSidebarCollapsed,
              boxShadowFn
            ),
          },
        }}
        variant="persistent"
        {...otherProps}
      >
        <SidebarContent
          sidebarWidth={sidebarWidth}
          justifyContentValue={justifyContentValue}
          isSidebarCollapsed={isSidebarCollapsed}
          isSidebarHovered={isSidebarHovered}
          menuItems={menuItems}
          currentMerchant={currentMerchant}
          merchants={sampleMerchants}
          onMerchantSwitch={handleMerchantSwitch}
          mdUp={mdUp}
        />
      </Drawer>
    );
  }

  // Render swipeable drawer for small screens
  // Using SwipeableDrawer for better mobile UX with swipe gestures
  return (
    <SwipeableDrawer
      anchor="left"
      onClose={onClose || (() => {})}
      onOpen={onOpen || (() => {})}
      open={open}
      ModalProps={{
        keepMounted: true,
        BackdropProps: {
          sx: {
            backdropFilter: 'blur(3px)',
            background: `linear-gradient(90deg, ${alpha(
              theme.palette.background.paper,
              0.7
            )} 10%, ${alpha(theme.palette.neutral[900], 0.6)} 100%)`,
          },
        },
      }}
      slotProps={{
        paper: {
          sx: {
            ...getTemporaryDrawerPaperStyles(theme),
            width: mobileSidebarWidth,
            height: '100%',
            maxWidth: '80%', // Limit width on very small screens
            boxShadow: theme.shadows[24],
          },
        },
      }}
      swipeAreaWidth={20}
      variant="temporary"
      {...otherProps}
    >
      <SidebarContent
        sidebarWidth={mobileSidebarWidth}
        justifyContentValue={justifyContentValue}
        isSidebarCollapsed={false} // Always expanded in mobile view
        isSidebarHovered={false} // Not applicable in mobile view
        menuItems={menuItems}
        currentMerchant={currentMerchant}
        merchants={sampleMerchants}
        onMerchantSwitch={handleMerchantSwitch}
        mdUp={mdUp}
      />
    </SwipeableDrawer>
  );
};

export default Sidebar;

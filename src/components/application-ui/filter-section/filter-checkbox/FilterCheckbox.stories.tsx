import i18n from '@/providers/i18n/i18n';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { useState } from 'react';
import { expect, userEvent, within } from 'storybook/internal/test';
import FilterCheckbox from './FilterCheckbox';

const meta: Meta<typeof FilterCheckbox> = {
  title: 'Components/application-ui/filter-section/FilterCheckbox',
  component: FilterCheckbox,
  parameters: {
    layout: 'centered',
  },
  args: {
    label: 'Checkbox',
    options: [
      { value: 'active', labelKey: 'active', testId: 'filter-checkbox-option-active' },
      { value: 'inactive', labelKey: 'inactive', testId: 'filter-checkbox-option-inactive' },
    ],
    accordionTestId: 'filter-checkbox-accordion',
    inputTestId: 'filter-checkbox-input',
  },
  decorators: [
    (Story, context) => {
      const locale = context.globals.locale || 'en';
      i18n.changeLanguage(locale);

      const [values, setValue] = useState<string[]>([]);

      return (
        <Story
          {...context}
          args={{
            ...context.args,
            onChange: (value) => {
              if (values.includes(value)) {
                setValue(values.filter((v) => v !== value));
              } else {
                setValue([...values, value]);
              }
            },
            selectedValues: values,
          }}
        />
      );
    },
  ],
};

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  play: async ({ canvasElement, step, args }) => {
    const canvas = within(canvasElement);
    await step('Should render checkboxes and label', async () => {
      expect(canvas.getByTestId('filter-checkbox-accordion')).toBeInTheDocument();
      expect(canvas.getByTestId('filter-checkbox-input')).toBeInTheDocument();
      expect(canvas.getByTestId('filter-checkbox-option-active')).toBeInTheDocument();
      expect(canvas.getByTestId('filter-checkbox-option-inactive')).toBeInTheDocument();
    });

    await step('Should be able check and uncheck all options', async () => {
      for (const option of args.options) {
        if (!option.testId) {
          return;
        }
        const el = canvas.getByTestId(option.testId);

        const checkbox = el.querySelector('input');
        if (!checkbox) {
          throw new Error(`Checkbox for ${option.labelKey} not found`);
        }
        await userEvent.click(checkbox);
        expect(checkbox).toBeChecked();
        await userEvent.click(checkbox);
        expect(checkbox).not.toBeChecked();
      }
    });
  },
};

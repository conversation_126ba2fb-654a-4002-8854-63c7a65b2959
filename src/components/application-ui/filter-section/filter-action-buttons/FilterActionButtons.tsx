import { Button } from '@/components/base/inputs/button';
import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { type FilterActionButtonsProps } from './FilterActionButtons.type';

/**
 * FilterActionButtons component displays the search and clear buttons for filter panels
 *
 * @example
 * ```tsx
 * <FilterActionButtons
 *   onSearch={handleApplyFilters}
 *   onClear={handleResetFilters}
 * />
 * ```
 */
export const FilterActionButtons = ({
  onSearch,
  onClear,
  searchButtonText,
  clearButtonText,
  searchButtonColor = 'primary',
  showClearButton = true,
  clearButtonTestId,
  searchButtonTestId,
}: FilterActionButtonsProps) => {
  const { t } = useTranslation();

  return (
    <Box
      sx={{
        p: 2,
      }}
    >
      <Button
        color={searchButtonColor}
        customStyle="soft"
        variant="outlined"
        fullWidth
        onClick={onSearch}
        data-testid={searchButtonTestId}
      >
        {searchButtonText ?? t('common.label.search')}
      </Button>

      {showClearButton && (
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'flex-end',
            mt: 1,
          }}
        >
          <Button
            color="inherit"
            variant="text"
            size="small"
            onClick={onClear}
            data-testid={clearButtonTestId}
          >
            {clearButtonText ?? t('common.action.clearAll')}
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default FilterActionButtons;

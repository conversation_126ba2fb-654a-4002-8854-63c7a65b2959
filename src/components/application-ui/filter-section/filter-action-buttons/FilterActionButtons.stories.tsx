import i18n from '@/providers/i18n/i18n';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { expect, fn, userEvent, within } from 'storybook/internal/test';
import FilterActionButtons from './FilterActionButtons';

const mockOnSearch = fn();

const mockOnClear = fn();

const meta: Meta<typeof FilterActionButtons> = {
  title: 'Components/application-ui/filter-section/FilterActionButtons',
  component: FilterActionButtons,
  parameters: {
    layout: 'centered',
  },
  args: {
    onSearch: mockOnSearch,
    onClear: mockOnClear,
    clearButtonTestId: 'filter-clear-button',
    searchButtonTestId: 'filter-search-button',
  },
  decorators: [
    (Story, context) => {
      const locale = context.globals.locale || 'en';
      i18n.changeLanguage(locale);

      return <Story {...context} />;
    },
  ],
};

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);
    await step('Should render action buttons', async () => {
      expect(canvas.getByTestId('filter-clear-button')).toBeInTheDocument();
      expect(canvas.getByTestId('filter-search-button')).toBeInTheDocument();
    });

    await step('Should fire onSearch on search button clicked', async () => {
      await userEvent.click(canvas.getByTestId('filter-search-button'));
      expect(mockOnSearch).toBeCalledTimes(1);
    });
    await step('Should fire onClear on clear button clicked', async () => {
      await userEvent.click(canvas.getByTestId('filter-clear-button'));
      expect(mockOnClear).toBeCalledTimes(1);
    });
  },
};

import { Accordion } from '@/components/base/surface/accordion';
import { Typography } from '@/components/base/typography';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {
  AccordionDetails,
  AccordionSummary,
  Box,
  FormControl,
  MenuItem,
  Select,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import type { FilterSelectProps } from './FilterSelect.type';

/**
 * FilterSelect component displays a collapsible section with a select dropdown for filtering
 *
 * @example
 * ```tsx
 * <FilterSelect
 *   label="status"
 *   menuItems={[
 *     { value: 'active', labelKey: 'active' },
 *     { value: 'inactive', labelKey: 'inactive', caseTransform: 'sentenceCase' }
 *   ]}
 *   value={selectValue}
 *   onChange={handleSelectChange}
 * />
 * ```
 */
export function FilterSelect({
  label,
  menuItems,
  defaultExpanded = true,
  accordionTestId,
  inputTestId,
  ...props
}: FilterSelectProps) {
  const { t } = useTranslation();

  return (
    <Accordion
      defaultExpanded={defaultExpanded}
      customStyle="minimal"
      data-testid={accordionTestId}
    >
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography
          variant="h5"
          caseTransform="sentenceCase"
        >
          {t(label)}
        </Typography>
      </AccordionSummary>
      <AccordionDetails>
        <Box
          px={{ xs: 2, sm: 3 }}
          pb={2}
        >
          <FormControl fullWidth>
            <Select
              {...props}
              data-testId={inputTestId}
            >
              {menuItems.map((menuItem) => (
                <MenuItem
                  key={menuItem.value}
                  value={menuItem.value}
                  data-testid={menuItem.testId}
                >
                  {menuItem.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      </AccordionDetails>
    </Accordion>
  );
}

export default FilterSelect;

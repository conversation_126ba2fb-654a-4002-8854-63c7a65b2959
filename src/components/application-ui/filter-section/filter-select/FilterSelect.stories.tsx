import i18n from '@/providers/i18n/i18n';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { expect, userEvent, within } from 'storybook/internal/test';
import FilterSelect from './FilterSelect';

const meta: Meta<typeof FilterSelect> = {
  title: 'Components/application-ui/filter-section/FilterSelect',
  component: FilterSelect,
  parameters: {
    layout: 'centered',
  },
  args: {
    label: 'Select',
    menuItems: [
      { value: 'active', label: 'active', testId: 'filter-select-option-active' },
      { value: 'inactive', label: 'inactive', testId: 'filter-select-option-inactive' },
    ],
    accordionTestId: 'filter-select-accordion',
    inputTestId: 'filter-select-input',
  },

  decorators: [
    (Story, context) => {
      const locale = context.globals.locale || 'en';
      i18n.changeLanguage(locale);

      return <Story {...context} />;
    },
  ],
};

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);
    await step('Should render checkboxes and label', async () => {
      expect(canvas.getByTestId('filter-select-accordion')).toBeInTheDocument();
      expect(canvas.getByTestId('filter-select-input')).toBeInTheDocument();
    });

    await step('Should be able check and uncheck all options', async () => {
      const el = canvas.getByTestId('filter-select-input');
      const selectTrigger = el.querySelector('[role=combobox]');
      if (!selectTrigger) {
        throw new Error('Select Trigger not found');
      }
      await userEvent.click(selectTrigger);
    });
  },
};

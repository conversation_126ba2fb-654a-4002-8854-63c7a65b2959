import type { OutlinedInputProps } from '@mui/material';

/**
 * Props for the FilterInput component
 */
export interface FilterInputProps extends OutlinedInputProps {
  /**
   * The label of the filter section
   */
  label: string;

  /**
   * Whether the accordion should be expanded by default
   * @default true
   */
  defaultExpanded?: boolean;

  /**
   * Test ID for the accordion
   */
  accordionTestId?: string;

  /**
   * Test ID for the text input
   */
  inputTestId?: string;
}

import { Accordion } from '@/components/base/surface/accordion';
import { Typography } from '@/components/base/typography';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { AccordionDetails, AccordionSummary, Box, OutlinedInput } from '@mui/material';
import { useTranslation } from 'react-i18next';
import type { FilterInputProps } from './FilterInput.type';

/**
 * FilterInput component displays a collapsible section with a single input field for filtering
 *
 * @example
 * ```tsx
 * <FilterInput
 *   label="myField"
 *   value={value}
 *   onChange={handleValueChange}
 * />
 * ```
 */
export const FilterInput = ({
  label,
  defaultExpanded = true,
  inputTestId,
  accordionTestId,
  ...props
}: FilterInputProps) => {
  const { t } = useTranslation();

  return (
    <Accordion
      defaultExpanded={defaultExpanded}
      customStyle="minimal"
      data-testid={accordionTestId}
    >
      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
        <Typography
          variant="h5"
          caseTransform="sentenceCase"
        >
          {t(label)}
        </Typography>
      </AccordionSummary>
      <AccordionDetails>
        <Box
          px={{ xs: 2, sm: 3 }}
          pb={2}
        >
          <OutlinedInput
            id={`${label}-input`}
            aria-label={t(label)}
            data-testid={inputTestId}
            {...props}
          />
        </Box>
      </AccordionDetails>
    </Accordion>
  );
};

export default FilterInput;

import i18n from '@/providers/i18n/i18n';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { expect, userEvent, within } from 'storybook/internal/test';
import FilterInput from './FilterInput';

const meta: Meta<typeof FilterInput> = {
  title: 'Components/application-ui/filter-section/FilterInput',
  component: FilterInput,
  parameters: {
    layout: 'centered',
  },
  args: {
    label: 'Search',
    accordionTestId: 'filter-input-accordion',
    inputTestId: 'filter-input-input',
  },
  decorators: [
    (Story, context) => {
      const locale = context.globals.locale || 'en';
      i18n.changeLanguage(locale);
      return <Story {...context} />;
    },
  ],
};

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);
    await step('Should render input field and label', async () => {
      expect(canvas.getByTestId('filter-input-input')).toBeInTheDocument();
      expect(canvas.getByTestId('filter-input-accordion')).toBeInTheDocument();
    });

    await step('Should be able type in input field', async () => {
      const inputWrapper = canvas.getByTestId('filter-input-input');

      const input = inputWrapper.querySelector('input');
      expect(input).not.toBeNull();

      if (!input) {
        throw new Error('Input field not found');
      }
      await userEvent.type(input, 'test');
      expect(input).toHaveValue('test');
    });
  },
};

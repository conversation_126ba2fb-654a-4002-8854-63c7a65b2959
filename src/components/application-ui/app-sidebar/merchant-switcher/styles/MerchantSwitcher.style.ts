import { alpha, type Theme } from '@mui/material';

/**
 * Get the styles for the merchant button
 */
export const getMerchantButtonStyles = (theme: Theme) => ({
  background: alpha(theme.palette.common.white, 0.03),
  color: theme.palette.mode === 'dark' ? theme.palette.neutral[400] : theme.palette.neutral[600],
  justifyContent: 'flex-start',
  minWidth: 40,
  px: 1.2,
  borderWidth: 1,
  borderStyle: 'solid',
  borderColor:
    theme.palette.mode === 'dark'
      ? theme.palette.neutral[600]
      : alpha(theme.palette.common.black, 0.08),

  '.MuiTypography-subtitle2': {
    color: theme.palette.mode === 'dark' ? theme.palette.neutral[300] : theme.palette.common.black,
  },

  '.MuiTypography-body1': {
    color:
      theme.palette.mode === 'dark'
        ? theme.palette.neutral[500]
        : alpha(theme.palette.common.black, 0.5),
  },

  '&:hover': {
    color:
      theme.palette.mode === 'dark'
        ? alpha(theme.palette.primary.dark, 0.8)
        : theme.palette.primary.dark,
    background:
      theme.palette.mode === 'dark'
        ? alpha(theme.palette.common.white, 0.04)
        : alpha(theme.palette.common.white, 0.8),
    borderColor:
      theme.palette.mode === 'dark' ? 'transparent' : alpha(theme.palette.neutral[600], 0.3),

    '.MuiTypography-subtitle2': {
      color: theme.palette.primary.dark,
    },

    '.MuiTypography-body1': {
      color: alpha(theme.palette.primary.dark, 0.8),
    },
  },
});

/**
 * Get the styles for the merchant menu paper
 */
export const getMerchantMenuPaperStyles = () => ({
  width: 380,
  '.MuiList-root': {
    p: 0,
  },
});

/**
 * Get the styles for the merchant menu item
 */
export const getMerchantMenuItemStyles = (theme: Theme) => ({
  pl: 1,
  borderRadius: theme.shape.borderRadius + 'px',
});

import type { Theme } from '@mui/material';

/**
 * Merchant data structure
 */
export interface Merchant {
  /**
   * Unique identifier for the merchant
   */
  id: number;

  /**
   * Display name of the merchant
   */
  name: string;

  /**
   * URL to the merchant's logo
   */
  logo: string;

  /**
   * Short prefix code for the merchant
   */
  prefix: string;
}

/**
 * Props for the MerchantSwitcher component
 */
export interface MerchantSwitcherProps {
  /**
   * List of merchants
   */
  merchants: Merchant[];

  /**
   * Whether the sidebar is hovered
   */
  isHovered: boolean;

  /**
   * Whether the sidebar is collapsed
   */
  sidebarCollapsed: boolean;

  /**
   * Currently selected merchant
   */
  currentMerchant: Merchant | null;

  /**
   * Callback fired when a merchant is selected
   */
  onSwitch: (merchant: Merchant | null) => void;
}

/**
 * Props for the MerchantDescription component
 */
export interface MerchantDescriptionProps {
  /**
   * Merchant data
   */
  merchant: Merchant;
}

/**
 * Props for the MerchantButton component
 */
export interface MerchantButtonProps {
  /**
   * Merchant data
   */
  merchant: Merchant | null;

  /**
   * Whether the sidebar is hovered
   */
  isHovered: boolean;

  /**
   * Whether the sidebar is collapsed
   */
  sidebarCollapsed: boolean;

  /**
   * Whether the viewport is at least medium size
   */
  mdUp: boolean;

  /**
   * Callback fired when the button is clicked
   */
  onClick: (event: React.MouseEvent<HTMLElement>) => void;
}

/**
 * Props for the MerchantButtonEndIcon component
 */
export interface MerchantButtonEndIconProps {
  /**
   * Whether the viewport is at least medium size
   */
  mdUp: boolean;

  /**
   * Whether the sidebar is collapsed
   */
  sidebarCollapsed: boolean;

  /**
   * Whether the sidebar is hovered
   */
  isHovered: boolean;
}

/**
 * Props for the MerchantButtonContent component
 */
export interface MerchantButtonContentProps {
  /**
   * Merchant data
   */
  merchant: Merchant;

  /**
   * Theme object
   */
  theme: Theme;

  /**
   * Whether the viewport is at least medium size
   */
  mdUp: boolean;

  /**
   * Whether the sidebar is collapsed
   */
  sidebarCollapsed: boolean;

  /**
   * Whether the sidebar is hovered
   */
  isHovered: boolean;
}

/**
 * Props for the MerchantMenu component
 */
export interface MerchantMenuProps {
  /**
   * List of merchants
   */
  merchants: Merchant[];

  /**
   * Currently selected merchant
   */
  currentMerchant: Merchant | null;

  /**
   * Anchor element for the menu
   */
  anchorEl: HTMLElement | null;

  /**
   * Whether the menu is open
   */
  open: boolean;

  /**
   * Callback fired when the menu is closed
   */
  onClose: () => void;

  /**
   * Callback fired when a merchant is selected
   */
  onSelect: (merchant: Merchant) => void;
}

import { stringAvatar } from '@/components/base/data-display/avatar';
import { Button } from '@/components/base/inputs/button';
import { Typography } from '@/components/base/typography';
import useAnchor from '@/hooks/ui/use-anchor.hook';
import CheckTwoToneIcon from '@mui/icons-material/CheckTwoTone';
import KeyboardArrowRightTwoToneIcon from '@mui/icons-material/KeyboardArrowRightTwoTone';
import {
  Avatar,
  Box,
  Divider,
  ListItemIcon,
  ListItemText,
  Menu,
  MenuItem,
  Stack,
  useMediaQuery,
  useTheme,
  type Theme,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import type {
  Merchant,
  MerchantButtonContentProps,
  MerchantButtonEndIconProps,
  MerchantButtonProps,
  MerchantDescriptionProps,
  MerchantMenuProps,
  MerchantSwitcherProps,
} from './MerchantSwitcher.type';
import {
  getMerchantButtonStyles,
  getMerchantMenuItemStyles,
  getMerchantMenuPaperStyles,
} from './styles/MerchantSwitcher.style';

/**
 * Displays merchant name and prefix
 */
const MerchantDescription = ({ merchant }: MerchantDescriptionProps) => (
  <Box
    ml={1}
    overflow="hidden"
    textAlign="left"
    flex={1}
  >
    <Typography
      variant="subtitle2"
      fontWeight={600}
      lineHeight={1.2}
      sx={{
        pb: 0.2,
      }}
    >
      {merchant.name}
    </Typography>
    <Typography
      variant="body1"
      noWrap
      lineHeight={1.2}
    >
      {merchant.prefix}
    </Typography>
  </Box>
);

/**
 * End icon for the merchant button
 */
const MerchantButtonEndIcon = ({
  mdUp,
  sidebarCollapsed,
  isHovered,
}: MerchantButtonEndIconProps) =>
  mdUp && sidebarCollapsed ? (
    isHovered && <KeyboardArrowRightTwoToneIcon />
  ) : (
    <KeyboardArrowRightTwoToneIcon />
  );

/**
 * Content of the merchant button
 */
const MerchantButtonContent = ({
  merchant,
  theme,
  mdUp,
  sidebarCollapsed,
  isHovered,
}: MerchantButtonContentProps) => (
  <>
    <Avatar
      variant="rounded"
      src={merchant.logo}
      alt={merchant.name}
      {...stringAvatar(merchant.name, theme)}
    />
    {mdUp && sidebarCollapsed ? (
      isHovered && <MerchantDescription merchant={merchant} />
    ) : (
      <MerchantDescription merchant={merchant} />
    )}
  </>
);

/**
 * Button to select a merchant
 */
const MerchantButton = ({
  merchant,
  isHovered,
  sidebarCollapsed,
  mdUp,
  onClick,
}: MerchantButtonProps) => {
  const theme = useTheme();
  const { t } = useTranslation();

  return (
    <Button
      color="primary"
      fullWidth
      endIcon={
        <MerchantButtonEndIcon
          mdUp={mdUp}
          sidebarCollapsed={sidebarCollapsed}
          isHovered={isHovered}
        />
      }
      onClick={onClick}
      sx={getMerchantButtonStyles(theme)}
    >
      {merchant ? (
        <MerchantButtonContent
          merchant={merchant}
          theme={theme}
          mdUp={mdUp}
          sidebarCollapsed={sidebarCollapsed}
          isHovered={isHovered}
        />
      ) : (
        <Box sx={{ display: 'flex', flex: 1, justifyContent: 'flex-start', py: 1 }}>
          <Typography
            variant="subtitle2"
            fontWeight={500}
          >
            {t('common.action.switchMerchant')}
          </Typography>
        </Box>
      )}
    </Button>
  );
};

/**
 * Menu to select a merchant
 */
const MerchantMenu = ({
  merchants,
  currentMerchant,
  anchorEl,
  open,
  onClose,
  onSelect,
}: MerchantMenuProps) => {
  const theme = useTheme();
  const { t } = useTranslation();

  return (
    <Menu
      anchorEl={anchorEl}
      open={open}
      onClose={onClose}
      anchorOrigin={{
        vertical: 'top',
        horizontal: 'center',
      }}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'center',
      }}
      slotProps={{
        paper: {
          sx: getMerchantMenuPaperStyles(),
        },
      }}
    >
      <Box
        p={2}
        sx={{
          backgroundColor: 'background.default',
        }}
      >
        <Typography
          variant="h6"
          caseTransform="sentenceCase"
        >
          {t('common.action.selectMerchant')}
        </Typography>
      </Box>
      <Stack
        p={1}
        divider={<Divider />}
      >
        {merchants.map((merchant) => (
          <MenuItem
            selected={currentMerchant?.id === merchant.id}
            sx={getMerchantMenuItemStyles(theme)}
            key={merchant.id}
            onClick={() => onSelect(merchant)}
          >
            <ListItemIcon
              sx={{
                mr: 1,
              }}
            >
              <Avatar
                variant="rounded"
                src={merchant.logo}
                alt={merchant.name}
                {...stringAvatar(merchant.name, theme)}
              />
            </ListItemIcon>

            <ListItemText
              primary={merchant.name}
              secondary={merchant.prefix}
              slotProps={{
                primary: {
                  sx: {
                    fontWeight: 600,
                  },
                },
                secondary: {
                  noWrap: true,
                },
              }}
            />
            {currentMerchant?.id === merchant.id && (
              <Box
                display="flex"
                alignItems="center"
                justifyContent="flex-end"
                minWidth={38}
              >
                <CheckTwoToneIcon color="primary" />
              </Box>
            )}
          </MenuItem>
        ))}
      </Stack>
    </Menu>
  );
};

/**
 * Component to switch between merchants
 */
export const MerchantSwitcher = ({
  merchants,
  sidebarCollapsed,
  isHovered,
  currentMerchant,
  onSwitch,
}: MerchantSwitcherProps) => {
  const { anchorEl, open, handleClick, handleClose } = useAnchor();
  const mdUp = useMediaQuery((theme: Theme) => theme.breakpoints.up('md'));

  const handleMerchantSelect = (merchant: Merchant) => {
    onSwitch(merchant);
    handleClose();
  };

  return (
    <Box
      px={2}
      py={1.5}
    >
      <MerchantButton
        merchant={currentMerchant}
        isHovered={isHovered}
        sidebarCollapsed={sidebarCollapsed}
        mdUp={mdUp}
        onClick={handleClick}
      />
      <MerchantMenu
        merchants={merchants}
        currentMerchant={currentMerchant}
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        onSelect={handleMerchantSelect}
      />
    </Box>
  );
};

export default MerchantSwitcher;

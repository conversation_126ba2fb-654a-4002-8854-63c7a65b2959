import { AvatarGradient } from '@/components/base/data-display/avatar';
import { RouterLink } from '@/components/base/navigation/router-link';
import { Typography } from '@/components/base/typography';
import { Box, Link, useTheme } from '@mui/material';
import { type LogoProps } from './Logo.type';

/**
 * Renders the logo component with optional styling for dark themes and static links.
 *
 * @example
 * ```tsx
 * <Logo dark={false} isLinkStatic={false} />
 * ```
 */
export const Logo = ({ dark = false, isLinkStatic = false }: LogoProps) => {
  const theme = useTheme();

  // Determine text color based on dark prop and theme mode
  const textColor =
    dark || theme.palette.mode === 'dark' ? theme.palette.common.white : theme.palette.common.black;

  // Configure link props based on isLinkStatic
  const linkProps = isLinkStatic
    ? {
        href: '',
        onClick: (e: { preventDefault: () => any }) => e.preventDefault(),
      }
    : { href: '/' };

  return (
    <Box
      sx={{
        position: 'relative',
        display: 'flex',
        alignItems: 'center',
      }}
    >
      <Link
        component={RouterLink}
        {...linkProps}
        sx={{
          color: textColor,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          textDecoration: 'none',
        }}
      >
        <AvatarGradient
          size={40}
          color="primary"
        >
          <Typography
            component="span"
            sx={{
              fontSize: '18px',
              lineHeight: '18px',
            }}
            fontWeight={600}
            textTransform={'uppercase'}
          >
            QP
          </Typography>
        </AvatarGradient>
        <Typography
          component="span"
          sx={{
            fontSize: '18px',
            letterSpacing: '-.45px',
            ml: 1.5,
            lineHeight: '18px',
          }}
          fontWeight={500}
        >
          Quantum Play
        </Typography>
      </Link>
    </Box>
  );
};

export default Logo;

import { useCustomization } from '@/hooks/ui/use-customization.hook';
import { useSidebarDrawer } from '@/hooks/ui/use-sidebar-drawer.hook';
import { Box, useMediaQuery, useTheme, type Theme } from '@mui/material';
import { useRef } from 'react';
import { type FilterableSidebarLayoutProps } from './FilterableSidebarLayout.type';
import { MainContentArea, PageHeadingWithSidebarToggle, SearchSidebarDrawer } from './parts';
import {
  containerStyles,
  contentWrapperStyles,
  createTransitionStyles,
} from './styles/FilterableSidebarLayout.style';

/**
 * FilterableSidebarLayout is a layout component that renders a page with a filterable sidebar.
 *
 * It includes:
 * - A page heading with a sidebar toggle button
 * - A collapsible sidebar drawer for filters
 * - A main content area that adjusts based on sidebar state
 *
 * This layout is designed for data listing pages that need filtering capabilities,
 * such as user lists, product catalogs, or any other data that can be filtered.
 *
 * @example
 * ```tsx
 * <FilterableSidebarLayout
 *   pageHeading={{ title: 'User', description: 'Manage user accounts' }}
 *   sidebarContent={<UserFilters onFilterChange={handleFilterChange} />}
 * >
 *   <UserDataGrid />
 * </FilterableSidebarLayout>
 * ```
 */
export const FilterableSidebarLayout = ({
  pageHeading,
  sidebarContent,
  children,
}: FilterableSidebarLayoutProps) => {
  const customization = useCustomization();
  const parentRef = useRef<HTMLDivElement | null>(null);
  const lgUp = useMediaQuery((theme: Theme) => theme.breakpoints.up('lg'));
  const { handleClose, handleOpen, handleToggle, open } = useSidebarDrawer();
  const theme = useTheme();

  // Create transition styles for smooth sidebar animation
  const transitionStyles = createTransitionStyles(open, theme, lgUp);

  return (
    <Box
      ref={parentRef}
      sx={containerStyles}
    >
      <PageHeadingWithSidebarToggle
        pageHeading={pageHeading}
        onToggle={handleToggle}
        open={open}
      />
      <Box sx={contentWrapperStyles}>
        <SearchSidebarDrawer
          parentContainer={parentRef.current}
          onClose={handleClose}
          onOpen={handleOpen}
          open={open}
          sidebarContent={sidebarContent}
        />
        <MainContentArea
          customization={customization}
          transitionStyles={transitionStyles}
        >
          {children}
        </MainContentArea>
      </Box>
    </Box>
  );
};

export default FilterableSidebarLayout;

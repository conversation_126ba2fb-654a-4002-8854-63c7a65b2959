import { PageHeading } from '@/components/base/headings/page-heading';
import { Button } from '@/components/base/inputs/button';
import MenuOpenIcon from '@mui/icons-material/MenuOpen';
import MenuRoundedIcon from '@mui/icons-material/MenuRounded';
import { useTranslation } from 'react-i18next';
import { type PageHeadingWithSidebarToggleProps } from '../FilterableSidebarLayout.type';

/**
 * A component that renders a page heading with a sidebar toggle button.
 *
 * This is used as a part of the FilterableSidebarLayout.
 */
export const PageHeadingWithSidebarToggle = ({
  pageHeading,
  onToggle,
  open,
}: PageHeadingWithSidebarToggleProps) => {
  const { t } = useTranslation();

  const toggleButton = (
    <Button
      onClick={onToggle}
      color="secondary"
      variant="outlined"
      customStyle="icon"
      sx={{
        width: 44,
        height: 44,
      }}
      title={open ? t('common.action.closeSearchFilter') : t('common.action.openSearchFilter')}
    >
      {open ? <MenuOpenIcon /> : <MenuRoundedIcon />}
    </Button>
  );

  return (
    <PageHeading
      {...pageHeading}
      iconBox={toggleButton}
    />
  );
};

export default PageHeadingWithSidebarToggle;

import { Scrollbar } from '@/components/base/data-display/scrollbar';
import { Drawer, SwipeableDrawer, useMediaQuery, useTheme, type Theme } from '@mui/material';
import type { SearchSidebarDrawerProps } from '../FilterableSidebarLayout.type';
import {
  createBackdropStyles,
  persistentDrawerStyles,
  swipeableDrawerStyles,
} from '../styles/SearchSidebarDrawer.style';

/**
 * A drawer component that will render either a persistent drawer on the left
 * side of the screen when the screen size is lg or larger, or a temporary
 * swipeable drawer when the screen size is smaller than lg.
 *
 * The drawer will be rendered with a width of 320px on xs and sm screens,
 * 380px on md screens, and a maximum width of 400px on larger screens.
 *
 * The drawer will have a shadow on the right side when the screen size is lg
 * or larger, and a blurred background with a gradient from white to dark
 * gray when the screen size is smaller than lg.
 *
 * @example
 * ```tsx
 * <SearchSidebarDrawer
 *   open={open}
 *   onClose={handleClose}
 *   onOpen={handleOpen}
 *   sidebarContent={<SidebarContent />}
 * />
 * ```
 */
export const SearchSidebarDrawer = ({
  open,
  onClose,
  onOpen,
  sidebarContent,
  parentContainer,
  ...otherProps
}: SearchSidebarDrawerProps) => {
  const theme = useTheme();
  const lgUp = useMediaQuery((theme: Theme) => theme.breakpoints.up('lg'));

  const content = <Scrollbar>{sidebarContent}</Scrollbar>;

  if (lgUp) {
    return (
      <Drawer
        anchor="left"
        open={open}
        slotProps={{
          paper: {
            sx: persistentDrawerStyles,
          },
          transition: {
            container: parentContainer,
          },
        }}
        variant="persistent"
        {...otherProps}
      >
        {content}
      </Drawer>
    );
  }

  return (
    <SwipeableDrawer
      anchor="left"
      onClose={onClose}
      onOpen={onOpen}
      open={open}
      slotProps={{
        paper: {
          sx: swipeableDrawerStyles,
        },
      }}
      ModalProps={{
        BackdropProps: {
          sx: createBackdropStyles(theme),
        },
      }}
      variant="temporary"
      {...otherProps}
    >
      {content}
    </SwipeableDrawer>
  );
};

export default SearchSidebarDrawer;

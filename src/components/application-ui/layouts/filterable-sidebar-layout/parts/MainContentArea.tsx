import { Box, Container } from '@mui/material';
import { type MainContentAreaProps } from '../FilterableSidebarLayout.type';
import {
  containerStyles,
  contentWrapperStyles,
  innerBoxStyles,
} from '../styles/MainContentArea.style';

/**
 * A component that renders the main content area of the FilterableSidebarLayout.
 *
 * This component handles the transition of the content area when the sidebar is opened or closed.
 */
export const MainContentArea = ({
  customization,
  transitionStyles,
  children,
}: MainContentAreaProps) => (
  <Box sx={{ ...contentWrapperStyles, ...transitionStyles }}>
    <Box sx={innerBoxStyles}>
      <Container
        disableGutters
        maxWidth={customization.stretch ? false : 'xl'}
        sx={containerStyles}
      >
        {children}
      </Container>
    </Box>
  </Box>
);

export default MainContentArea;

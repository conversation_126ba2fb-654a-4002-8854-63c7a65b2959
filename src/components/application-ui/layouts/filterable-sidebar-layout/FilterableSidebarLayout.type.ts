import { type PageHeadingProps } from '@/components/base/headings/page-heading';
import { type useCustomization } from '@/hooks/ui/use-customization.hook';
import { type ReactNode } from 'react';

/**
 * Props for the FilterableSidebarLayout component
 */
export interface FilterableSidebarLayoutProps {
  /**
   * The props for the page heading.
   */
  pageHeading: PageHeadingProps;

  /**
   * The content to be displayed in the sidebar drawer.
   * Typically contains filter controls.
   */
  sidebarContent: ReactNode;

  /**
   * The content to be displayed in the main content area.
   * Typically contains the data listing or main page content.
   */
  children: ReactNode;
}

/**
 * Props for the PageHeadingWithSidebarToggle component
 */
export interface PageHeadingWithSidebarToggleProps {
  /**
   * The props for the page heading.
   */
  pageHeading: PageHeadingProps;

  /**
   * Function to toggle the sidebar open/closed.
   */
  onToggle: () => void;

  /**
   * Whether the sidebar is currently open.
   */
  open: boolean;
}

/**
 * Props for the MainContentArea component
 */
export interface MainContentAreaProps {
  /**
   * Customization settings from useCustomization hook.
   */
  customization: ReturnType<typeof useCustomization>;

  /**
   * Transition styles for the content area.
   */
  transitionStyles: {
    ml: string | number;
    transition: string;
  };

  /**
   * The content to be displayed in the main content area.
   */
  children: ReactNode;
}

/**
 * Props for the SearchSidebarDrawer component
 */
export interface SearchSidebarDrawerProps {
  /**
   * Whether the drawer is open
   */
  open: boolean;

  /**
   * Callback fired when the drawer is closed
   */
  onClose: () => void;

  /**
   * Callback fired when the drawer is opened
   */
  onOpen: () => void;

  /**
   * Content to be rendered inside the drawer
   */
  sidebarContent: ReactNode;

  /**
   * Parent container element that the drawer will be appended to
   */
  parentContainer?: HTMLElement | null;
}

import { alpha, type Theme } from '@mui/material';

/**
 * Styles for the persistent drawer (large screens)
 */
export const persistentDrawerStyles = {
  position: 'relative',
  width: 320,
  zIndex: 5,
  boxShadow: (theme: Theme) => theme.shadows[0],
};

/**
 * Styles for the swipeable drawer (small screens)
 */
export const swipeableDrawerStyles = {
  maxWidth: '100%',
  width: { xs: 320, sm: 380, md: 400 },
  pointerEvents: 'auto',
  position: 'absolute',
  boxShadow: (theme: Theme) => theme.shadows[24],
};

/**
 * Create backdrop styles with theme-aware colors
 *
 * @param theme - The current theme
 * @returns The backdrop styles object
 */
export const createBackdropStyles = (theme: Theme) => ({
  backdropFilter: 'blur(3px) !important',
  background: `linear-gradient(90deg, ${alpha(
    theme.palette.neutral[200],
    0.7
  )} 10%, ${alpha(theme.palette.neutral[900], 0.6)} 100%) !important`,
});

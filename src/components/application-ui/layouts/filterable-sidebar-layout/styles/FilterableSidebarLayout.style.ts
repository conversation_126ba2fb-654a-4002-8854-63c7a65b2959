import { type Theme } from '@mui/material';

/**
 * Calculate margin based on sidebar state and screen size
 *
 * @param open - Whether the sidebar is open
 * @param lgUp - Whether the screen is large or up
 * @returns The margin left value
 */
export const getMarginLeft = (open: boolean, lgUp: boolean): string | number => {
  if (open) {
    return 0;
  }
  return lgUp ? '-320px' : 0;
};

/**
 * Create transition styles for smooth sidebar animation
 *
 * @param open - Whether the sidebar is open
 * @param theme - The current theme
 * @param lgUp - Whether the screen is large or up
 * @returns The transition styles object
 */
export const createTransitionStyles = (open: boolean, theme: Theme, lgUp: boolean) => ({
  ml: getMarginLeft(open, lgUp),
  transition: theme.transitions.create('margin', {
    easing: open ? theme.transitions.easing.easeOut : theme.transitions.easing.sharp,
    duration: open
      ? theme.transitions.duration.enteringScreen
      : theme.transitions.duration.leavingScreen,
  }),
});

/**
 * Container styles for the layout
 */
export const containerStyles = {
  minWidth: '100%',
  display: 'flex',
  flexDirection: 'column',
  height: '100%',
};

/**
 * Content wrapper styles
 */
export const contentWrapperStyles = {
  flex: 1,
  position: 'relative',
  display: 'flex',
  minHeight: 0,
};

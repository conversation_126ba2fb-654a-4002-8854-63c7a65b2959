import { type Theme } from '@mui/material';

/**
 * Styles for the main content wrapper
 */
export const contentWrapperStyles = {
  flex: 1,
  position: 'relative',
  zIndex: 5,
  minWidth: 0,
  background: (theme: Theme) =>
    theme.palette.mode === 'light'
      ? theme.palette.common.white // White background for light theme
      : theme.palette.neutral[900], // Dark background for dark theme
};

/**
 * Styles for the inner content box
 */
export const innerBoxStyles = {
  height: '100%',
  p: { xs: 2, sm: 3 },
};

/**
 * Container styles
 */
export const containerStyles = {
  height: '100%',
};

import { type SxProps, type Theme } from '@mui/material';

/**
 * Styles for the language dropdown menu
 */
export const getLanguageMenuStyles = (): SxProps<Theme> => ({
  '& .MuiMenuItem-root': {
    px: 2,
    py: 1,
  },
});

/**
 * Styles for the language button
 */
export const getLanguageButtonStyles = (theme: Theme): SxProps<Theme> => ({
  color: theme.palette.text.primary,
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
  },
});

/**
 * Styles for the language menu item
 */
export const getLanguageMenuItemStyles = (theme: Theme): SxProps<Theme> => ({
  '&.Mui-selected': {
    backgroundColor: theme.palette.action.selected,
    '&:hover': {
      backgroundColor: theme.palette.action.hover,
    },
  },
});

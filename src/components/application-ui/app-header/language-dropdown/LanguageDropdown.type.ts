import { type SxProps, type Theme } from '@mui/material';

/**
 * Props for the LanguageDropdown component
 */
export interface LanguageDropdownProps {
  /**
   * Color of the dropdown button
   */
  color?: 'inherit' | 'primary' | 'secondary' | 'warning' | 'info' | 'success' | 'error';

  /**
   * Additional styles for the dropdown button
   */
  sx?: SxProps<Theme>;
}

/**
 * Props for the LanguageButton component
 */
export interface LanguageButtonProps {
  /**
   * Color of the button
   */
  color: LanguageDropdownProps['color'];

  /**
   * Additional styles for the button
   */
  sx: LanguageDropdownProps['sx'];

  /**
   * Click handler for the button
   */
  onClick: (event: React.MouseEvent<HTMLButtonElement>) => void;

  /**
   * Country code for the flag
   */
  flag: string;
}

/**
 * Props for the LanguageMenu component
 */
export interface LanguageMenuProps {
  /**
   * Anchor element for the menu
   */
  anchorEl: null | HTMLElement;

  /**
   * Whether the menu is open
   */
  open: boolean;

  /**
   * Callback fired when the menu is closed
   */
  onClose: () => void;

  /**
   * Current language
   */
  currentLanguage: Language;

  /**
   * Callback fired when a language is selected
   */
  onLanguageChange: (language: Language) => void;
}

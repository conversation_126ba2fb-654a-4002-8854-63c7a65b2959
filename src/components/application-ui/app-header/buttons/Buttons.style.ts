import { alpha, type SxProps, type Theme } from '@mui/material';

/**
 * Common styles for header buttons
 */
export const getCommonButtonStyles = (theme: Theme): SxProps<Theme> => ({
  '&:hover': {
    background: alpha(theme.palette.primary.main, 0.1),
    color: theme.palette.primary.main,
  },
  borderRadius: 50,
  '& .MuiSvgIcon-root': {
    fontSize: 21,
  },
  p: 1,
});

/**
 * Styles for the user profile button
 */
export const getUserProfileButtonStyles = (theme: Theme): SxProps<Theme> => ({
  p: 0,
  borderRadius: 50,
  '&:hover': {
    boxShadow: `0 0 0 3px ${theme.palette.primary.main}`,
  },
});

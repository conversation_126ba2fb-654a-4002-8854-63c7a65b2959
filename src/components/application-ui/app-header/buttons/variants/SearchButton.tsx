import { Button } from '@/components/base/inputs/button';
import SearchRoundedIcon from '@mui/icons-material/SearchRounded';
import { useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { getCommonButtonStyles } from '../Buttons.style';
import type { SearchButtonProps } from '../Buttons.type';

/**
 * Search button component for the header
 */
export const SearchButton = ({ onClick }: SearchButtonProps) => {
  const theme = useTheme();
  const { t } = useTranslation();

  return (
    <Button
      title={t('common.label.search')}
      sx={getCommonButtonStyles(theme)}
      color="inherit"
      onClick={onClick}
      customStyle="icon"
    >
      <SearchRoundedIcon />
    </Button>
  );
};

export default SearchButton;

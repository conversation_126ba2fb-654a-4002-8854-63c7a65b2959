import { PulseBadge } from '@/components/base/data-display/badge';
import { Button } from '@/components/base/inputs/button';
import { transformText } from '@/utils/text-transform.util';
import NotificationsNoneRoundedIcon from '@mui/icons-material/NotificationsNoneRounded';
import { useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { getCommonButtonStyles } from '../Buttons.style';
import type { NotificationsButtonProps } from '../Buttons.type';

/**
 * Notifications button component for the header
 */
export const NotificationsButton = ({ onClick }: NotificationsButtonProps) => {
  const theme = useTheme();
  const { t } = useTranslation();

  return (
    <Button
      title={transformText(t('common.label.notifications'), 'sentenceCase')}
      sx={getCommonButtonStyles(theme)}
      color="inherit"
      onClick={onClick}
      customStyle="icon"
    >
      <PulseBadge
        sx={{
          '& .MuiBadge-badge': {
            boxShadow: 'none',
          },
        }}
        overlap="rectangular"
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
        variant="dot"
        color="success"
      >
        <NotificationsNoneRoundedIcon />
      </PulseBadge>
    </Button>
  );
};

export default NotificationsButton;

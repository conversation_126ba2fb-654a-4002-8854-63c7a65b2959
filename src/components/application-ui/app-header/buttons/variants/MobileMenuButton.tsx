import { Button } from '@/components/base/inputs/button';
import MenuRoundedIcon from '@mui/icons-material/MenuRounded';
import { useTheme } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { getCommonButtonStyles } from '../Buttons.style';
import type { MobileMenuButtonProps } from '../Buttons.type';

/**
 * Mobile menu button component for the header
 */
export const MobileMenuButton = ({ onClick }: MobileMenuButtonProps) => {
  const theme = useTheme();
  const { t } = useTranslation();

  return (
    <Button
      title={t('common.label.toggleMenu')}
      sx={getCommonButtonStyles(theme)}
      color="inherit"
      onClick={onClick}
      customStyle="icon"
    >
      <MenuRoundedIcon />
    </Button>
  );
};

export default MobileMenuButton;

import { usePopover } from '@/hooks/ui/use-popover.hook';

/**
 * Props for the SearchButton component
 */
export interface SearchButtonProps {
  /**
   * Callback fired when the button is clicked
   */
  onClick: () => void;
}

/**
 * Props for the NotificationsButton component
 */
export interface NotificationsButtonProps {
  /**
   * Callback fired when the button is clicked
   */
  onClick: () => void;
}

/**
 * Props for the UserProfileButton component
 */
export interface UserProfileButtonProps {
  /**
   * Popover hook return value
   */
  popover: ReturnType<typeof usePopover>;
}

/**
 * Props for the MobileMenuButton component
 */
export interface MobileMenuButtonProps {
  /**
   * Callback fired when the button is clicked
   */
  onClick?: () => void;
}

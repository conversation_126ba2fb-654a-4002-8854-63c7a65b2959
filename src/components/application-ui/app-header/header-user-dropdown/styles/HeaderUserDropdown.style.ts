import { type SxProps, type Theme } from '@mui/material';

/**
 * Styles for the user dropdown menu
 */
export const getUserDropdownStyles = (): SxProps<Theme> => ({
  width: 280,
  p: 0,
});

/**
 * Styles for the user profile section in the dropdown
 */
export const getUserProfileSectionStyles = (): SxProps<Theme> => ({
  px: 2,
  pt: 2,
  pb: 1.5,
});

/**
 * Styles for the menu options section in the dropdown
 */
export const getMenuOptionsSectionStyles = (): SxProps<Theme> => ({
  p: 1,
});

/**
 * Styles for the sign out button section in the dropdown
 */
export const getSignOutButtonSectionStyles = (): SxProps<Theme> => ({
  p: 1,
});

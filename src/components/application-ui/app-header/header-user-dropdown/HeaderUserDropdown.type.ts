import { type MenuProps } from '@mui/material';

/**
 * Origin position for menus
 */
export interface Origin {
  /**
   * Vertical position
   */
  vertical: 'top' | 'bottom' | 'center';

  /**
   * Horizontal position
   */
  horizontal: 'left' | 'right' | 'center';
}

/**
 * Props for the HeaderUserDropdown component
 */
export interface HeaderUserDropdownProps extends Omit<MenuProps, 'open'> {
  /**
   * Whether the dropdown is open
   */
  open: boolean;

  /**
   * <PERSON><PERSON> fired when the dropdown is closed
   */
  onClose: () => void;

  /**
   * Anchor origin for the dropdown
   */
  anchorOrigin?: Origin;

  /**
   * Transform origin for the dropdown
   */
  transformOrigin?: Origin;
}

/**
 * Props for the MenuOptions component
 */
export interface MenuOptionsProps {
  /**
   * Callback fired when an option is selected
   */
  onClose: () => void;
}

/**
 * Props for the SignOutButton component
 */
export interface SignOutButtonProps {
  /**
   * Callback fired when the button is clicked
   */
  onClick: () => void;
}

export type AlertSeverity = 'error' | 'success' | 'info' | 'warning';

export type AlertData = {
  severity: AlertSeverity;
  title: string;
  message?: string;
};

export type AlertResolver = (errorCode?: string, message?: string) => AlertData | null;

/**
 * This function retrieves an alert object based on the provided error code and message.
 * If a custom resolver function is provided, it will be used to generate the alert object.
 * If no custom resolver is provided, a default alert object will be created with an error severity,
 * a 'Failed' title, and a fallback message.
 *
 * @param errorCode - The error code to use for generating the alert object. Optional.
 * @param message - The custom message to use for generating the alert object. Optional.
 * @param resolver - A custom function to resolve the alert object based on the error code and message. Optional.
 *
 * @returns An AlertData object representing the alert to be displayed.
 *
 * @example
 * ```typescript
 * const customResolver: AlertResolver = (errorCode, message) => {
 *   if (errorCode === '404') {
 *     return { severity: 'warning', title: 'Not Found', message: 'The requested resource was not found.' };
 *   }
 *   return null;
 * };
 *
 * const alert = getAlertFromErrorCode('404', 'Custom error message', customResolver);
 * console.log(alert); // Output: { severity: 'warning', title: 'Not Found', message: 'The requested resource was not found.' }
 * ```
 */
export const getAlertFromErrorCode = (
  errorCode?: string,
  message?: string,
  resolver?: AlertResolver
): AlertData => {
  const fallbackMessage = message ?? 'An unexpected error occurred. Please try again.';

  const customAlert = resolver?.(errorCode, message);
  if (customAlert) {
    return customAlert;
  } else {
    return {
      severity: 'error',
      title: 'Failed',
      message: fallbackMessage,
    };
  }
};

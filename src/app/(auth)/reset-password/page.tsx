'use client';

import { AuthLayout } from '@/components/application-ui/layouts/auth-layout';
import { ResetPasswordForm } from '@/components/features/auth/forms';
import { GuestGuard as Layout } from '@/components/features/auth/guards/guest-guard';
import { usePageTitle } from '@/hooks/ui/use-page-title.hook';
import { type JSX } from 'react';

const PageContent = (): JSX.Element => {
  return (
    <AuthLayout>
      <ResetPasswordForm />
    </AuthLayout>
  );
};
const Page = (): JSX.Element => {
  usePageTitle('common.action.recoverPassword');
  return (
    <Layout>
      <PageContent />
    </Layout>
  );
};

export default Page;

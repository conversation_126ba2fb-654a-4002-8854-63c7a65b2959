'use client';

import { FilterableSidebarLayout } from '@/components/application-ui/layouts/filterable-sidebar-layout';
import { Button } from '@/components/base/inputs/button';
import { useUserActions, useUserData, useUserFilters } from '@/components/features/user/hooks';
import { UserDataGrid, UserFilterPanel } from '@/components/features/user/user-listing';
import { usePageTitle } from '@/hooks/ui/use-page-title.hook';
import transformText from '@/utils/text-transform.util';
import { type JSX } from 'react';
import { useTranslation } from 'react-i18next';

const pageMeta = {
  title: 'common.label.user',
  description: 'user.sentence.pageDesc',
};

/**
 * UserPage - View and manage user accounts
 */
const UserPage = (): JSX.Element => {
  usePageTitle(pageMeta.title);
  const { t } = useTranslation();
  const translatedTitle = t(pageMeta.title);
  const transformedTitle = transformText(translatedTitle, 'sentenceCase');
  const translatedDescription = t(pageMeta.description);
  const transformedDescription = transformText(translatedDescription, 'sentenceCase');

  const {
    filterState,
    appliedFilters,
    handleApplyFilters,
    handleResetFilters,
    handleFilterChange,
  } = useUserFilters();
  const { users, isLoading, refreshUsers } = useUserData(appliedFilters);
  const { handleDeleteUser, handleCreateUser } = useUserActions(refreshUsers);

  const pageHeadingConfig = {
    title: transformedTitle,
    description: transformedDescription,
    actions: (
      <Button
        sx={{ mt: { xs: 2, md: 0 } }}
        variant="contained"
        fullWidth
        onClick={handleCreateUser}
      >
        {t('common.label.newUser')}
      </Button>
    ),
  };

  return (
    <FilterableSidebarLayout
      pageHeading={pageHeadingConfig}
      sidebarContent={
        <UserFilterPanel
          filters={{
            filters: filterState.filters,
          }}
          onFilterChange={handleFilterChange}
          onSearch={handleApplyFilters}
          onClear={handleResetFilters}
        />
      }
    >
      <UserDataGrid
        users={users}
        isLoading={isLoading}
        onDeleteUser={handleDeleteUser}
      />
    </FilterableSidebarLayout>
  );
};

export default UserPage;

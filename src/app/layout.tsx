import type { Metadata, Viewport } from 'next';
import type { ReactNode } from 'react';
import '@/global.css';
import { NProgress } from '@/components/base/feedback/nprogress';
import { DocumentLayout } from '@/layouts/document';
import { Provider as QueryProvider } from '@/providers/tanstack/query/Provider';
import { restoreCustomization } from '@/utils/server-side-customization.util';

export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: {
    default: 'Quantum Play',
    template: `%s | Quantum Play`,
  },
  description: 'A customizable white-label back office',
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  colorScheme: 'dark',
};

interface LayoutProps {
  children: ReactNode;
}

const Layout = async ({ children }: LayoutProps) => {
  const customization = await restoreCustomization();

  return (
    <html lang="en">
      <body>
        <QueryProvider>
          <DocumentLayout customization={customization}>
            {children}
            <NProgress />
          </DocumentLayout>
        </QueryProvider>
      </body>
    </html>
  );
};

export default Layout;

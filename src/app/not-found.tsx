'use client';

import { WarningAvatar } from '@/components/base/data-display/avatar';
import { Button } from '@/components/base/inputs/button';
import { RouterLink } from '@/components/base/navigation/router-link';
import { Typography } from '@/components/base/typography';
import ROUTES from '@/router/routes';
import WestRoundedIcon from '@mui/icons-material/WestRounded';
import { Box, Container, Divider, Stack } from '@mui/material';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

/**
 * NotFoundPage component
 *
 * This component renders a 404 Not Found page with a warning message,
 * explanation, and a button to return to the home page.
 *
 * @returns {JSX.Element} The rendered NotFoundPage component
 */
const NotFoundPage = () => {
  const { t } = useTranslation();

  /** Workaround hydration issue start */
  /*
   * NextJS will render this page even when defined 'use client'
   * This causing hydration issue if client side lanaguge is other than default language
   */
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;
  /** Workaround hydration issue end */

  return (
    <Container maxWidth="sm">
      <Stack
        spacing={4}
        justifyContent="center"
        alignItems="center"
        direction="column"
        textAlign="center"
        py={8}
      >
        <WarningAvatar size={84} />

        <Typography
          variant="h2"
          fontWeight={700}
          color="text.primary"
          gutterBottom
          caseTransform="sentenceCase"
        >
          {t('common.label.pageNotFound')}
        </Typography>

        <Typography
          variant="h4"
          fontWeight={500}
          color="text.secondary"
          caseTransform="sentenceCase"
        >
          {t('common.sentence.pageMoved')}
        </Typography>

        <Divider sx={{ width: '100%' }}>
          <Box
            sx={{
              width: 60,
              height: 4,
              backgroundColor: 'primary.main',
              borderRadius: 2,
            }}
          />
        </Divider>

        <Button
          variant="outlined"
          color="secondary"
          component={RouterLink}
          href={ROUTES.INDEX}
          startIcon={<WestRoundedIcon />}
        >
          {t('common.action.goToHome')}
        </Button>
      </Stack>
    </Container>
  );
};

export default NotFoundPage;

import {
  ExampleRoute,
  ItemRoute,
  MongoRoute,
  TypesenseRoute,
  UploadRoute,
} from '#src/modules/example/index.js';
import { UserRoute } from '#src/modules/user/index.js';

import { getPackageJson } from '#src/utils/file.util.js';

// Get the package.json content
const packageJson = getPackageJson();

// Extract the major version number
const majorVersion = packageJson.version.split('.')[0];

// Function to generate prefix
const generatePrefix = (module) => `${API_PREFIX}/v${majorVersion}/${BACKOFFICE_ROUTES}/${module}`;
const API_PREFIX = '/api';
const BACKOFFICE_ROUTES = 'bo';
const routeGroups = {
  [BACKOFFICE_ROUTES]: [
    {
      routes: ExampleRoute,
      opts: { prefix: generatePrefix('examples') },
    },
    {
      routes: ItemRoute,
      opts: { prefix: generatePrefix('examples') },
    },
    {
      routes: UploadRoute,
      opts: { prefix: generatePrefix('examples') },
    },
    {
      routes: MongoRoute,
      opts: { prefix: generatePrefix('mongos') },
    },
    {
      routes: TypesenseRoute,
      opts: { prefix: generatePrefix('typesenses') },
    },
    {
      routes: UserRoute,
      opts: { prefix: generatePrefix('users') },
    },
  ],
};

export default async (fastify, options) => {
  await Promise.all(
    routeGroups[BACKOFFICE_ROUTES].map(async ({ routes, opts }) => {
      await fastify.register(routes, { ...options, ...opts });
    }),
  );
};

export const VALIDATIONS = {
  validator: {
    $jsonSchema: {
      bsonType: "object",
      required: [
        "action",
        "actor",
        "context",
        "details",
        "event",
        "status",
        "target",
        "entityAccessId",
        "hierarchyLevel",
        "timestamp",
      ],
      properties: {
        _id: {
          bsonType: "objectId",
        },
        action: {
          bsonType: "object",
          required: [
            "translationKey",
            "translationParams",
          ],
          properties: {
            translationKey: { bsonType: ["string", "null"],},
            translationParams: {
              bsonType: "object",
            },
          },
        },
        actor: {
          bsonType: "object",
          required: [
            "type",
            "id",
            "username",
            "role",
            "department",
            "merchant",
            "organization",
          ],
          properties: {
            department: { bsonType: "string" },
            merchant: {
              bsonType: ["object", "null"],
              properties: {
                id: { bsonType: "string" },
                code: { bsonType: "string" },
                name: { bsonType: "string" },
                prefix: { bsonType: "string" },
              },
            },
            organization: {
              bsonType: ["object", "null"],
              required: ["id", "code", "name", "prefix"],
              properties: {
                id: { bsonType: "string" },
                code: { bsonType: "string" },
                name: { bsonType: "string" },
                prefix: { bsonType: "string" },
              },
            },
             root: {
              bsonType: ["object", "null"],
              properties: {
                id: { bsonType: "string" },
                code: { bsonType: "string" },
                name: { bsonType: "string" },
                prefix: { bsonType: "string" },
              },
            },
            id: { bsonType: "string" },
            username: { bsonType: "string" },
            type: { bsonType: "string" },
            role: { bsonType: "string" },
          },
        },
        context: {
          bsonType: "object",
          required: ["device", "ip", "location", "userAgent"],
          properties: {
            device: { bsonType: "string" },
            ip: { bsonType: "string" },
            location: { bsonType: "string" },
            userAgent: { bsonType: "string" },
            host: { bsonType: "string" },
            origin: { bsonType: "string" },
          },
        },
        description: {
          bsonType: "object",
          required: [
            "translationKey",
            "translationParams",
          ],
          properties: {
            translationKey: { bsonType: ["string", "null"] },
            translationParams: {
              bsonType: "object",
            },
          },
        },
        details: {
          bsonType: "object",
          required: ["request"],
          properties: {
            // afterState: { bsonType: "object" },
            // beforeState: { bsonType: "object" },
            error: { bsonType: "object" },
            metrics: { bsonType: "object" },
            request: {
              bsonType: "object",
              required: ["path", "requestId", "statusCode", "method"],
              properties: {
                parameters: { bsonType: "object" },
                path: { bsonType: "string" },
                method: {
                  bsonType: "string",
                  enum: [
                    "GET",
                    "HEAD",
                    "POST",
                    "PUT",
                    "DELETE",
                    "TRACE",
                    "CONNECT",
                    "OPTIONS",
                    "PATCH",
                  ],
                },
                requestBody: { bsonType: "object" },
                requestId: { bsonType: "string" },
                statusCode: { bsonType: "int" },
              },
            },
            result: { bsonType: "object" },
          },
        },
        event: {
          bsonType: ["string", "null"],
          description: "The type of event logged",
        },
        module: {
          bsonType: ["string", "null"],
        },
        status: {
          bsonType: "string",
          description: "Status of the logged action",
          enum: ["Success", "Failed"],
        },
        target: {
          bsonType: "array",
          items: {
            bsonType: "object",
            required: ["referenceId", "model"],
            properties: {
              referenceId: { bsonType: "string" },
              referenceDetails: { bsonType: ["object", "null"], },
              model: { bsonType: "string" },
              changes: {
                bsonType: "object",
                properties: {
                  beforeState: { bsonType: "object" },
                  afterState: { bsonType: "object" },
                },
              },
            },
          },
        },
        entityAccessId: {
          bsonType: "string",
          description: "ID of the entity accessing the system",
        },
        hierarchyLevel: {
          bsonType: "string",
          description: "Level of the entity hierarchy",
        },
        timestamp: {
          bsonType: "date",
          description: "Timestamp of the log entry",
        },
      },
    },
  },
  validationAction: "error",
};

export const INDEXES = [
  { key: { entityAccessId: 1, timestamp: -1 }, name: 'idx_entityAccessId_time' }, // Sort by recent logs per entity (Descending order)
  { key: { 'details.request.requestId': 1 }, name: 'idx_details_request_requestId'}, // Key for deduplication
  { key: { action: 1 }, name: 'idx_action' }, // Filter by action
  { key: { 'actor.userId': 1, timestamp: -1 }, name: 'idx_actor_userId_time' }, // Search logs by userId
  { key: { 'actor.username': 1 }, name: 'idx_actor_username' }, // Search logs by username
  { key: { 'context.ip': 1 }, name: 'idx_context_ip' }, // Search logs by IP
  { key: { event: 1 }, name: 'idx_event' }, // Search by event type
  { key: { event: 1, action: 1 }, name: 'idx_event_action' }, // Search by event and action
  { key: { status: 1 }, name: 'idx_status' }, // Filter logs by status
  { key: { 'target.model': 1, 'target.referenceId': 1, timestamp: -1 }, name: 'idx_target_model_referenceId_time' }, // Lookup logs by target model & reference ID, sorted by latest
];

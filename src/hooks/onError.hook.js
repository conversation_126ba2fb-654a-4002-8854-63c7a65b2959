import fp from 'fastify-plugin';

/**
 * onError hook
 *
 * This hook is executed when an error occurs during the request/response lifecycle.
 * It can be used for custom error handling or logging.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} options - Hook options
 */
const onErrorHook = (fastify, options) => {
  fastify.addHook('onError', async (request, reply, error) => {
    fastify.log.debug('Executing onError hook');
    reply.statusCode = error.statusCode || 500;
    fastify.log.error({ request, reply, error }, 'Error occurred');
    if (fastify.sentry) {
      fastify.sentry.captureException(error);
    }
  });
};

export default fp(onErrorHook, {
  name: 'onErrorHook',
});

import fp from 'fastify-plugin';

/**
 * onRoute hook
 *
 * This hook is executed when a new route is registered.
 * It can be used for logging or modifying route options.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} options - Hook options
 */
const onRouteHook = (fastify, options) => {
  fastify.addHook('onRoute', async (routeOptions) => {
    fastify.log.debug('Executing onRoute hook');
  });
};

export default fp(onRouteHook, {
  name: 'onRouteHook',
});

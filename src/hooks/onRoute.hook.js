import { CoreSchema } from '#src/modules/core/schemas/index.js';
import fp from 'fastify-plugin';

/**
 * onRoute hook
 *
 * This hook is executed when a new route is registered.
 * It can be used for logging or modifying route options.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} options - Hook options
 */
const onRouteHook = (fastify, options) => {
  fastify.addHook('onRoute', async (routeOptions) => {
    fastify.log.debug('Executing onRoute hook');

    routeOptions.schema = routeOptions.schema || {};
    routeOptions.schema.headers = routeOptions.schema.headers || { type: 'object', properties: {} };
    routeOptions.schema.headers.type = 'object';

    // Add common header properties to the schema headers object
    // This will automatically be included in the response when the route is called.
    // You can customize this object according to your needs.

    // Example: Add a custom header property
    // routeOptions.schema.headers.properties.customHeader = {
    //   type: 'string',
    //   description: 'A custom header property',
    // };
    routeOptions.schema.headers.properties = {
      ...CoreSchema.COMMON_HEADERS.properties,
      ...routeOptions.schema.headers.properties,
    };
  });
};

export default fp(onRouteHook, {
  name: 'onRouteHook',
});

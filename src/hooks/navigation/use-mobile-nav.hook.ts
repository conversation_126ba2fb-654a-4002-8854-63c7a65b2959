import { useCallback, useEffect, useRef, useState } from 'react';
import { usePathname } from './use-pathname.hook';

/**
 * Interface for the return value of the useMobileNav hook
 */
export interface UseMobileNavReturn {
  /** Function to open the mobile navigation */
  handleOpen: () => void;

  /** Function to close the mobile navigation */
  handleClose: () => void;

  /** <PERSON><PERSON><PERSON> indicating whether the mobile navigation is open */
  open: boolean;
}

/**
 * Hook for managing mobile navigation state
 *
 * This hook provides state management for mobile navigation, automatically
 * closing the navigation when the user navigates to a different page.
 *
 * @returns Object containing the open state and functions to control it
 */
export const useMobileNav = (): UseMobileNavReturn => {
  const pathname = usePathname();
  const [open, setOpen] = useState<boolean>(false);

  // Store the previous pathname to compare with the current one
  const prevPathnameRef = useRef(pathname);

  /**
   * Handles pathname changes by closing the mobile navigation
   * only when the pathname has actually changed
   */
  const handlePathnameChange = useCallback((): void => {
    // Only close the sidebar if the pathname has actually changed
    if (open && pathname !== prevPathnameRef.current) {
      setOpen(false);
      // Update the previous pathname reference
      prevPathnameRef.current = pathname;
    }
  }, [open, pathname]);

  // Effect to handle pathname changes
  useEffect(() => {
    handlePathnameChange();
  }, [pathname, handlePathnameChange]);

  /**
   * Opens the mobile navigation
   */
  const handleOpen = useCallback((): void => {
    setOpen(true);
  }, []);

  /**
   * Closes the mobile navigation
   */
  const handleClose = useCallback((): void => {
    setOpen(false);
  }, []);

  return {
    handleOpen,
    handleClose,
    open,
  };
};

export default useMobileNav;

import fp from 'fastify-plugin';

/**
 * onResponse hook
 *
 * This hook is executed when a response has been sent.
 * It can be used for logging or clean up purposes.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} options - Hook options
 */
const onResponseHook = (fastify, options) => {
  fastify.addHook('onResponse', async (request, reply) => {
    fastify.log.debug('Executing onResponse hook');
    let message = 'Request completed successfully';
    if (reply.statusCode >= 400) {
      message = 'Request completed with error';
    }
    fastify.log.info({ request, reply }, message);
  });
};

export default fp(onResponseHook, {
  name: 'onResponseHook',
});

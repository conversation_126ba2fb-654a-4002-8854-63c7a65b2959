import { bypassAccessCheck } from '#src/utils/access.util.js';
import { finalizeAuditTrailEntry } from '#src/utils/audit-trail.util.js';
import fp from 'fastify-plugin';

/**
 * onResponse hook
 *
 * This hook is executed when a response has been sent.
 * It can be used for logging or clean up purposes.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} options - Hook options
 */
const onResponseHook = (fastify, options) => {
  fastify.addHook('onResponse', async (request, reply) => {
    fastify.log.debug('Executing onResponse hook');
    const logMessage =
      reply.statusCode >= 400 ? 'Request completed with error' : 'Request completed successfully';

    fastify.log.info({ request, reply }, logMessage);

    if (!bypassAccessCheck(request)) {
      finalizeAuditTrailEntry(fastify, request, reply);
    }
  });
};

export default fp(onResponseHook, {
  name: 'onResponseHook',
});

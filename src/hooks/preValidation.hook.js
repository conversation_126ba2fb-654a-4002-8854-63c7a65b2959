import fp from 'fastify-plugin';

/**
 * preValidation hook
 *
 * This hook is executed after the request has been parsed and before the input validation.
 * It can be used for custom validation or data manipulation before the actual validation.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} options - Hook options
 */
const preValidationHook = (fastify, options) => {
  fastify.addHook('preValidation', async (request, reply) => {
    fastify.log.debug('Executing preValidation hook');
    // Add your preValidation logic here
  });
};

export default fp(preValidationHook, {
  name: 'preValidationHook',
});

import fp from 'fastify-plugin';
import i18next from 'i18next';

import { v4 as uuidv4 } from 'uuid';

/**
 * onRequest hook
 *
 * This hook is executed for every request before the routing is executed.
 * It can be used for logging, setting up request-specific variables, or performing
 * initial checks before processing the request.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} options - Hook options
 */
const onRequestHook = (fastify, options) => {
  fastify.addHook('onRequest', async (request, reply) => {
    fastify.log.debug('Executing onRequest hook');
    request.startTime = Date.now();
    request.id = uuidv4();

    // Parse the accept-language header
    const acceptLanguage = request.headers['accept-language'];
    let language = i18next.options.fallbackLng;

    if (acceptLanguage) {
      const languages = acceptLanguage.split(',').map((lang) => lang.split(';')[0]);
      language = languages[0].split('-')[0] || language;
    }
    await i18next.changeLanguage(language);
    request.locale = acceptLanguage; // Store the detected locale in the request object
  });
};

export default fp(onRequestHook, {
  name: 'onRequestHook',
});

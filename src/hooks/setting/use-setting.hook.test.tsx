import {
  settingService,
  type PersonalFormData,
  type SafetyFormData,
  type Setting,
  type SettingCategory,
  type SettingOption,
  type ThemesFormData,
} from '@/services/settings';
import { type ApiResponse } from '@/types/api-responses.type';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { renderHook, waitFor } from '@testing-library/react';
import { type ReactNode } from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useGetSettingOptions, useGetSettings, useUpdateSetting } from './use-setting.hook';

// Mock the setting service
vi.mock('@/services/settings');

describe('useGetSettings', () => {
  const mockSettings: Setting[] = [
    {
      id: '1',
      category: 'personal',
      field: 'appearance',
      accessLevel: [],
      customSettings: {
        value: 'light',
        version: 1,
        createdBy: 'user',
        updatedBy: 'user',
        createdAt: '2025-07-01T12:34:56Z',
        updatedAt: '2025-07-01T12:34:56Z',
      },
    },
    {
      id: '2',
      category: 'personal',
      field: 'recordPerPage',
      accessLevel: [],
      customSettings: {
        value: '20',
        version: 1,
        createdBy: 'user',
        updatedBy: 'user',
        createdAt: '2025-07-01T12:34:56Z',
        updatedAt: '2025-07-01T12:34:56Z',
      },
    },
  ];
  const mockResponse: ApiResponse<Setting[]> = {
    message: 'Settings retrieved successfully',
    data: mockSettings,
  };
  let queryClient: QueryClient;
  let wrapper: any;

  beforeEach(() => {
    vi.resetAllMocks();
    vi.resetModules();

    queryClient = new QueryClient({ defaultOptions: { queries: { retry: false } } });

    // Wrapper with QueryClient
    wrapper = () => {
      return ({ children }: { children: ReactNode }) => (
        <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
      );
    };
  });

  it('should fetch settings successfully', async () => {
    vi.mocked(settingService.getSettings).mockResolvedValue(mockResponse);

    const { result } = renderHook(() => useGetSettings('personal'), {
      wrapper: wrapper(),
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(settingService.getSettings).toHaveBeenCalledWith('personal');
    expect(settingService.getSettings).toHaveBeenCalledOnce();

    expect(result.current.data).toEqual(mockResponse);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();

    const queryData = queryClient.getQueryData(['settings', 'personal']);
    expect(queryData).toEqual(mockResponse);
  });

  it('should handle error when fetching settings fails', async () => {
    const mockError = new Error('mock api fail');

    vi.mocked(settingService.getSettings).mockRejectedValue(mockError);

    const { result } = renderHook(() => useGetSettings('personal'), {
      wrapper: wrapper(),
    });

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    expect(settingService.getSettings).toHaveBeenCalledWith('personal');
    expect(settingService.getSettings).toHaveBeenCalledOnce();

    expect(result.current.data).toBeUndefined();
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toEqual(mockError);

    const queryData = queryClient.getQueryData(['settings', 'personal']);
    expect(queryData).toEqual(undefined);
  });

  it('should use correct query key for different categories', async () => {
    vi.mocked(settingService.getSettings).mockResolvedValue(mockResponse);

    const { result: themesResult } = renderHook(() => useGetSettings('themes'), {
      wrapper: wrapper(),
    });

    await waitFor(() => {
      expect(themesResult.current.isSuccess).toBe(true);
    });

    expect(settingService.getSettings).toHaveBeenCalledWith('themes');

    const themesQueryData = queryClient.getQueryData(['settings', 'themes']);
    expect(themesQueryData).toEqual(mockResponse);

    const { result: safetyResult } = renderHook(() => useGetSettings('safety'), {
      wrapper: wrapper(),
    });

    await waitFor(() => {
      expect(safetyResult.current.isSuccess).toBe(true);
    });

    expect(settingService.getSettings).toHaveBeenCalledWith('safety');

    const safetyQueryData = queryClient.getQueryData(['settings', 'themes']);
    expect(safetyQueryData).toEqual(mockResponse);
  });

  it('should use custom options when provided', async () => {
    vi.mocked(settingService.getSettings).mockResolvedValue(mockResponse);

    // Set enabled false so that the query will not execute automatically
    const customOptions = { enabled: false };
    const { result } = renderHook(() => useGetSettings('personal', customOptions), {
      wrapper: wrapper(),
    });

    // Since not executed, isSuccess will not become true
    await expect(
      waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      })
    ).rejects.toThrow();

    const queryData = queryClient.getQueryData(['settings', 'personal']);
    expect(queryData).toEqual(undefined);
  });
});

describe('useGetSettingOptions', () => {
  const mockSettingOptions: SettingOption = {
    appearance: [
      { label: 'System', value: 'system' },
      { label: 'Light', value: 'light' },
      { label: 'Dark', value: 'dark' },
    ],
  };
  const mockResponse: ApiResponse<SettingOption> = {
    message: 'Settings retrieved successfully',
    data: mockSettingOptions,
  };
  let queryClient: QueryClient;
  let wrapper: any;

  beforeEach(() => {
    vi.resetAllMocks();
    vi.resetModules();

    queryClient = new QueryClient({ defaultOptions: { queries: { retry: false } } });

    // Wrapper with QueryClient
    wrapper = () => {
      return ({ children }: { children: ReactNode }) => (
        <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
      );
    };
  });

  it('should fetch settings successfully', async () => {
    vi.mocked(settingService.getSettingOptions).mockResolvedValue(mockResponse);

    const { result } = renderHook(() => useGetSettingOptions('personal'), {
      wrapper: wrapper(),
    });

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(settingService.getSettingOptions).toHaveBeenCalledWith('personal');
    expect(settingService.getSettingOptions).toHaveBeenCalledOnce();

    expect(result.current.data).toEqual(mockResponse);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();

    const queryData = queryClient.getQueryData(['settingOptions', 'personal']);
    expect(queryData).toEqual(mockResponse);
  });

  it('should handle error when fetching settings fails', async () => {
    const mockError = new Error('mock api fail');

    vi.mocked(settingService.getSettingOptions).mockRejectedValue(mockError);

    const { result } = renderHook(() => useGetSettingOptions('personal'), {
      wrapper: wrapper(),
    });

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    expect(settingService.getSettingOptions).toHaveBeenCalledWith('personal');
    expect(settingService.getSettingOptions).toHaveBeenCalledOnce();

    expect(result.current.data).toBeUndefined();
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toEqual(mockError);

    const queryData = queryClient.getQueryData(['settingOptions', 'personal']);
    expect(queryData).toEqual(undefined);
  });

  it('should use correct query key for different categories', async () => {
    vi.mocked(settingService.getSettingOptions).mockResolvedValue(mockResponse);

    const { result: themesResult } = renderHook(() => useGetSettingOptions('themes'), {
      wrapper: wrapper(),
    });

    await waitFor(() => {
      expect(themesResult.current.isSuccess).toBe(true);
    });

    expect(settingService.getSettingOptions).toHaveBeenCalledWith('themes');

    const themesQueryData = queryClient.getQueryData(['settingOptions', 'themes']);
    expect(themesQueryData).toEqual(mockResponse);

    const { result: safetyResult } = renderHook(() => useGetSettingOptions('safety'), {
      wrapper: wrapper(),
    });

    await waitFor(() => {
      expect(safetyResult.current.isSuccess).toBe(true);
    });

    expect(settingService.getSettingOptions).toHaveBeenCalledWith('safety');

    const safetyQueryData = queryClient.getQueryData(['settingOptions', 'themes']);
    expect(safetyQueryData).toEqual(mockResponse);
  });

  it('should use custom options when provided', async () => {
    vi.mocked(settingService.getSettingOptions).mockResolvedValue(mockResponse);

    // Set enabled false so that the query will not execute automatically
    const customOptions = { enabled: false };
    const { result } = renderHook(() => useGetSettingOptions('personal', customOptions), {
      wrapper: wrapper(),
    });

    // Since not executed, isSuccess will not become true
    await expect(
      waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      })
    ).rejects.toThrow();

    const queryData = queryClient.getQueryData(['settingOptions', 'personal']);
    expect(queryData).toEqual(undefined);
  });
});

describe('useUpdateSetting', () => {
  const mockResponse: ApiResponse<SettingOption> = { message: 'Settings updated successfully' };
  const personalData = { version: 1, appearance: 'system' } as PersonalFormData;
  const themesData = { version: 1, alertBarPosition: 'top' } as ThemesFormData;
  const safetyData = { version: 1, twoFactorSessionTimeoutDays: '30' } as SafetyFormData;
  let queryClient: QueryClient;
  let wrapper: any;

  beforeEach(() => {
    vi.resetAllMocks();
    vi.resetModules();

    queryClient = new QueryClient({ defaultOptions: { mutations: { retry: false } } });

    // Wrapper with QueryClient
    wrapper = () => {
      return ({ children }: { children: ReactNode }) => (
        <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
      );
    };

    vi.spyOn(queryClient, 'invalidateQueries');
  });

  it('should update personal setting successfully', async () => {
    vi.mocked(settingService.updatePersonalSetting).mockResolvedValue(mockResponse);

    const { result } = renderHook(() => useUpdateSetting('personal'), {
      wrapper: wrapper(),
    });

    result.current.mutate(personalData);

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toEqual(mockResponse);

    expect(settingService.updatePersonalSetting).toHaveBeenCalledWith(personalData);
    expect(settingService.updatePersonalSetting).toHaveBeenCalledTimes(1);

    expect(queryClient.invalidateQueries).toHaveBeenCalledWith({
      queryKey: ['settings', 'personal'],
    });
  });

  it('should update themes setting successfully', async () => {
    vi.mocked(settingService.updateThemesSetting).mockResolvedValue(mockResponse);

    const { result } = renderHook(() => useUpdateSetting('themes'), {
      wrapper: wrapper(),
    });

    result.current.mutate(themesData);

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toEqual(mockResponse);

    expect(settingService.updateThemesSetting).toHaveBeenCalledWith(themesData);
    expect(settingService.updateThemesSetting).toHaveBeenCalledTimes(1);

    expect(queryClient.invalidateQueries).toHaveBeenCalledWith({
      queryKey: ['settings', 'themes'],
    });
  });

  it('should update safety setting successfully', async () => {
    vi.mocked(settingService.updateSafetySetting).mockResolvedValue(mockResponse);

    const { result } = renderHook(() => useUpdateSetting('safety'), {
      wrapper: wrapper(),
    });

    result.current.mutate(safetyData);

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toEqual(mockResponse);

    expect(settingService.updateSafetySetting).toHaveBeenCalledWith(safetyData);
    expect(settingService.updateSafetySetting).toHaveBeenCalledTimes(1);

    expect(queryClient.invalidateQueries).toHaveBeenCalledWith({
      queryKey: ['settings', 'safety'],
    });
  });

  it('should handle error when update setting fails', async () => {
    const mockError = new Error('mock api fail');

    vi.mocked(settingService.updatePersonalSetting).mockRejectedValue(mockError);

    const { result } = renderHook(() => useUpdateSetting('personal'), {
      wrapper: wrapper(),
    });

    result.current.mutate(personalData);

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    expect(settingService.updatePersonalSetting).toHaveBeenCalledWith(personalData);
    expect(settingService.updatePersonalSetting).toHaveBeenCalledOnce();

    expect(result.current.data).toBeUndefined();
    expect(result.current.error).toEqual(mockError);

    expect(queryClient.invalidateQueries).not.toHaveBeenCalled();
  });

  it('should use custom options when provided', async () => {
    const mockError = new Error('mock api fail');

    vi.mocked(settingService.updatePersonalSetting).mockRejectedValue(mockError);

    const mockFunction = vi.fn();
    const customOptions = {
      onError: (error: Error) => {
        mockFunction(error);
      },
    };
    const { result } = renderHook(() => useUpdateSetting('personal', customOptions), {
      wrapper: wrapper(),
    });

    result.current.mutate(personalData);

    await expect(
      waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      })
    ).rejects.toThrow();

    expect(mockFunction).toHaveBeenCalledWith(mockError);
  });

  it('should throw error for invalid category', () => {
    const category = 'invalid' as SettingCategory;

    expect(() => {
      renderHook(() => useUpdateSetting(category), {
        wrapper: wrapper(),
      });
    }).toThrow(`Invalid category: ${category}`);
  });
});

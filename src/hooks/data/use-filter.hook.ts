import { useCallback, useState } from 'react';

/**
 * Generic filter state type that can be extended for specific filtering needs
 *
 * @template T - The type of the filter state object
 */
export interface FilterState<T extends Record<string, any> = Record<string, any>> {
  /** The current filters applied */
  filters: T;
}

/**
 * Interface for the return value of the useFilter hook
 *
 * @template T - The type of the filter state object
 */
export interface UseFilterReturn<T extends Record<string, any>> {
  /** The current filter state */
  filterState: FilterState<T>;

  /** Function to update a specific filter */
  updateFilter: <K extends keyof T>(name: K, value: T[K]) => void;

  /** Function to reset all filters to their initial state */
  resetFilters: () => void;
}

/**
 * Hook for managing filter state in data filtering components
 *
 * This hook provides state management for filters commonly used in data,
 * lists, and other filterable components.
 *
 * @template T - The type of the filter state object
 * @param initialFilters - The initial filter values
 * @returns Object containing filter state and functions to update it
 */
export const useFilter = <T extends Record<string, any>>(initialFilters: T): UseFilterReturn<T> => {
  const [filterState, setFilterState] = useState<FilterState<T>>({
    filters: initialFilters,
  });

  const updateFilter = useCallback(<K extends keyof T>(name: K, value: T[K]) => {
    setFilterState((prevState) => ({
      ...prevState,
      filters: {
        ...prevState.filters,
        [name]: value,
      },
    }));
  }, []);

  const resetFilters = useCallback(() => {
    setFilterState({
      filters: initialFilters,
    });
  }, [initialFilters]);

  return {
    filterState,
    updateFilter,
    resetFilters,
  };
};

export default useFilter;

import { useCallback, useEffect, useRef } from 'react';

/**
 * Type for the function that checks if a component is mounted
 */
export type IsMountedFn = () => boolean;

/**
 * Hook for tracking whether a component is currently mounted
 *
 * This hook is useful for preventing memory leaks by checking if a component
 * is still mounted before updating state, especially in async operations.
 *
 * @returns A function that returns `true` if the component is mounted, otherwise `false`
 */
export const useRefMounted = (): IsMountedFn => {
  // Reference to track mounted state
  const isMounted = useRef<boolean>(false);

  // Set up effect to track mounted state
  useEffect(() => {
    // Mark as mounted on initial render
    isMounted.current = true;

    // Mark as unmounted when component is destroyed
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Return memoized function to check mounted state
  return useCallback((): boolean => isMounted.current, []);
};

export default useRefMounted;

import { AuthContext } from '@/contexts/auth/auth.context';
import type { AuthContextValue } from '@/contexts/auth/auth.type';
import { useContext } from 'react';

/**
 * Hook for accessing authentication context and user information
 *
 * This hook provides access to the current authentication state, including
 * the user object, loading state, and utility functions for common auth checks.
 *
 * @throws {Error} If used outside of an AuthProvider
 * @returns {AuthContextValue} Enhanced authentication context with additional utility functions
 */
export const useAuth = (): AuthContextValue => {
  const context = useContext(AuthContext);

  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }

  return context;
};

export default useAuth;

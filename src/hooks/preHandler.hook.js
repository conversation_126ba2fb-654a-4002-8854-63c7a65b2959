import fp from 'fastify-plugin';

/**
 * preHandler hook
 *
 * This hook is executed just before the handler is executed.
 * It's useful for performing operations that should happen right before
 * the route handler, such as input validation or authentication checks.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} options - Hook options
 */
const preHandlerHook = (fastify, options) => {
  fastify.addHook('preHandler', async (request, reply) => {
    fastify.log.debug('Executing preHandler hook');
    // Add your preHandler logic here
  });
};

export default fp(preHandlerHook, {
  name: 'preHandlerHook',
});

import { useCallback, useState } from 'react';

/**
 * A custom React hook that provides a boolean state with a toggle function.
 *
 * @param initial - The initial boolean value for the toggle state. Defaults to false.
 * @returns A tuple containing the current boolean state and a function to toggle it.
 */
export const useToggle = (initial = false): [boolean, () => void] => {
  const [value, setValue] = useState(initial);
  const toggle = useCallback(() => setValue((v) => !v), []);
  return [value, toggle];
};

export default useToggle;

import { useCallback, useRef, useState, type RefObject } from 'react';

/**
 * Interface for the return value of the usePopover hook
 *
 * @template T - The type of HTML element that will be used as the anchor
 */
export interface UsePopoverReturn<T extends HTMLElement> {
  /** Reference to the anchor element */
  anchorRef: RefObject<T | null>;

  /** Function to open the popover */
  handleOpen: () => void;

  /** Function to close the popover */
  handleClose: () => void;

  /** Function to toggle the popover's open state */
  handleToggle: () => void;

  /** <PERSON>olean indicating whether the popover is open */
  open: boolean;
}

/**
 * Hook for managing popover state and anchor element
 *
 * This hook provides state management for popovers, including an anchor
 * reference and functions to control the popover's visibility.
 *
 * @template T - The type of HTML element that will be used as the anchor
 * @returns Object containing the anchor reference, open state, and control functions
 */
export const usePopover = <T extends HTMLElement>(): UsePopoverReturn<T> => {
  // Reference to the anchor element
  const anchorRef = useRef<T | null>(null);

  // State to track whether the popover is open
  const [open, setOpen] = useState<boolean>(false);

  /**
   * Opens the popover
   */
  const handleOpen = useCallback((): void => {
    setOpen(true);
  }, []);

  /**
   * Closes the popover
   */
  const handleClose = useCallback((): void => {
    setOpen(false);
  }, []);

  /**
   * Toggles the popover's open state
   */
  const handleToggle = useCallback((): void => {
    setOpen((prevState) => !prevState);
  }, []);

  return {
    anchorRef,
    handleClose,
    handleOpen,
    handleToggle,
    open,
  };
};

export default usePopover;

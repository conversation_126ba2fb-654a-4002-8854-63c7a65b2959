import { useCallback, useState } from 'react';

interface DialogController<T> {
  data?: T;
  handleClose: () => void;
  handleOpen: (data?: T) => void;
  open: boolean;
}

/**
 * Returns a controller to open/close a dialog, with an optional data value.
 *
 * @returns a controller with the following properties:
 * - `data`: the data passed to `handle<PERSON><PERSON>`, or undefined if the dialog is closed.
 * - `handleClose`: a function to close the dialog.
 * - `handleOpen`: a function to open the dialog, with an optional data value.
 * - `open`: a boolean indicating whether the dialog is open.
 */
export const useDialog = <T = unknown>(): DialogController<T> => {
  const [state, setState] = useState<{ open: boolean; data?: T }>({
    open: false,
    data: undefined,
  });

  const handleOpen = useCallback((data?: T): void => {
    setState((prevState) => ({
      ...prevState,
      open: true,
      data,
    }));
  }, []);

  const handleClose = useCallback((): void => {
    setState((prevState) => ({
      ...prevState,
      open: false,
    }));
  }, []);

  return {
    data: state.data,
    handleClose,
    handleOpen,
    open: state.open,
  };
};

export default useDialog;

import { useCallback, useState, type MouseEvent } from 'react';

/**
 * Interface for the return value of the useAnchor hook
 */
export interface UseAnchorReturn {
  /** The anchor element that is currently open */
  anchorEl: HTMLElement | null;

  /** A boolean indicating whether the component is open */
  open: boolean;

  /** A function to handle click events on the anchor element */
  handleClick: (event: MouseEvent<HTMLElement>) => void;

  /** A function to close the component */
  handleClose: () => void;
}

/**
 * Hook for managing anchor elements used in menus, dropdowns, and popovers
 *
 * This hook provides a convenient way to manage anchor elements and their open state,
 * which is commonly needed for components like menus, dropdowns, and popovers.
 *
 * @returns Object containing the anchor element, open state, and handler functions
 */
export const useAnchor = (): UseAnchorReturn => {
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const open = Boolean(anchorEl);

  const handleClick = useCallback((event: MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  }, []);

  const handleClose = useCallback(() => {
    setAnchorEl(null);
  }, []);

  return {
    anchorEl,
    open,
    handleClick,
    handleClose,
  };
};

export default useAnchor;

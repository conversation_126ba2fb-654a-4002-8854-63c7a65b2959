import { useMediaQuery, type Theme } from '@mui/material';
import { useCallback, useEffect, useState } from 'react';

/**
 * Hook that handles the state and events of the sidebar drawer.
 *
 * It also takes into account the screen size and will automatically
 * open or close the sidebar drawer based on the screen size.
 *
 * @returns An object with the following props:
 *   - `handleToggle`: A function that toggles the sidebar drawer.
 *   - `handleClose`: A function that closes the sidebar drawer.
 *   - `handleOpen`: A function that opens the sidebar drawer.
 *   - `open`: A boolean indicating whether the sidebar drawer is open or not.
 */
export const useSidebarDrawer = () => {
  const lgUp = useMediaQuery((theme: Theme) => theme.breakpoints.up('lg'));
  const [open, setOpen] = useState(lgUp);

  const screenResize = useCallback((): void => {
    if (!lgUp) {
      setOpen(false);
    } else {
      setOpen(true);
    }
  }, [lgUp]);

  useEffect(() => {
    screenResize();
  }, [lgUp, screenResize]);

  const handleToggle = useCallback((): void => {
    setOpen((prevState) => !prevState);
  }, []);

  const handleClose = useCallback((): void => {
    setOpen(false);
  }, []);

  const handleOpen = useCallback((): void => {
    setOpen(true);
  }, []);

  return {
    handleToggle,
    handleClose,
    handleOpen,
    open,
  };
};

export default useSidebarDrawer;

import { useCallback, useEffect, useState } from 'react';

/**
 * Type for scroll direction values
 */
export type ScrollDirection = 'up' | 'down';

/**
 * Configuration options for the useScrollDirection hook
 */
export interface UseScrollDirectionOptions {
  /**
   * The threshold in pixels after which the direction is considered "down"
   * @default 320
   */
  threshold?: number;

  /**
   * Whether to initialize with a specific direction
   * @default 'up'
   */
  initialDirection?: ScrollDirection;
}

/**
 * Hook for detecting the scroll direction of the window
 *
 * This hook listens for scroll events and determines whether the user
 * is scrolling up or down based on the scroll position and threshold.
 *
 * @param options - Configuration options
 * @returns The current scroll direction ('up' or 'down')
 */
export const useScrollDirection = (options: UseScrollDirectionOptions = {}): ScrollDirection => {
  const { threshold = 320, initialDirection = 'up' } = options;

  const [scrollDirection, setScrollDirection] = useState<ScrollDirection>(initialDirection);

  /**
   * Updates the scroll direction based on the current scroll position
   */
  const updateScrollDirection = useCallback(() => {
    const scrollY = window.scrollY;
    const direction: ScrollDirection = scrollY > threshold ? 'down' : 'up';

    if (direction !== scrollDirection) {
      setScrollDirection(direction);
    }
  }, [scrollDirection, threshold]);

  useEffect(() => {
    // Add scroll event listener
    window.addEventListener('scroll', updateScrollDirection);

    // Initial check
    updateScrollDirection();

    // Clean up event listener on unmount
    return () => {
      window.removeEventListener('scroll', updateScrollDirection);
    };
  }, [updateScrollDirection]);

  return scrollDirection;
};

export default useScrollDirection;

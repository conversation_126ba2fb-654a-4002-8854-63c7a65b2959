'use client';

import type { ChangeCaseTransform } from '@/components/base/typography/Typography.type';
import transformText from '@/utils/text-transform.util';
import { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';

/**
 * Sets the page title to the given title, translated using react-i18next and optionally
 * transformed using change-case, appending ' | QPLY 2.0' to the end.
 *
 * @param title The title to set for the page
 * @param caseTransform Optional transformation to apply to the title using change-case
 * @default 'sentenceCase' - The title will be transformed to sentence case
 *
 * @example
 * ```tsx
 * // Basic usage
 * usePageTitle('dashboard');
 *
 * // With text transformation
 * usePageTitle('KYC verification', 'none');
 * ```
 */
export const usePageTitle = (title: string, caseTransform?: ChangeCaseTransform) => {
  const { t } = useTranslation();
  const originalTitleRef = useRef<string | null>(null);

  // This effect runs only on the client side and handles the title setting
  useEffect(() => {
    // Skip execution during server-side rendering
    if (typeof window === 'undefined') return;

    // Store the original title only on the first render
    originalTitleRef.current ??= document.title || '';

    const translatedTitle = t(title);
    const finalTitle = transformText(translatedTitle, caseTransform ?? 'sentenceCase');

    // Set the document title with the QPLY suffix
    document.title = `${finalTitle} | QPLY 2.0`;

    // Cleanup function to restore the original title on unmount
    return () => {
      if (originalTitleRef.current) {
        document.title = originalTitleRef.current;
      }
    };
  }, [t, title, caseTransform]);
};

export default usePageTitle;

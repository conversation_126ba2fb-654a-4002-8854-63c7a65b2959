import { CustomizationContext } from '@/contexts/customization';
import { useContext } from 'react';

/**
 * React hook that returns the values of the CustomizationContext.
 *
 * You can use this hook to get the current customization settings,
 * check if the user has customized the settings, and reset the customization settings.
 *
 * This hook must be used within a CustomizationProvider.
 */
/**
 * Hook to access the customization context
 *
 * @returns The customization context value
 */
export const useCustomization = () => {
  return useContext(CustomizationContext);
};

export default useCustomization;

import fp from 'fastify-plugin';

/**
 * onReady hook
 *
 * This hook is executed when the server is ready to accept connections.
 * It can be used for performing operations after the server is fully initialized.
 *
 * @param {FastifyInstance} fastify - The Fastify instance
 * @param {Object} options - Hook options
 */
const onReadyHook = (fastify, options) => {
  fastify.addHook('onReady', async () => {
    fastify.log.debug('Executing onReady hook');
    // Add your onReady logic here
  });
};

export default fp(onReadyHook, {
  name: 'onReadyHook',
});

import { autoloadModels } from '#src/utils/load-model.util.js';
import fp from 'fastify-plugin';

export class DatabaseFactory {
  constructor(fastify) {
    this.fastify = fastify;
  }

  /**
   * Creates a Fastify plugin for database connection and model loading.
   *
   * @param {string} type - The type of database to connect to.
   * @param {boolean} [loadModels=true] - Whether to automatically load models.
   * @returns {Function} A Fastify plugin function that sets up the database connection and loads models.
   */
  static createPlugin(type, loadModels = true) {
    return fp(async (fastify, options) => {
      const instance = new this(fastify);
      await instance.connect();
      instance.decorate();
      if (loadModels) {
        const dbInstance = type === 'postgres' ? instance.sequelize : null;
        await autoloadModels(fastify, type, dbInstance);
      }
    });
  }

  getReadReplicas() {
    return Object.keys(process.env)
      .filter((key) => key.startsWith('POSTGRES_READ_REPLICA_') && key.endsWith('_HOST'))
      .map((key) => {
        const prefix = key.replace('_HOST', '');
        return {
          host: process.env[`${prefix}_HOST`],
          port: process.env[`${prefix}_PORT`],
          database: process.env[`${prefix}_DB`],
          username: process.env[`${prefix}_USER`],
          password: process.env[`${prefix}_PASSWORD`],
        };
      });
  }

  async connect() {
    throw new Error('connect method must be implemented by subclasses');
  }

  decorate() {
    throw new Error('decorate method must be implemented by subclasses');
  }
}

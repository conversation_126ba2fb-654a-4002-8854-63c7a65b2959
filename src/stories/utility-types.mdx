import { Meta } from '@storybook/addon-docs/blocks';

<Meta title="Documentation/TypeScript/Utility Types" />

# TypeScript Utility Types Guide

TypeScript provides several utility types to help with common type
transformations. Here's a comprehensive guide on when and how to use them.

## Common Utility Types

### `Partial<T>`

Makes all properties in a type optional.

```typescript
interface User {
  name: string;
  age: number;
  email: string;
}

// All fields become optional
type PartialUser = Partial<User>;

const user: PartialUser = {
  name: '<PERSON>', // Valid! age and email can be omitted
};
```

**Use when:**

- Creating form state that starts empty
- Defining update operations where not all fields are required
- Creating configuration objects with optional settings

### `Omit<T, K>`

Removes specific properties from a type.

```typescript
interface ButtonProps {
  onClick: () => void;
  type: 'submit' | 'button';
  children: React.ReactNode;
}

// Removes 'type' property
type CustomButtonProps = Omit<ButtonProps, 'type'> & {
  variant: 'primary' | 'secondary';
};
```

**Use when:**

- Extending a base type but wanting to replace certain properties
- Removing properties that don't make sense for your use case
- Avoiding prop conflicts in component composition

### `Pick<T, K>`

Selects specific properties from a type.

```typescript
interface User {
  id: string;
  name: string;
  age: number;
  email: string;
  password: string;
}

// Only includes id and name
type PublicUser = Pick<User, 'id' | 'name'>;
```

**Use when:**

- Creating a subset of a larger type
- Defining public-facing interfaces
- Working with specific fields in forms

### `Required<T>`

Makes all properties in a type required.

```typescript
interface Config {
  cache?: boolean;
  timeout?: number;
  debug?: boolean;
}

// All fields become required
type RequiredConfig = Required<Config>;
```

**Use when:**

- Ensuring all properties are provided
- Converting optional configurations to required ones
- Validating complete objects

### `Record<K, T>`

Creates a type with properties of type `K` and values of type `T`.

```typescript
type UserRoles = Record<string, string[]>;

const roles: UserRoles = {
  admin: ['read', 'write', 'delete'],
  user: ['read'],
};
```

**Use when:**

- Creating key-value mappings
- Defining dictionaries or lookup tables
- Working with dynamic property names

## Advanced Combinations

### Combining Utility Types

You can combine utility types for more complex transformations:

```typescript
interface User {
  id: string;
  name: string;
  email: string;
  password: string;
}

// Makes all properties optional EXCEPT 'id'
type UpdateableUser = Partial<Omit<User, 'id'>> & { id: string };

// Only picks certain fields and makes them optional
type OptionalUserContact = Partial<Pick<User, 'email' | 'phone'>>;
```

### Custom Utility Types

You can create your own utility types:

```typescript
// Makes specific properties nullable
type Nullable<T, K extends keyof T> = Omit<T, K> & {
  [P in K]: T[P] | null;
};

interface User {
  name: string;
  age: number;
}

// Makes 'age' nullable
type UserWithNullableAge = Nullable<User, 'age'>;
```

## Best Practices

1. **Type Naming**
   - Use descriptive names that indicate the purpose
   - Follow PascalCase convention
   - Consider adding suffixes like `Props`, `State`, `Config`

```typescript
type UserFormState = Partial<User>;
type TableProps = Omit<BaseTableProps, 'onSort'>;
```

2. **Composition Over Modification**
   - Prefer composing types over modifying existing ones
   - Use intersection (&) to add properties
   - Use unions (|) for variant types

```typescript
// Good
type CustomButtonProps = Omit<ButtonProps, 'size'> & {
  customSize: 'xl' | '2xl';
};

// Avoid
interface CustomButtonProps extends ButtonProps {
  size: 'xl' | '2xl'; // Conflicts with ButtonProps
}
```

3. **Documentation**
   - Add JSDoc comments for complex type transformations
   - Explain the purpose and constraints of utility types
   - Include examples for non-obvious use cases

```typescript
/**
 * Represents a user with optional contact information
 * but required identification fields.
 */
type PartialUserContact = Required<Pick<User, 'id' | 'name'>> &
  Partial<Pick<User, 'email' | 'phone'>>;
```

## Common Gotchas

1. **Nested Objects**
   - `Partial` only works one level deep
   - Use recursive types for deep partial objects

```typescript
type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};
```

2. **Union Types**
   - Be careful with unions when using utility types
   - Consider the impact on each union member

```typescript
type StringOrNumber = string | number;
type NullableStringOrNumber = StringOrNumber | null; // Better than Partial<StringOrNumber>
```

3. **Generic Constraints**
   - Use constraints to ensure type safety
   - Restrict generic parameters when needed

```typescript
function pick<T, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> {
  // Implementation
}
```

## Additional Resources

- [TypeScript Handbook - Utility Types](https://www.typescriptlang.org/docs/handbook/utility-types.html)
- [TypeScript Deep Dive](https://basarat.gitbook.io/typescript/)
- [TypeScript Release Notes](https://www.typescriptlang.org/docs/handbook/release-notes/overview.html)

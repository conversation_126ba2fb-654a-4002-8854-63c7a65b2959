import { Meta } from '@storybook/addon-docs/blocks';

<Meta title="Documentation/Architecture/Project Structure" />

# Frontend Project Structure Guidelines

This document outlines the architecture and organization patterns used in our
Next.js frontend application. Following these guidelines ensures consistency
across the codebase and makes it easier for new developers to understand and
contribute to the project.

## Directory Structure

Our frontend project follows a modular architecture with clear separation of
concerns:

```
project-root
├── config
├── coverage
│   └── ... (test coverage reports)
├── lcov-report
│   └── ... (HTML coverage report)
├── public
│   └── placeholders
│       └── logo
├── scripts
├── src
│   ├── app
│   │   ├── (auth)
│   │   │   ├── login
│   │   │   └── reset-password
│   │   ├── [accessId]
│   │   │   ├── (example)
│   │   │   │   └── user
│   │   │   └── dashboard
│   │   └── api
│   ├── components
│   │   ├── application-ui
│   │   │   ├── app-header
│   │   │   ├── app-sidebar
│   │   │   ├── filter-section
│   │   │   ├── layouts
│   │   │   └── shells
│   │   ├── base
│   │   │   ├── data-display
│   │   │   ├── feedback
│   │   │   ├── headings
│   │   │   ├── inputs
│   │   │   ├── layouts
│   │   │   ├── navigation
│   │   │   ├── surface
│   │   │   └── typography
│   │   └── features
│   │       ├── auth
│   │       └── user
│   ├── constants
│   ├── contexts
│   ├── hocs
│   ├── hooks
│   ├── i18n
│   ├── layouts
│   ├── mocks
│   ├── router
│   ├── schemas
│   ├── services
│   ├── store
│   ├── stories
│   ├── theme
│   ├── types
│   └── utils
└── ...other root-level folders/files (README, package.json, etc.)
```

# Summary: Project Structure & Usage

## Top-Level Directories

- **config**  
  App configuration files (e.g., environment, runtime, build configs).

- **coverage**  
  Automatically generated test coverage reports.

- **lcov-report**  
  HTML code coverage reports for browser viewing.

- **public**  
  Static assets served at the root URL (e.g., images, logos, icons).

- **scripts**  
  Utility scripts for development, build, migration, etc.

- **src**  
  Main application source code — where the core of your Next.js app lives.

## Inside `src` Directory

### 1. app

- Next.js App Router folder structure
- Contains route segments, dynamic routes (e.g., `[accessId]`), and special
  segments (e.g., `(auth)`)
- Each segment can have its own pages/components for authentication, dashboards,
  APIs, etc.

### 2. components

- **application-ui**  
  Large, reusable UI building blocks (headers, sidebars, layouts, shells).

- **base**  
  Foundational UI components (avatars, badges, datagrids, labels, scrollbars,
  feedback, headings, buttons, navigation, accordion, typography).  
  Includes support for variants and styles, promoting reusability and
  consistency.

- **features**  
  Domain-specific feature sets (e.g., auth forms/guards, user
  listing/filtering).

### 3. constants

Shared constant values (e.g., enums, config constants, key mappings).

### 4. contexts

React Contexts for app-wide state management (e.g., authentication,
customisation, sidebar state).

### 5. hocs

Higher-Order Components for advanced React patterns (wrapping/augmenting
components).

### 6. hooks

Custom React hooks, grouped by domain (auth, data, lifecycle, navigation, UI).

### 7. i18n

Internationalisation setup and translation files.

### 8. layouts

Layout components and wrappers for pages or UI sections (e.g., custom document
structure).

### 9. mocks

Mock data or modules for development and testing.

### 10. router

Custom routing logic or helpers (if not purely using Next.js routing
conventions).

### 11. schemas

Schema definitions (likely for forms or validation, e.g., using Zod or Yup).

### 12. services

Business/domain logic, API calls, and data fetching for auth and users.

### 13. store

State management logic (could be Redux, Zustand, etc.).

### 14. stories

Storybook stories for components — UI previews and documentation.

### 15. theme

Theming logic and configuration (base/dark/light themes).

### 16. types

TypeScript type definitions and shared interfaces.

### 17. utils

Shared utility functions/helpers.

## Component Categories

Components are organized into three main categories based on their purpose and
reusability:

### 1. Base Components (`src/components/base/`)

Base components are the foundation of our UI system. They are highly reusable,
generic components that can be used across the entire application.

**Examples**: Button, Typography, Avatar, Dialog, Label

**Organization**: Grouped by function (data-display, inputs, feedback, etc.)

### 2. Application UI Components (`src/components/application-ui/`)

Application UI components are specific to our application's UI and combine base
components to create more complex UI elements.

**Examples**: HeaderUserDropdown, MerchantSwitcher, FilterSection

**Organization**: Grouped by application area (app-header, app-sidebar,
filter-section, etc.)

### 3. Feature Components (`src/components/features/`)

Feature components are tied to specific business features or domains and often
combine application UI components.

**Examples**: LoginForm, UserDataGrid, AuthGuard

**Organization**: Grouped by business domain (auth, user, etc.)

## Standard Component Structure

All components across all categories follow this consistent structure:

```
components/category/component-name/
├── ComponentName.tsx           # Main component implementation
├── ComponentName.test.tsx      # Unit tests
├── ComponentName.type.ts       # TypeScript type definitions
├── ComponentName.stories.tsx   # Storybook stories
├── ComponentName.constant.ts   # Constants and default values
├── ComponentName.utils.ts      # Component-specific utilities
├── index.ts                    # Public exports
├── parts/                      # Component parts/subcomponents
│   ├── ComponentPart.tsx
│   └── index.ts
├── styles/                     # Styled components
│   ├── ComponentName.style.ts
│   └── ComponentPart.style.ts
└── variants/                   # Component variants
    ├── component-variant-a/
    │   ├── ComponentVariantA.tsx
    │   ├── ComponentVariantA.stories.tsx
    │   ├── ComponentVariantA.test.ts
    │   └── ComponentVariantA.type.ts
    └── component-variant-b/
        ├── ComponentVariantB.tsx
        ├── ComponentVariantB.stories.tsx
        ├── ComponentVariantB.test.ts
        └── ComponentVariantB.type.ts
```

## Services Structure

Services handle API communication and are organized by domain:

```
services/
├── auth/                   # Authentication services
│   ├── auth.service.ts      # Service implementation
│   ├── auth.type.ts         # Service types
│   └── index.ts             # Service exports
└── users/                  # User management services
    ├── user.service.ts      # Service implementation
    ├── user.type.ts         # Service types
    └── index.ts             # Service exports
```

## Hooks Structure

Hooks are organized by their purpose:

```
hooks/
├── auth/                   # Authentication hooks
├── data/                   # Data management hooks
├── lifecycle/              # Component lifecycle hooks
├── navigation/             # Navigation hooks
├── ui/                     # UI-related hooks
└── index.ts                # Hook exports
```

## Contexts Structure

Contexts provide global state management:

```
contexts/
├── auth/                   # Authentication context
├── customization/          # Theme customization context
├── sidebar/                # Sidebar context
└── index.ts                # Context exports
```

## Types Structure

Types are organized in a dedicated directory:

```
types/
├── api-responses.type.ts   # API response type definitions
├── global.d.ts             # Global type declarations
├── mui.d.ts                # Material UI type extensions
└── reset.d.ts              # TypeScript reset types
```

## Best Practices

### Component Organization

1. **Component Categorization**: Categorize components based on their purpose
   and reusability.

   - Base components: Generic, highly reusable
   - Application UI components: Application-specific UI elements
   - Feature components: Business domain-specific components

2. **Component Structure**: Follow the standard component structure for all
   components.

   - Separate types, styles, and component logic
   - Use index.ts files for clean exports
   - Organize variants in subfolders

3. **Component Naming**: Use clear, descriptive names.
   - Base components: Generic names (Button, Typography)
   - Application UI components: Specific names (HeaderUserDropdown)
   - Feature components: Domain-specific names (UserDataGrid)

### Code Organization

1. **Single Responsibility**: Each file should have a single responsibility.
2. **Consistent Imports**: Use consistent import patterns.
3. **Path Aliases**: Use path aliases for cleaner imports.
4. **Type Safety**: Use TypeScript types and interfaces consistently.
5. **API Response Handling**: Follow the standard API response pattern.

### Documentation

1. **JSDoc Comments**: Use JSDoc comments for components, props, and functions.
2. **Storybook Stories**: Create Storybook stories for all components.
3. **MDX Documentation**: Use MDX files for comprehensive documentation.

import { Meta } from '@storybook/addon-docs/blocks';

<Meta title="Documentation/Storybook/Writing Component Stories" />

# Component Documentation Guidelines

This guide explains how to write components and stories that generate clear,
useful auto-documentation.

## Component Structure

### Props Interface

Always define a clear props interface with JSDoc comments:

```typescript
interface ButtonProps {
  /** The variant style of the button */
  variant?: 'contained' | 'outlined' | 'text';

  /** The size of the button */
  size?: 'small' | 'medium' | 'large';

  /** The content to be rendered inside the button */
  children: React.ReactNode;

  /** Callback fired when the button is clicked */
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;

  /** Whether the button is in a disabled state */
  disabled?: boolean;
}
```

### Component Definition

Use JSDoc to document the component itself:

````typescript
/**
 * A reusable button component that supports different variants and sizes.
 *
 * @example
 * ```tsx
 * <Button variant="contained" size="large" onClick={handleClick}>
 *   Click Me
 * </Button>
 * ```
 */
export const Button: React.FC<ButtonProps> = ({
  variant = 'contained',
  size = 'medium',
  children,
  onClick,
  disabled = false,
}) => {
  return (
    <button
      className={`button ${variant} ${size}`}
      onClick={onClick}
      disabled={disabled}
    >
      {children}
    </button>
  );
};
````

## Component Story Format (CSF)

Reference: [Storybook CSF](https://storybook.js.org/docs/api/csf)

### Default Export - Metadata Configuration

```typescript
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { Button } from './Button';

const meta = {
  title: 'Components/Button',
  component: Button,
  parameters: {
    layout: 'centered',
    // Component description that appears in docs
    componentSubtitle: 'A versatile button component for user interactions',
    docs: {
      description: {
        component: `
          The Button component is used to trigger actions or events.
          It supports different variants and sizes to match your design needs.
        `,
      },
    },
  },
  // Define how controls appear in docs
  argTypes: {
    variant: {
      control: 'select',
      options: ['contained', 'outlined', 'text'],
      description: 'The visual style of the button',
      table: {
        defaultValue: { summary: 'contained' },
        type: { summary: 'string' },
      },
    },
    size: {
      control: 'radio',
      options: ['small', 'medium', 'large'],
      description: 'The size of the button',
    },
    onClick: {
      action: 'clicked',
      description: 'Function called when button is clicked',
    },
  },
} satisfies Meta<typeof Button>;

export default meta;
```

### Named Story Exports

Every named export in the file represents a story object by default.

```typescript
type Story = StoryObj<typeof meta>;

// Base story with default props
export const Default: Story = {
  args: {
    children: 'Button',
    variant: 'contained',
  },
};

// Story with description and custom args
export const Primary: Story = {
  args: {
    variant: 'contained',
    children: 'Primary Button',
  },
  parameters: {
    docs: {
      description: {
        story: 'The primary button style, used for main actions.',
      },
    },
  },
};

// Story demonstrating different states
export const States: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '1rem' }}>
      <Button variant="contained">Normal</Button>
      <Button variant="contained" disabled>Disabled</Button>
      <Button variant="contained" size="small">Small</Button>
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Different states and sizes of the button.',
      },
    },
  },
};
```

## Best Practices

### Component Documentation

- **Clear Props Interface**

  ```typescript
  interface TableProps<T> {
    /** The data to display in the table */
    data: T[];

    /** The columns configuration */
    columns: {
      /** Unique identifier for the column */
      id: string;
      /** Header text */
      header: string;
      /** Function to render cell content */
      render: (row: T) => React.ReactNode;
    }[];

    /** Called when a row is selected */
    onRowSelect?: (row: T) => void;
  }
  ```

- **Default Props**
  ```typescript
  const defaultProps = {
    pageSize: 10,
    showHeader: true,
  } as const;
  ```

### Story Organization

- **Group Related Stories**

  ```typescript
  // Button.stories.tsx
  export const Variants: Story = {
    render: () => (
      <div style={{ display: 'flex', gap: '1rem' }}>
        <Button variant="contained">Contained</Button>
        <Button variant="outlined">Outlined</Button>
        <Button variant="text">Text</Button>
      </div>
    ),
  };
  ```

- **Interactive Examples**
  ```typescript
  export const Interactive: Story = {
    args: {
      children: 'Interactive Button',
      variant: 'contained',
    },
    argTypes: {
      onClick: { action: 'clicked' },
    },
  };
  ```

### Testing Integration

```typescript
export const WithControls: Story = {
  args: {
    children: 'Test Button',
    variant: 'contained',
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const button = canvas.getByRole('button');
    await userEvent.click(button);
  },
};
```

## Additional Tips

1. **Use TypeScript Template Literal Types**

```typescript
type Color = 'red' | 'blue' | 'green';
type Size = 'small' | 'large';
type Variant = `${Color}-${Size}`; // 'red-small' | 'red-large' | etc.
```

2. **Document Event Handlers**

```typescript
interface TableProps<T> {
  /** Called when a row is clicked
   * @param row - The row data
   * @param index - The row index
   */
  onRowClick?: (row: T, index: number) => void;
}
```

3. **Use Discriminated Unions**

```typescript
type LoadingState = {
  status: 'loading';
};

type SuccessState = {
  status: 'success';
  data: string[];
};

type ErrorState = {
  status: 'error';
  error: Error;
};

type State = LoadingState | SuccessState | ErrorState;
```

## Resources

- [Storybook Documentation](https://storybook.js.org/docs/writing-docs/autodocs)
- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [React TypeScript Cheatsheet](https://react-typescript-cheatsheet.netlify.app/)

import { Typography } from '@/components/base/typography';
import { Meta } from '@storybook/addon-docs/blocks';

<Meta title="Documentation/Utils/Text Transformation" />

# Text Transformation with `change-case`

This documentation explains how to use the "change-case" library in your project
for text transformations.

## Available Transformations

The `change-case` library provides the following transformations:

<table
  style={{ width: '100%', borderCollapse: 'collapse', marginBottom: '24px' }}
>
  <thead>
    <tr style={{ borderBottom: '2px solid #ddd' }}>
      <th style={{ padding: '8px', textAlign: 'left' }}>Method</th>
      <th style={{ padding: '8px', textAlign: 'left' }}>Result</th>
      <th style={{ padding: '8px', textAlign: 'left' }}>Example Input</th>
      <th style={{ padding: '8px', textAlign: 'left' }}>Example Output</th>
    </tr>
  </thead>
  <tbody>
    <tr style={{ borderBottom: '1px solid #ddd' }}>
      <td style={{ padding: '8px' }}>
        <code>camelCase</code>
      </td>
      <td style={{ padding: '8px' }}>
        <code>twoWords</code>
      </td>
      <td style={{ padding: '8px' }}>"Hello World"</td>
      <td style={{ padding: '8px' }}>"helloWorld"</td>
    </tr>
    <tr style={{ borderBottom: '1px solid #ddd' }}>
      <td style={{ padding: '8px' }}>
        <code>capitalCase</code>
      </td>
      <td style={{ padding: '8px' }}>
        <code>Two Words</code>
      </td>
      <td style={{ padding: '8px' }}>"hello world"</td>
      <td style={{ padding: '8px' }}>"Hello World"</td>
    </tr>
    <tr style={{ borderBottom: '1px solid #ddd' }}>
      <td style={{ padding: '8px' }}>
        <code>constantCase</code>
      </td>
      <td style={{ padding: '8px' }}>
        <code>TWO_WORDS</code>
      </td>
      <td style={{ padding: '8px' }}>"Hello World"</td>
      <td style={{ padding: '8px' }}>"HELLO_WORLD"</td>
    </tr>
    <tr style={{ borderBottom: '1px solid #ddd' }}>
      <td style={{ padding: '8px' }}>
        <code>dotCase</code>
      </td>
      <td style={{ padding: '8px' }}>
        <code>two.words</code>
      </td>
      <td style={{ padding: '8px' }}>"Hello World"</td>
      <td style={{ padding: '8px' }}>"hello.world"</td>
    </tr>
    <tr style={{ borderBottom: '1px solid #ddd' }}>
      <td style={{ padding: '8px' }}>
        <code>kebabCase</code>
      </td>
      <td style={{ padding: '8px' }}>
        <code>two-words</code>
      </td>
      <td style={{ padding: '8px' }}>"Hello World"</td>
      <td style={{ padding: '8px' }}>"hello-world"</td>
    </tr>
    <tr style={{ borderBottom: '1px solid #ddd' }}>
      <td style={{ padding: '8px' }}>
        <code>lowercase</code>
      </td>
      <td style={{ padding: '8px' }}>
        <code>two words</code>
      </td>
      <td style={{ padding: '8px' }}>"Hello World"</td>
      <td style={{ padding: '8px' }}>"hello world"</td>
    </tr>
    <tr style={{ borderBottom: '1px solid #ddd' }}>
      <td style={{ padding: '8px' }}>
        <code>noCase</code>
      </td>
      <td style={{ padding: '8px' }}>
        <code>two words</code>
      </td>
      <td style={{ padding: '8px' }}>"HelloWorld"</td>
      <td style={{ padding: '8px' }}>"hello world"</td>
    </tr>
    <tr style={{ borderBottom: '1px solid #ddd' }}>
      <td style={{ padding: '8px' }}>
        <code>none</code>
      </td>
      <td style={{ padding: '8px' }}>
        <code>Hello World</code>
      </td>
      <td style={{ padding: '8px' }}>"Hello World"</td>
      <td style={{ padding: '8px' }}>"Hello World"</td>
    </tr>
    <tr style={{ borderBottom: '1px solid #ddd' }}>
      <td style={{ padding: '8px' }}>
        <code>pascalCase</code>
      </td>
      <td style={{ padding: '8px' }}>
        <code>TwoWords</code>
      </td>
      <td style={{ padding: '8px' }}>"hello world"</td>
      <td style={{ padding: '8px' }}>"HelloWorld"</td>
    </tr>
    <tr style={{ borderBottom: '1px solid #ddd' }}>
      <td style={{ padding: '8px' }}>
        <code>pascalSnakeCase</code>
      </td>
      <td style={{ padding: '8px' }}>
        <code>Two_Words</code>
      </td>
      <td style={{ padding: '8px' }}>"hello world"</td>
      <td style={{ padding: '8px' }}>"Hello_World"</td>
    </tr>
    <tr style={{ borderBottom: '1px solid #ddd' }}>
      <td style={{ padding: '8px' }}>
        <code>pathCase</code>
      </td>
      <td style={{ padding: '8px' }}>
        <code>two/words</code>
      </td>
      <td style={{ padding: '8px' }}>"Hello World"</td>
      <td style={{ padding: '8px' }}>"hello/world"</td>
    </tr>
    <tr style={{ borderBottom: '1px solid #ddd' }}>
      <td style={{ padding: '8px' }}>
        <code>sentenceCase</code>
      </td>
      <td style={{ padding: '8px' }}>
        <code>Two words</code>
      </td>
      <td style={{ padding: '8px' }}>"helloWorld"</td>
      <td style={{ padding: '8px' }}>"Hello world"</td>
    </tr>
    <tr style={{ borderBottom: '1px solid #ddd' }}>
      <td style={{ padding: '8px' }}>
        <code>snakeCase</code>
      </td>
      <td style={{ padding: '8px' }}>
        <code>two_words</code>
      </td>
      <td style={{ padding: '8px' }}>"Hello World"</td>
      <td style={{ padding: '8px' }}>"hello_world"</td>
    </tr>
    <tr style={{ borderBottom: '1px solid #ddd' }}>
      <td style={{ padding: '8px' }}>
        <code>trainCase</code>
      </td>
      <td style={{ padding: '8px' }}>
        <code>Two-Words</code>
      </td>
      <td style={{ padding: '8px' }}>"hello world"</td>
      <td style={{ padding: '8px' }}>"Hello-World"</td>
    </tr>
    <tr style={{ borderBottom: '1px solid #ddd' }}>
      <td style={{ padding: '8px' }}>
        <code>uppercase</code>
      </td>
      <td style={{ padding: '8px' }}>
        <code>TWO WORDS</code>
      </td>
      <td style={{ padding: '8px' }}>"Hello World"</td>
      <td style={{ padding: '8px' }}>"HELLO WORLD"</td>
    </tr>
  </tbody>
</table>

## Usage Options

### 1. Custom Typography Component

Use our custom Typography component that extends MUI's Typography with
change-case capabilities:

```tsx
import { Typography } from '@/components/base/typography';

// In your component
<Typography caseTransform="pascalCase">hello world</Typography> // Renders as "HelloWorld"
<Typography caseTransform="kebabCase">Hello World</Typography> // Renders as "hello-world"

// Using MUI Typography's textTransform
<Typography textTransform="uppercase">Hello World</Typography> // Renders as "HELLO WORLD"
```

### 2. Direct Utility Function

Use the utility function directly:

```tsx
import transformText from '@/utils/text-transform';

// In your component
const MyComponent = () => {
  const originalText = 'Hello World';
  const transformedText = transformText(originalText, 'constantCase');

  return <div>{transformedText}</div>; // Renders as "HELLO_WORLD"
};
```

## Integration with MUI Theme

While MUI's `textTransform` property is limited to 'uppercase', 'lowercase',
'capitalize', or 'none', you can use our custom Typography component or the
utility functions to achieve more complex transformations.

For components that need to display text with specific transformations, consider
using the `transformText` utility function or the custom Typography component.

## When to Use Text Transformations

- **User Interface**: Ensure consistent text styling across your application
- **Data Formatting**: Format data from APIs or user input in a consistent way
- **URL Generation**: Create URL-friendly strings using `kebabCase` or
  `pathCase`
- **Variable Naming**: Convert between different naming conventions in code
  generation
- **Display Names**: Format user names or titles consistently

## Best Practices

1. **Consistency**: Choose a transformation style and use it consistently
   throughout your application
2. **Accessibility**: Ensure transformed text remains readable and accessible
3. **Performance**: For large text transformations, consider memoizing the
   results
4. **Internationalization**: Be aware that some transformations may not work as
   expected with non-Latin characters

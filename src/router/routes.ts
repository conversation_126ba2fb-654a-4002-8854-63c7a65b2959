/**
 * Application routes
 *
 * This file contains all the routes used in the application.
 * Routes are organized by section for better maintainability.
 */

const ROUTES = {
  INDEX: '/',
  AUTH: {
    LOGIN: '/login',
    // eslint-disable-next-line sonarjs/no-hardcoded-passwords
    RESET_PASSWORD: '/reset-password',
  },
  DASHBOARD: '/[accessId]/dashboard',
  USER_MANAGEMENT: {
    USER: '/[accessId]/user',
  },
  NOT_FOUND: '/404',
} as const;

export default ROUTES;

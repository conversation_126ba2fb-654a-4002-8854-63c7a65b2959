import type { ChangeCaseTransform } from '@/components/base/typography';
import { type ReactNode } from 'react';
import ROUTES from './routes';

/**
 * Menu item structure for the sidebar navigation
 */
export interface MenuItem {
  /**
   * The title of the menu item
   */
  title: string;

  /**
   * The path to navigate to when the menu item is clicked
   */
  path?: string;

  /**
   * The icon to display next to the menu item
   */
  icon?: ReactNode;

  /**
   * Whether the menu item is a header (section title)
   */
  isHeader?: boolean;

  /**
   * Submenu items
   */
  items?: MenuItem[];

  /**
   * Text transformation for the menu item title
   */
  caseTransform?: ChangeCaseTransform;
}

// Raw menu data structure, can be replaced with DB data in the future
const rawMenuData: MenuItem[] = [
  {
    title: 'common.label.general',
    isHeader: true,
    items: [{ title: 'common.label.dashboard', path: ROUTES.DASHBOARD }],
  },
  {
    title: 'common.label.userManagement',
    isHeader: true,
    items: [
      { title: 'common.label.user', path: ROUTES.USER_MANAGEMENT.USER },
      { title: 'common.label.rnp' },
      { title: 'common.label.department' },
    ],
  },
  {
    title: 'common.label.memManagement',
    isHeader: true,
    items: [
      {
        title: 'common.label.memAccManagement',
        items: [{ title: 'common.label.memberAcc' }, { title: 'common.label.memVerification' }],
      },
      {
        title: 'common.label.memSegmentation',
        items: [
          { title: 'common.label.riskGroup' },
          { title: 'common.label.vipTier', caseTransform: 'none' },
        ],
      },
    ],
  },
  {
    title: 'common.label.logNMonitoring',
    isHeader: true,
    items: [{ title: 'common.label.auditTrail' }],
  },
  {
    title: 'common.label.setting',
    isHeader: true,
    items: [
      {
        title: 'common.label.generalSetting',
        items: [{ title: 'common.label.currency' }, { title: 'common.label.theme' }],
      },
      {
        title: 'common.label.securityControl',
        items: [{ title: 'common.label.safety' }, { title: 'common.label.accessControl' }],
      },
    ],
  },
];

/**
 * Map raw menu data to translated menu items
 *
 * @param data Raw menu data
 * @param t Translation function
 * @returns Array of translated menu items
 */
const mapMenuData = (data: MenuItem[], t: (token: string) => string): MenuItem[] => {
  return data.map((section) => ({
    title: t(section.title),
    isHeader: section.isHeader,
    path: section.path ?? (section.items ? undefined : ROUTES.NOT_FOUND),
    icon: section.icon,
    caseTransform: section.caseTransform,
    items: section.items ? mapMenuData(section.items, t) : undefined,
  }));
};

/**
 * Generate menu items with translations
 *
 * This can be extended to include role-based filtering in the future.
 *
 * @param t Translation function
 * @returns Array of menu items
 */
export const getMenuItems = (t: (token: string) => string): MenuItem[] => {
  return mapMenuData(rawMenuData, t);
};

export default getMenuItems;

import cors from '@fastify/cors';
import corsConfig from '#config/cors.config.js';
import fp from 'fastify-plugin';

/**
 * This function is a Fastify plugin that configures and registers the CORS middleware.
 * It customizes the CORS settings for the Fastify application.
 *
 * @async
 * @param {FastifyInstance} fastify - The Fastify instance to which the CORS middleware is registered.
 * @param {Object} opts - Optional configuration options for the CORS middleware.
 * @returns {Promise<void>} - A promise that resolves when the CORS middleware is successfully registered.
 */
export default fp(async (fastify, opts) => {
  fastify.register(cors, {
    ...corsConfig,
    ...opts,
  });
});

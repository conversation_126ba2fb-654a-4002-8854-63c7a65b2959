import { fastifyRedis } from '@fastify/redis';
import fp from 'fastify-plugin';

export default fp(async (fastify) => {
  try {
    await fastify.register(fastifyRedis, {
      host: fastify.config.REDIS_HOST,
      port: fastify.config.REDIS_PORT,
      db: 2, // Use the second database for caching
    });
    fastify.log.info('Redis connection established');
  } catch (error) {
    fastify.log.error(error, 'Failed to initialize Redis plugin:');
    throw error;
  }
});

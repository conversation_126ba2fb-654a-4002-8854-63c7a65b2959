import { formatErrorResponse } from '#src/utils/response.util.js';
import fp from 'fastify-plugin';
import { translateValidationErrors } from '#src/utils/i18next.util.js';

/**
 * This plugin sets up custom error handlers for various scenarios including
 * 404 Not Found errors, general error handling, unhandled rejections, and
 * uncaught exceptions.
 *
 * @async
 * @param {FastifyInstance} fastify - The Fastify instance to configure
 * @param {Object} opts - Options for the plugin (not used in this implementation)
 * @returns {Promise<void>} A promise that resolves when the error handlers are set up
 */
const errorHandlerPlugin = async (fastify, opts) => {
  const handleError = (error, request, reply) => {
    const statusCode = error.statusCode || 500;
    const errorCode = error.code || 'INTERNAL_SERVER_ERROR';

    let message = error.message;
    if (statusCode === 500) {
      if (fastify.config.NODE_ENV !== 'development') {
        message = fastify.t('error.sentence.internal');
      }
    } else if (typeof error.tMessageFn === 'function') {
      message = error.tMessageFn(fastify);
    }

    let metaData = error.metaData || {};
    if (error.validation) {
      message = fastify.t('error.label.validation');
      metaData.details = translateValidationErrors(error.validation);
    }
    const response = formatErrorResponse(message, errorCode, metaData);
    return reply.status(statusCode).send(response);
  };

  fastify.setNotFoundHandler((request, reply) =>
    handleError(
      { statusCode: 404, message: fastify.t('error.sentence.itemNotFound') },
      request,
      reply,
    ),
  );

  fastify.setErrorHandler(handleError);
};

export default fp(errorHandlerPlugin, {
  name: 'errorHandler',
  dependencies: ['logger', 'gracefulShutdown', 'sensible'],
});

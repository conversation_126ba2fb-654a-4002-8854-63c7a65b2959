import fp from 'fastify-plugin';
import rateLimit from '@fastify/rate-limit';
import rateLimitConfig from '#config/rate-limit.config.js';

const {
  rateLimitGroups,
  groupPrefixes,
  whitelist,
  addHeadersOnExceeding,
  addHeaders,
  errorResponseBuilder,
  skipOnError,
  nameSpace,
  redis,
  cache,
  hook,
} = rateLimitConfig;

/**
 * Determines the rate limit group for a request based on its URL.
 *
 * @param {Object} request - The request object.
 * @param {string} request.url - The request URL.
 * @returns {Object} The applicable rate limit group.
 */
const findRateLimitGroup = (request) => {
  const group = Object.entries(groupPrefixes).find(([_, urls]) =>
    urls.some((url) => request.url.startsWith(url)),
  );
  return group ? rateLimitGroups[group[0]] : rateLimitGroups.default;
};

/**
 * A Fastify plugin that sets up rate limiting for different route groups.
 * It registers a global rate limit and applies specific rate limits to defined route groups.
 *
 * @param {FastifyInstance} fastify - The Fastify instance.
 * @param {Object} opts - Options for configuring the rate limit plugin.
 * @returns {Promise<void>} A promise that resolves when the plugin is registered.
 */
export default fp(async (fastify, opts) => {
  fastify.register(rateLimit, {
    max: (request) => findRateLimitGroup(request).max,
    timeWindow: (request) => findRateLimitGroup(request).timeWindow,
    keyGenerator: (request) => `${request.ip}:${request.url}`,
    allowList: (request) => whitelist.includes(request.ip),
    addHeadersOnExceeding,
    addHeaders,
    errorResponseBuilder,
    skipOnError,
    nameSpace,
    redis,
    cache,
    hook,
    ...opts,
  });
});

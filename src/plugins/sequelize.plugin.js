import { DatabaseFactory } from '#src/factories/database.factory.js';
import { Sequelize } from 'sequelize';
import _ from 'lodash';
import sequelizeConfig from '#config/sequelize.config.js';

export class SequelizePlugin extends DatabaseFactory {
  async connect() {
    let config = _.cloneDeep(sequelizeConfig);
    const readReplicas = this.getReadReplicas();
    if (readReplicas.length > 0) {
      config.replication.read = readReplicas;
    }

    this.sequelize = new Sequelize(config);
    await this.sequelize.authenticate();
    this.fastify.log.info('Database connection established');
  }
  decorate() {
    this.fastify.decorate('psql', {
      connection: this.sequelize,
    });
  }
}

export default SequelizePlugin.createPlugin('postgres', true);

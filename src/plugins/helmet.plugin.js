import fp from 'fastify-plugin';
import helmet from '@fastify/helmet';
import helmetConfig from '#config/helmet.config.js';

/**
 * Configures and registers the Helmet security middleware for a Fastify application.
 * This plugin sets up various HTTP headers to enhance the security of the application.
 *
 * @async
 * @param {FastifyInstance} fastify - The Fastify instance to register the plugin with.
 * @param {Object} opts - Additional options to customize Helmet's behavior.
 * @returns {Promise<void>} A promise that resolves when the plugin has been registered.
 */
export default fp(async (fastify, opts) => {
  fastify.register(helmet, {
    ...helmetConfig,
    ...opts,
  });
});

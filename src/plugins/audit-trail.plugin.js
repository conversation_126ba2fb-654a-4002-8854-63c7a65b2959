import { EVENT_ACTIONS, VIEW_ACTION } from '#src/modules/core/constants/core.constant.js';
import _ from 'lodash';
import { buildAuditTrailTarget } from '#src/utils/audit-trail.util.js';
import fp from 'fastify-plugin';

async function auditTrailPlugin(fastify) {
  fastify.decorate('withAuditLogging', withAuditLogging);
}

/**
 * A function to handle audit logging for CRUD operations. It generates audit trail entries based on the provided parameters.
 *
 * @param {Object} options - An object containing the necessary parameters for audit logging.
 * @param {Object} options.request - The request object containing audit trail entries.
 * @param {Object} options.modelMapping - Maps model names to their associated audit information:
 *   {
 *     ModelName: {
 *       fieldsChanged: Array<string> | false,
 *       beforeState: Object,
 *       afterState: Object,
 *       referenceDetails: Object
 *     },
 *     ...
 *   }
 * @param {boolean} [options.isMultiple=false] - Indicates whether the operation involves multiple records.
 * @param {Object} [options.metrics={}] - Additional metrics related to the operation.
 * @param {number} [options.status=null] - The HTTP status code associated with the operation.
 *
 * @returns {Promise<Array>} - A promise that resolves to an array of target objects, each containing the model name, reference ID, and changes made.
 */
const withAuditLogging = async ({
  request,
  modelMapping = {},
  isMultiple = false,
  metrics = {},
  status = null,
}) => {
  const action =
    request.auditEntries?.action.translationKey ??
    `auditTrail.action.${EVENT_ACTIONS.UNKNOWN_ACTION}`;

  if (
    _.isEmpty(modelMapping) &&
    !Object.values(VIEW_ACTION).some((viewAction) => action.includes(viewAction))
  ) {
    request.server.log.warn('withAuditLogging called without modelMapping for non-view actions.');

    return;
  }

  buildAuditTrailTarget(request, {
    modelMapping,
    action,
    isMultiple,
    metrics,
    status,
  });
};

export default fp(auditTrailPlugin, {
  name: 'auditTrail',
});

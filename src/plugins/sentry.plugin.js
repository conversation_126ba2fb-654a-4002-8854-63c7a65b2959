import { captureException, profiler, init as sentryInit } from '@sentry/node';

import fp from 'fastify-plugin';

import { nodeProfilingIntegration } from '@sentry/profiling-node';

const sentryPlugin = async (fastify, opts) => {
  // Initialize Sentry only in production
  const isSentryEnabled = fastify.config.NODE_ENV === 'production';

  if (isSentryEnabled) {
    sentryInit({
      dsn: fastify.config.SENTRY_DSN,
      environment: fastify.config.NODE_ENV,
      integrations: [nodeProfilingIntegration()],
      tracesSampleRate: 1.0,
    });

    profiler.startProfiler();
    fastify.log.info('Sentry initialized');
  } else {
    fastify.log.info('Sentry not initialized (non-production environment)');
  }
  // Decorate fastify instance with Sentry
  const sentry = {
    captureException: (error, request) => {
      if (isSentryEnabled) {
        captureException(error);
      }
    },
  };

  fastify.decorate('sentry', sentry);
};

export default fp(sentryPlugin, { name: 'sentry' });

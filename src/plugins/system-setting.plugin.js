import { LocalisationRepository } from '#src/modules/setting/repository/index.js';
import fp from 'fastify-plugin';

/**
 * Loaders for system settings. Each loader is a function that returns a promise
 * which resolves to an object containing the setting(s) it loads.
 *
 * If a loader fails, it should throw an error, which will be caught and logged
 * by the systemSettingPlugin.
 *
 * @type {Array<function(FastifyInstance): Promise<Object>>}
 */
const loaders = [
  async (fastify) => {
    const baseCurrency = await LocalisationRepository.findBaseCurrency(fastify);
    if (!baseCurrency) {
      throw new Error('Base currency not found');
    }
    return { baseCurrency };
  },
  // Future system setting loaders can be added here:
  // async (fastify) => ({ someSetting: await SomeRepo.getSomething(fastify) })
];

/**
 * A Fastify plugin to load and manage system settings.
 *
 * @param {FastifyInstance} fastify - The Fastify instance.
 * @returns {Promise<void>} A promise that resolves when all settings are loaded.
 */
const systemSettingPlugin = async (fastify) => {
  const settings = {};

  for (const loader of loaders) {
    try {
      const partial = await loader(fastify);
      Object.assign(settings, partial);
    } catch (err) {
      fastify.log.error(err, 'System setting loader failed');
      throw err;
    }
  }

  fastify.decorate('systemSetting', settings);
  fastify.log.info(`System settings initialised: ${Object.keys(settings).join(', ')}`);
};

export default fp(systemSettingPlugin, {
  name: 'systemSetting',
  dependencies: ['postgres', 'mongo'],
});

import { Kafka } from 'kafkajs';
import fp from 'fastify-plugin';

/**
 * A Fastify plugin for initializing Kafka producer and admin client.
 *
 * @param {import('fastify').FastifyInstance} fastify - The Fastify instance to register the plugin.
 * @returns {Promise<void>} - A promise that resolves when the plugin is registered.
 */
const kafkaPlugin = async (fastify) => {
  if (!fastify.config.KAFKA) {
    fastify.log.warn('Kafka configuration is missing.');
    return;
  }

  const kafka = new Kafka({
    brokers: [fastify.config.KAFKA_BROKERS],
  });

  const producer = kafka.producer();
  const admin = kafka.admin();

  // Check if the 'kafka' decorator is already registered to prevent duplicate registration
  if (fastify.hasDecorator('kafka')) {
    return;
  }

  try {
    await producer.connect();
    fastify.log.info('Kafka producer connected.');

    fastify.decorate('kafka', { producer, admin });
  } catch (error) {
    fastify.log.error(error, 'Error initializing Kafka');
  }
};

export default fp(kafkaPlugin, { name: 'kafka' });

import { beforeEach, describe, expect, it, vi } from 'vitest';
import AutoLoad from '@fastify/autoload';
import app from '#src/app.js';
import routes from '#src/routes.js';

// Mock the utils module
vi.mock('#src/utils/file.util.js', () => ({
  getHooksPath: vi.fn().mockReturnValue('/path/to/hooks'),
  getPluginsPath: vi.fn().mockReturnValue('/path/to/plugins'),
  getPackageJson: vi.fn().mockReturnValue({
    name: 'test-app',
    version: '1.0.0',
  }),
}));

// Import the mocked functions
import { getHooksPath, getPluginsPath } from '#src/utils/file.util.js';

describe('app', () => {
  let fastifyMock;
  let opts;

  beforeEach(async () => {
    vi.resetModules(); // Reset modules to clear cache

    fastifyMock = {
      register: vi.fn(),
    };
    opts = { someOption: true };

    await app(fastifyMock, opts);
  });

  it('should register hooks from the directory specified by getHooksPath', () => {
    expect(fastifyMock.register).toHaveBeenCalledWith(AutoLoad, {
      dir: getHooksPath(),
      options: { ...opts },
    });
  });

  it('should pass the correct options to the AutoLoad registration for hooks', () => {
    expect(fastifyMock.register).toHaveBeenCalledWith(AutoLoad, {
      dir: getHooksPath(),
      options: { ...opts },
    });
  });

  it('should register plugins from the directory specified by getPluginsPath', () => {
    expect(fastifyMock.register).toHaveBeenCalledWith(AutoLoad, {
      dir: getPluginsPath(),
      options: { ...opts },
    });
  });

  it('should pass the correct options to the AutoLoad registration for plugins', () => {
    expect(fastifyMock.register).toHaveBeenCalledWith(AutoLoad, {
      dir: getPluginsPath(),
      options: { ...opts },
    });
  });

  it('should register routes using the provided routes function', () => {
    expect(fastifyMock.register).toHaveBeenCalledWith(routes);
  });

  it('should call fastify.register three times in total', () => {
    expect(fastifyMock.register).toHaveBeenCalledTimes(3);
  });

  it('should not modify the opts object passed to the function', async () => {
    const originalOpts = { someOption: true };
    const optsCopy = { ...originalOpts };

    await app(fastifyMock, originalOpts);

    expect(originalOpts).toEqual(optsCopy);
  });

  it('should handle an empty options object without errors', async () => {
    const emptyOpts = {};
    await app(fastifyMock, emptyOpts);

    expect(fastifyMock.register).toHaveBeenCalledWith(AutoLoad, {
      dir: getHooksPath(),
      options: { ...emptyOpts },
    });

    expect(fastifyMock.register).toHaveBeenCalledWith(AutoLoad, {
      dir: getPluginsPath(),
      options: { ...emptyOpts },
    });
  });

  it('should handle missing options parameter gracefully', async () => {
    await app(fastifyMock);

    expect(fastifyMock.register).toHaveBeenCalledWith(AutoLoad, {
      dir: getHooksPath(),
      options: {},
    });

    expect(fastifyMock.register).toHaveBeenCalledWith(AutoLoad, {
      dir: getPluginsPath(),
      options: {},
    });

    expect(fastifyMock.register).toHaveBeenCalledWith(routes);
  });

  it('should throw an error if getPluginsPath returns an invalid path', async () => {
    const invalidPathError = new Error('Invalid path');
    getPluginsPath.mockImplementationOnce(() => {
      throw invalidPathError;
    });

    await expect(app(fastifyMock, opts)).rejects.toThrow(invalidPathError);
  });
});

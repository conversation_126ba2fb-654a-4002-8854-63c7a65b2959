import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import Fastify from 'fastify';

import preValidationHook from '#src/hooks/preValidation.hook.js';

describe('Test case for preValidation hook', () => {
  let fastify;
  let preValidationFunction;

  const mockRequest = {};
  const mockReply = {};

  beforeEach(async () => {
    fastify = Fastify();
    // Add spy to the function
    vi.spyOn(fastify, 'addHook');

    // Mock the log object
    fastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    };

    // Register the hook
    await fastify.register(preValidationHook, {});

    // Extract the function
    preValidationFunction = fastify.addHook.mock.calls.find(
      (call) => call[0] === 'preValidation',
    )[1];
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should register the preValidation hook', async () => {
    expect(fastify.addHook).toHaveBeenCalledWith('preValidation', expect.any(Function));
  });

  it('should have run log debug', async () => {
    // Trigger the preValidation hook
    await preValidationFunction(mockRequest, mockReply);

    // Check if the debug was called with correct arguments
    expect(fastify.log.debug).toHaveBeenCalledWith('Executing preValidation hook');
  });
});

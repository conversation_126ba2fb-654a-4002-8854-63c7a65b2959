import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import Fastify from 'fastify';

import onListenHook from '#src/hooks/onListen.hook.js';

describe('Test case for onListen hook', () => {
  let fastify;
  let onListenFunction;

  beforeEach(async () => {
    fastify = Fastify();
    // Add spy to the function
    vi.spyOn(fastify, 'addHook');

    // Mock the log object
    fastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    };

    // Register the hook
    await fastify.register(onListenHook);

    // Extract the function
    onListenFunction = fastify.addHook.mock.calls.find((call) => call[0] === 'onListen')[1];
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should register the onListen hook', async () => {
    expect(fastify.addHook).toHaveBeenCalledWith('onListen', expect.any(Function));
  });

  it('should have run log debug', async () => {
    await fastify.register(onListenHook);

    // Trigger the onListen hook
    await onListenFunction();

    // Check if the debug was called with correct arguments
    expect(fastify.log.debug).toHaveBeenCalledWith('Executing onListen hook');
  });
});

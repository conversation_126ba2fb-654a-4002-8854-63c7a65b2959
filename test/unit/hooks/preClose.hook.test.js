import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import Fastify from 'fastify';

import preCloseHook from '#src/hooks/preClose.hook.js';

describe('Test case for preClose hook', () => {
  let fastify;
  let preCloseFunction;

  beforeEach(async () => {
    fastify = Fastify();
    // Add spy to the function
    vi.spyOn(fastify, 'addHook');

    // Mock the log object
    fastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    };

    // Register the hook
    await fastify.register(preCloseHook, {});

    // Extract the function
    preCloseFunction = fastify.addHook.mock.calls.find((call) => call[0] === 'preClose')[1];
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should register the preClose hook', async () => {
    expect(fastify.addHook).toHaveBeenCalledWith('preClose', expect.any(Function));
  });

  it('should have run log debug', async () => {
    // Trigger the preClose hook
    await preCloseFunction();

    // Check if the debug was called with correct arguments
    expect(fastify.log.debug).toHaveBeenCalledWith('Executing preClose hook');
  });
});

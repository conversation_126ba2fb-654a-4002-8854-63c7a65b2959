import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import Fastify from 'fastify';

import onRouteHook from '#src/hooks/onRoute.hook.js';

describe('Test case for onRoute hook', () => {
  let fastify;
  let onRouteFunction;

  const mockRouteOptions = {};

  beforeEach(async () => {
    fastify = Fastify();
    // Add spy to the function
    vi.spyOn(fastify, 'addHook');

    // Mock the log object
    fastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    };

    // Register the hook
    await fastify.register(onRouteHook, {});

    // Extract the function
    onRouteFunction = fastify.addHook.mock.calls.find((call) => call[0] === 'onRoute')[1];
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should register the onRoute hook', async () => {
    expect(fastify.addHook).toHaveBeenCalledWith('onRoute', expect.any(Function));
  });

  it('should have run log debug', async () => {
    // Trigger the onRoute hook
    await onRouteFunction(mockRouteOptions);

    // Check if the debug was called with correct arguments
    expect(fastify.log.debug).toHaveBeenCalledWith('Executing onRoute hook');
  });
});

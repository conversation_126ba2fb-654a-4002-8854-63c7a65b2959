import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import Fastify from 'fastify';

import { CoreSchema } from '#src/modules/core/schemas/index.js';
import onRouteHook from '#src/hooks/onRoute.hook.js';

describe('Test case for onRoute hook', () => {
  let fastify;
  let onRouteFunction;

  beforeEach(async () => {
    fastify = Fastify();

    vi.spyOn(fastify, 'addHook');

    fastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    };

    await fastify.register(onRouteHook, {});
    onRouteFunction = fastify.addHook.mock.calls.find((call) => call[0] === 'onRoute')[1];
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should register the onRoute hook', async () => {
    expect(fastify.addHook).toHaveBeenCalledWith('onRoute', expect.any(Function));
  });

  it('should call log.debug when schema is provided', async () => {
    const routeOptions = { schema: {} };

    await onRouteFunction(routeOptions);

    expect(fastify.log.debug).toHaveBeenCalledWith('Executing onRoute hook');
  });

  it('should set schema.headers with COMMON_HEADERS if schema exists', async () => {
    const routeOptions = {
      schema: {},
    };

    await onRouteFunction(routeOptions);

    expect(routeOptions.schema.headers).toEqual({
      type: 'object',
      properties: {
        ...CoreSchema.COMMON_HEADERS.properties,
      },
    });
  });

  it('should initialize schema if not provided', async () => {
    const routeOptions = {};

    await onRouteFunction(routeOptions);

    expect(routeOptions.schema.headers).toEqual({
      type: 'object',
      properties: {
        ...CoreSchema.COMMON_HEADERS.properties,
      },
    });
  });

  it('should merge existing header properties with COMMON_HEADERS', async () => {
    const routeOptions = {
      schema: {
        headers: {
          properties: {
            existingHeader: {
              type: 'string',
              description: 'An existing header',
            },
          },
        },
      },
    };

    await onRouteFunction(routeOptions);

    expect(routeOptions.schema.headers.properties).toEqual({
      ...CoreSchema.COMMON_HEADERS.properties,
      existingHeader: {
        type: 'string',
        description: 'An existing header',
      },
    });
  });
});

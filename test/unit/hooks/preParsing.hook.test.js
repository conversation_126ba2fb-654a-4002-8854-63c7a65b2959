import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import Fastify from 'fastify';

import preParsingHook from '#src/hooks/preParsing.hook.js';

describe('Test case for preParsing hook', () => {
  let fastify;
  let preParsingFunction;

  const mockRequest = {};
  const mockReply = {};
  const mockPayload = {
    test: 'test-parsing',
  };

  beforeEach(async () => {
    fastify = Fastify();
    // Add spy to the function
    vi.spyOn(fastify, 'addHook');

    // Mock the log object
    fastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    };

    // Register the hook
    await fastify.register(preParsingHook, {});

    // Extract the function
    preParsingFunction = fastify.addHook.mock.calls.find((call) => call[0] === 'preParsing')[1];
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should register the preParsing hook', async () => {
    expect(fastify.addHook).toHaveBeenCalledWith('preParsing', expect.any(Function));
  });

  it('should have run log debug and payload is matching', async () => {
    // Trigger the preParsing hook
    await preParsingFunction(mockRequest, mockReply, mockPayload);

    // Check if the debug was called with correct arguments
    expect(fastify.log.debug).toHaveBeenCalledWith('Executing preParsing hook');
  });

  it('should have the same payload output', async () => {
    // Trigger the preParsing hook
    const testPayload = await preParsingFunction(mockRequest, mockReply, mockPayload);

    // Check if the data returned is same
    expect(testPayload).equal(mockPayload);
  });
});

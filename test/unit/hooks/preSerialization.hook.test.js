import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import Fastify from 'fastify';

import preSerializationHook from '#src/hooks/preSerialization.hook.js';

describe('Test case for preSerialization hook', () => {
  let fastify;
  let preSerializationFunction;

  const mockRequest = {};
  const mockReply = {};
  const mockPayload = {
    test: 'test-serialization',
  };

  beforeEach(async () => {
    fastify = Fastify();
    // Add spy to the function
    vi.spyOn(fastify, 'addHook');

    // Mock the log object
    fastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    };

    // Register the hook
    await fastify.register(preSerializationHook, {});

    // Extract the function
    preSerializationFunction = fastify.addHook.mock.calls.find(
      (call) => call[0] === 'preSerialization',
    )[1];
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should register the preSerialization hook', async () => {
    expect(fastify.addHook).toHaveBeenCalledWith('preSerialization', expect.any(Function));
  });

  it('should have run log debug and payload is matching', async () => {
    // Trigger the preSerialization hook
    await preSerializationFunction(mockRequest, mockReply, mockPayload);

    // Check if the debug was called with correct arguments
    expect(fastify.log.debug).toHaveBeenCalledWith('Executing preSerialization hook');
  });

  it('should have the same payload output', async () => {
    // Trigger the preSerialization hook
    const testPayload = await preSerializationFunction(mockRequest, mockReply, mockPayload);

    // Check if the data returned is same
    expect(testPayload).equal(mockPayload);
  });
});

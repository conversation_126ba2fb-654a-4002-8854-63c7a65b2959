import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import Fastify from 'fastify';

import onReadyHook from '#src/hooks/onReady.hook.js';

describe('Test case for onReady hook', () => {
  let fastify;
  let onReadyFunction;

  beforeEach(async () => {
    fastify = Fastify();
    // Add spy to the function
    vi.spyOn(fastify, 'addHook');

    // Mock the log object
    fastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    };

    // Register the hook
    await fastify.register(onReadyHook);

    // Extract the function
    onReadyFunction = fastify.addHook.mock.calls.find((call) => call[0] === 'onReady')[1];
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should register the onReady hook', async () => {
    expect(fastify.addHook).toHaveBeenCalledWith('onReady', expect.any(Function));
  });

  it('should have run log debug', async () => {
    // Trigger the onReady hook
    await onReadyFunction();

    // Check if the debug was called with correct arguments
    expect(fastify.log.debug).toHaveBeenCalledWith('Executing onReady hook');
  });
});

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import Fastify from 'fastify';

import onRegisterHook from '#src/hooks/onRegister.hook.js';

describe('Test case for onRegister hook', () => {
  let fastify;
  let onRegisterFunction;

  beforeEach(async () => {
    fastify = Fastify();
    // Add spy to the function
    vi.spyOn(fastify, 'addHook');

    // Mock the log object
    fastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    };

    // Register the hook
    await fastify.register(onRegisterHook, {});

    // Extract the function
    onRegisterFunction = fastify.addHook.mock.calls.find((call) => call[0] === 'onRegister')[1];
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should register the onRegister hook', async () => {
    expect(fastify.addHook).toHaveBeenCalledWith('onRegister', expect.any(Function));
  });

  it('should have run log debug', async () => {
    // Trigger the onRegister hook
    await onRegisterFunction(fastify, {});

    // Check if the debug was called with correct arguments
    expect(fastify.log.debug).toHaveBeenCalledWith('Executing onRegister hook');
  });
});

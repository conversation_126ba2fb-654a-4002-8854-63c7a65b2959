import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { validate as uuidValidate, version as uuidVersion } from 'uuid';
import Fastify from 'fastify';
import onRequestHook from '#src/hooks/onRequest.hook.js';

describe('Test case for onRequest hook', () => {
  let fastify;
  let onRequestFunction;

  // Mock the data
  const mockRequest = {};
  const mockReply = {};

  vi.mock('i18next', () => ({
    default: {
      changeLanguage: vi.fn(),
      options: {
        fallbackLng: 'en',
      },
    },
  }));

  beforeEach(async () => {
    fastify = Fastify();
    // Add spy to the function
    vi.spyOn(fastify, 'addHook');

    // Mock the log object
    fastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    };

    mockRequest.headers = {
      'Accept-Language': 'en-MY',
    };

    // Register the hook
    await fastify.register(onRequestHook, {});

    // Extract the function
    onRequestFunction = fastify.addHook.mock.calls.find((call) => call[0] === 'onRequest')[1];
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should register the onRequest hook', async () => {
    expect(fastify.addHook).toHaveBeenCalledWith('onRequest', expect.any(Function));
  });

  it('should add startTime and uuid to the request object', async () => {
    // Trigger the onRequest hook
    await onRequestFunction(mockRequest, mockReply);

    // Check if the debug was called with correct arguments
    expect(fastify.log.debug).toHaveBeenCalledWith('Executing onRequest hook');

    // Verify start time
    expect(mockRequest.startTime).toBeDefined();
    expect(typeof mockRequest.startTime).toBe('number');

    // Verify UUID
    expect(mockRequest.id).toBeDefined();
    expect(typeof mockRequest.id).toBe('string');
    expect(uuidValidate(mockRequest.id) && uuidVersion(mockRequest.id) === 4).toBeTruthy();
  });

  it('should extract the correct language from accept-language header', async () => {
    // Test with a specific language
    mockRequest.headers['accept-language'] = 'fr-FR';
    await onRequestFunction(mockRequest, mockReply);
    expect(mockRequest.locale).toBe('fr-FR');

    // Test with fallback language
    mockRequest.headers['accept-language'] = undefined;
    await onRequestFunction(mockRequest, mockReply);
    expect(mockRequest.locale).toBeUndefined();
  });
});

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import Fastify from 'fastify';

import onErrorHook from '#src/hooks/onError.hook.js';

describe('Test case for onError hook', () => {
  let fastify;
  let onErrorFunction;

  // Mock the data and function
  const mockError = new Error('Test error');
  const mockRequest = {
    id: 'test-request-id',
  };
  const mockReply = {
    statusCode: 500,
  };

  beforeEach(async () => {
    fastify = Fastify();
    // Add spy to the function
    vi.spyOn(fastify, 'addHook');

    // Mock the log object
    fastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    };
    // Mock the Sentry function
    fastify.sentry = {
      captureException: vi.fn(),
    };

    // Register the hook
    await fastify.register(onErrorHook);

    // Extract the function
    onErrorFunction = fastify.addHook.mock.calls.find((call) => call[0] === 'onError')[1];
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should register the onError hook', async () => {
    expect(fastify.addHook).toHaveBeenCalledWith('onError', expect.any(Function));
  });

  it('should have logged debug and error', async () => {
    // Trigger the onError hook
    await onErrorFunction(mockRequest, mockReply, mockError);

    // Check if debug log was called
    expect(fastify.log.debug).toHaveBeenCalledWith('Executing onError hook');

    // Check if error log was called with correct arguments
    const mockLog = { request: mockRequest, reply: mockReply, error: mockError };
    expect(fastify.log.error).toHaveBeenCalledWith(mockLog, 'Error occurred');
  });

  it('should have triggered sentry', async () => {
    // Trigger the onError hook
    await onErrorFunction(mockRequest, mockReply, mockError);

    // Check if Sentry captureException was called
    expect(fastify.sentry.captureException).toHaveBeenCalledWith(mockError);
  });

  it('should handle errors thrown within the hook', async () => {
    fastify.log.error.mockImplementation(() => {
      throw mockError;
    });

    // Check if error will be thrown
    await expect(onErrorFunction(mockRequest, mockReply, mockError)).rejects.toThrow(mockError);
  });
});

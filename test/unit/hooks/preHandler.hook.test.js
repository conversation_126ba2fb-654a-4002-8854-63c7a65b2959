import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import Fastify from 'fastify';

import preHandlerHook from '#src/hooks/preHandler.hook.js';

describe('Test case for preHandler hook', () => {
  let fastify;
  let preHandlerFunction;

  const mockRequest = {};
  const mockReply = {};

  beforeEach(async () => {
    fastify = Fastify();
    // Add spy to the function
    vi.spyOn(fastify, 'addHook');

    // Mock the log object
    fastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    };

    // Register the hook
    await fastify.register(preHandlerHook, {});

    // Extract the function
    preHandlerFunction = fastify.addHook.mock.calls.find((call) => call[0] === 'preHandler')[1];
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should register the preHandler hook', async () => {
    expect(fastify.addHook).toHaveBeenCalledWith('preHandler', expect.any(Function));
  });

  it('should have run log debug', async () => {
    // Trigger the preHandler hook
    await preHandlerFunction(mockRequest, mockReply);

    // Check if the debug was called with correct arguments
    expect(fastify.log.debug).toHaveBeenCalledWith('Executing preHandler hook');
  });
});

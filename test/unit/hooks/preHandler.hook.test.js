import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import Fastify from 'fastify';

import * as accessUtil from '#src/utils/access.util.js';
import preHandlerHook from '#src/hooks/preHandler.hook.js';

describe('Test case for preHandler hook', () => {
  let fastify;
  let preHandlerFunction;

  let mockRequest = {};
  let mockReply = {};

  beforeEach(async () => {
    fastify = Fastify();
    // Add spy to the function
    vi.spyOn(fastify, 'addHook');

    // Mock the log object
    fastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    };

    // Mock the t function for translation
    fastify.t = vi.fn().mockImplementation((key) => `t_${key}`);

    // Mock the reply object
    mockReply = {
      code: vi.fn(() => mockReply),
      send: vi.fn(),
      server: fastify,
    };

    // Mock the raw url
    mockRequest.raw = {
      url: '/api-route',
    };

    // Mock the sanitiseData function
    fastify.sanitiseData = vi.fn((data) => data);

    // Mock access utilities to allow execution to proceed for most tests
    vi.spyOn(accessUtil, 'checkAccess').mockReturnValue(true);
    vi.spyOn(accessUtil, 'enforceIPWhitelist').mockReturnValue(true);

    // Register the hook
    await fastify.register(preHandlerHook, {});

    // Extract the function
    preHandlerFunction = fastify.addHook.mock.calls.find((call) => call[0] === 'preHandler')[1];
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should register the preHandler hook', async () => {
    expect(fastify.addHook).toHaveBeenCalledWith('preHandler', expect.any(Function));
  });

  it('should have run log debug', async () => {
    // Trigger the preHandler hook
    await preHandlerFunction(mockRequest, mockReply);

    // Check if the debug was called with correct arguments
    expect(fastify.log.debug).toHaveBeenCalledWith('Executing preHandler hook');
  });

  it('should send 403 if access is denied by checkAccess', async () => {
    vi.spyOn(accessUtil, 'checkAccess').mockReturnValue(false);

    const mockRequest = {
      raw: { url: 'api-route' },
      authInfo: { authAccess: 'user' },
      routeOptions: {
        config: {
          access: {
            user: false,
          },
        },
      },
    };

    await preHandlerFunction(mockRequest, mockReply);

    expect(mockReply.code).toHaveBeenCalledWith(403);
    expect(mockReply.send).toHaveBeenCalledWith({ message: fastify.t('error.label.accessDenied') });
  });

  it('should send 403 if IP is not whitelisted', async () => {
    vi.spyOn(accessUtil, 'checkAccess').mockReturnValue(true);
    vi.spyOn(accessUtil, 'enforceIPWhitelist').mockReturnValue(false);

    const mockRequest = {
      raw: { url: 'api-route' },
      authInfo: { authAccess: 'webhook' },
      routeOptions: {
        config: {
          access: {
            webhook: true,
            ipWhitelist: ['127.0.0.1'],
          },
        },
      },
      headers: {
        'x-forwarded-for': '*********',
      },
      ip: '*********',
    };
    await preHandlerFunction(mockRequest, mockReply);
    expect(mockReply.code).toHaveBeenCalledWith(403);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: fastify.t('error.accessControls.sentence.ipBlocked'),
    });
  });

  it('should use default values when routeConfig does not have sanitiseHtml config', async () => {
    const mockBody = { field1: '<script>alert("xss")</script>', field2: 'safe content' };
    const mockRequest = {
      body: mockBody,
      method: 'POST',
      headers: {
        'content-type': 'application/json',
      },
      routeOptions: {
        config: {},
      },
      raw: { url: 'api-route' },
    };

    await preHandlerFunction(mockRequest, mockReply);

    expect(fastify.sanitiseData).toHaveBeenCalledWith(mockBody, [], {});
  });

  it('should sanitize request body when present', async () => {
    const mockBody = {
      field1: '&lt;script&gt;alert(&quot;xss&quot;)&lt;/script&gt;',
      field2: 'safe content',
    };
    const mockRequest = {
      body: mockBody,
      method: 'POST',
      headers: {
        'content-type': 'application/json',
      },
      routeOptions: {
        config: {
          sanitiseHtml: {
            ignoreFields: ['field2'],
            options: { ALLOWED_TAGS: ['b', 'i'] },
          },
        },
      },
      raw: { url: 'api-route' },
    };

    await preHandlerFunction(mockRequest, mockReply);

    expect(fastify.sanitiseData).toHaveBeenCalledWith(mockBody, ['field2'], {
      ALLOWED_TAGS: ['b', 'i'],
    });
    expect(mockRequest.body).toBe(mockBody);
  });

  it('should sanitize request body for JSON content type', async () => {
    const mockBody = {
      field1: '<script>alert("xss")</script>',
      field2: 'safe content',
    };
    const mockRequest = {
      body: mockBody,
      method: 'POST',
      headers: {
        'content-type': 'application/json',
      },
      raw: { url: 'api-route' },
    };

    await preHandlerFunction(mockRequest, mockReply);

    expect(fastify.sanitiseData).toHaveBeenCalledWith(mockBody, [], {});
    expect(mockRequest.body).toBe(mockBody);
  });
});

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import Fastify from 'fastify';

import onResponseHook from '#src/hooks/onResponse.hook.js';

describe('Test case for onResponse hook', () => {
  let fastify;
  let onResponseFunction;

  const mockRequest = {};
  let mockReply = {};

  beforeEach(async () => {
    fastify = Fastify();
    // Add spy to the function
    vi.spyOn(fastify, 'addHook');

    // Mock the log object
    fastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    };

    // Reset mock reply object
    mockReply = {
      statusCode: 200,
    };

    // Register the hook
    await fastify.register(onResponseHook, {});

    // Extract the function
    onResponseFunction = fastify.addHook.mock.calls.find((call) => call[0] === 'onResponse')[1];
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should register the onResponse hook', async () => {
    expect(fastify.addHook).toHaveBeenCalledWith('onResponse', expect.any(Function));
  });

  it('should have run log debug', async () => {
    // Trigger the onResponse hook
    await onResponseFunction(mockRequest, mockReply);

    // Check if the debug was called with correct arguments
    expect(fastify.log.debug).toHaveBeenCalledWith('Executing onResponse hook');
  });

  it('should have run log info with different message when statusCode is different', async () => {
    // Define expected result
    const successResponseMessage = 'Request completed successfully';
    const failResponseMessage = 'Request completed with error';

    // Set mockReply.statusCode to 200
    mockReply.statusCode = 200;

    // Trigger the onResponse hook
    await onResponseFunction(mockRequest, mockReply);

    // Check if the info was called with correct arguments when statusCode is 200
    expect(fastify.log.info).toHaveBeenCalledWith(
      { request: mockRequest, reply: mockReply },
      successResponseMessage,
    );

    // Reset mocks for error test case
    vi.resetAllMocks();

    // Set mockReply.statusCode to 400
    mockReply.statusCode = 400;

    // Trigger the onResponse hook
    await onResponseFunction(mockRequest, mockReply);

    // Check if the info was called with correct arguments when statusCode is 200
    expect(fastify.log.info).toHaveBeenCalledWith(
      { request: mockRequest, reply: mockReply },
      failResponseMessage,
    );
  });
});

import { beforeEach, describe, expect, it, vi } from 'vitest';
import { AuditTrailHandler } from '#src/modules/audit-trail/handlers/index.js';
import AuditTrailRoute from '#src/modules/audit-trail/routes/audit-trail.route.js';
import { AuditTrailSchema } from '#src/modules/audit-trail/schemas/index.js';

describe('AuditTrailRoute', () => {
  let fastifyMock;

  const commonAccessConfig = {
    user: true,
    member: false,
    webhook: true,
    public: false,
    ipWhitelist: ['127.0.0.1'],
  };

  beforeEach(() => {
    fastifyMock = {
      get: vi.fn(),
      post: vi.fn(),
    };

    vi.clearAllMocks();
  });

  it('should register all audit trail routes with correct configuration', async () => {
    await AuditTrailRoute(fastifyMock);

    expect(fastifyMock.get).toHaveBeenCalledTimes(2);
    expect(fastifyMock.post).toHaveBeenCalledTimes(1);
  });

  it('should register the index route correctly', async () => {
    await AuditTrailRoute(fastifyMock);

    expect(fastifyMock.get).toHaveBeenCalledWith('/', {
      schema: AuditTrailSchema.index,
      config: { name: 'auditTrail.list', access: commonAccessConfig },
      handler: AuditTrailHandler.index,
    });
  });

  it('should register the view route correctly', async () => {
    await AuditTrailRoute(fastifyMock);

    expect(fastifyMock.get).toHaveBeenCalledWith('/:id', {
      schema: AuditTrailSchema.view,
      config: { name: 'auditTrail.view', access: commonAccessConfig },
      handler: AuditTrailHandler.view,
    });
  });

  it('should register the export route correctly', async () => {
    await AuditTrailRoute(fastifyMock);

    expect(fastifyMock.post).toHaveBeenCalledWith('/export', {
      schema: AuditTrailSchema.exportAuditTrails,
      config: { name: 'auditTrail.export', access: commonAccessConfig },
      handler: AuditTrailHandler.exportAuditTrail,
    });
  });
});

import * as AuditTrailHandler from '#src/modules/audit-trail/handlers/audit-trail.handler.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { AuditTrailService } from '#src/modules/audit-trail/services/index.js';
import { fetchFromCache } from '#src/utils/cache.util.js';
import { handleServiceResponse } from '#src/utils/response.util.js';
vi.mock('#src/modules/audit-trail/services/index.js', () => ({
  AuditTrailService: {
    index: vi.fn(),
    view: vi.fn(),
    exportAuditTrail: vi.fn(),
  },
}));

vi.mock('#src/utils/cache.util.js', () => ({
  fetchFromCache: vi.fn(),
  generateCacheKey: vi.fn(() => 'test-cache-key'),
}));

vi.mock('#src/utils/response.util.js', () => ({
  handleServiceResponse: vi.fn(),
}));

describe('Test case for AuditTrail Handler', () => {
  let mockRequest;
  let mockReply;

  beforeEach(() => {
    vi.clearAllMocks();
    mockRequest = {
      server: {
        redis: {},
        withAuditLogging: vi.fn().mockResolvedValue(undefined),
      },
      params: { id: '1' },
    };
    mockReply = { status: vi.fn().mockReturnThis(), send: vi.fn() };
  });

  it('should call handleServiceResponse with cached index service', async () => {
    fetchFromCache.mockResolvedValue({ rows: [{ id: 1 }], meta: {} });

    await AuditTrailHandler.index(mockRequest, mockReply);

    expect(mockRequest.server.withAuditLogging).toHaveBeenCalled();
    expect(handleServiceResponse).toHaveBeenCalledWith(
      expect.objectContaining({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: expect.any(String),
        method: expect.any(String),
      }),
    );

    const { serviceFn } = handleServiceResponse.mock.calls[0][0];
    await serviceFn();
  });

  it('should call handleServiceResponse with cached view service', async () => {
    fetchFromCache.mockResolvedValue({ rows: [{ id: 1 }] });

    await AuditTrailHandler.view(mockRequest, mockReply);

    expect(mockRequest.server.withAuditLogging).toHaveBeenCalled();
    expect(handleServiceResponse).toHaveBeenCalledWith(
      expect.objectContaining({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: expect.any(String),
        method: expect.any(String),
      }),
    );

    const { serviceFn } = handleServiceResponse.mock.calls[0][0];
    await serviceFn();
  });

  it('should call handleServiceResponse when export is successful', async () => {
    AuditTrailService.exportAuditTrail.mockResolvedValue({ id: 1 });

    await AuditTrailHandler.exportAuditTrail(mockRequest, mockReply);

    expect(AuditTrailService.exportAuditTrail).toHaveBeenCalledWith(mockRequest);
    expect(handleServiceResponse).toHaveBeenCalledWith(
      expect.objectContaining({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: expect.any(String),
        method: expect.any(String),
      }),
    );

    const { serviceFn } = handleServiceResponse.mock.calls[0][0];
    await serviceFn();
  });
});

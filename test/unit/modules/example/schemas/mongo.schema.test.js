import { createMongoSchema, getMongoSchema } from '#src/modules/example/schemas/mongo.schema.js';
import { describe, expect, it } from 'vitest';

describe('Mongo Schema', () => {
  describe('getMongoSchema', () => {
    it('should have correct structure', () => {
      expect(getMongoSchema).toBeDefined();
      expect(getMongoSchema.tags).toEqual(['Mongo']);
      expect(getMongoSchema.summary).toBe('Get all mongo records');
      expect(getMongoSchema.description).toBe('Retrieves a list of mongo.');
      expect(getMongoSchema.querystring).toEqual({ type: 'object', properties: {} });
      expect(getMongoSchema.response).toBeDefined();
      expect(getMongoSchema.examples).toBeDefined();
    });

    it('should have correct response schema', () => {
      const responseSchema = getMongoSchema.response[200];
      expect(responseSchema.type).toBe('object');
      expect(responseSchema.properties.message).toBeDefined();
      expect(responseSchema.properties.data).toBeDefined();
      expect(responseSchema.properties.data.type).toBe('array');
      expect(responseSchema.properties.data.items.type).toBe('object');
      expect(responseSchema.properties.data.items.properties).toHaveProperty('name');
      expect(responseSchema.properties.data.items.properties).toHaveProperty('timestamp');
    });

    it('should have error responses', () => {
      expect(getMongoSchema.response['4xx']).toEqual({ $ref: 'ErrorResponse' });
      expect(getMongoSchema.response['5xx']).toEqual({ $ref: 'ErrorResponse' });
    });
  });

  describe('createMongoSchema', () => {
    it('should have correct structure', () => {
      expect(createMongoSchema).toBeDefined();
      expect(createMongoSchema.tags).toEqual(['Mongo']);
      expect(createMongoSchema.summary).toBe('Create a new mongo entry');
      expect(createMongoSchema.description).toBe('Creates a new mongo entry in the system.');
      expect(createMongoSchema.body).toBeDefined();
      expect(createMongoSchema.response).toBeDefined();
    });

    it('should have correct body schema', () => {
      const bodySchema = createMongoSchema.body;
      expect(bodySchema.type).toBe('object');
      expect(bodySchema.required).toEqual(['name']);
      expect(bodySchema.properties.name.type).toBe('string');
    });

    it('should have correct response schema', () => {
      const responseSchema = createMongoSchema.response[201];
      expect(responseSchema.type).toBe('object');
      expect(responseSchema.properties.message).toBeDefined();
      expect(responseSchema.properties.data).toBeDefined();
      expect(responseSchema.properties.data.type).toBe('object');
      expect(responseSchema.properties.data.properties).toHaveProperty('name');
      expect(responseSchema.properties.data.properties).toHaveProperty('timestamp');
    });

    it('should have error responses', () => {
      expect(createMongoSchema.response['4xx']).toEqual({ $ref: 'ErrorResponse' });
      expect(createMongoSchema.response['5xx']).toEqual({ $ref: 'ErrorResponse' });
    });
  });
});

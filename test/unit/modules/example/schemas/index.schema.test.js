import { describe, expect, it, vi } from 'vitest';

// Mock the mongo.schema.js module
vi.mock('#src/modules/example/schemas/mongo.schema.js', () => ({
  createMongoSchema: { type: 'object' },
  getMongoSchema: { type: 'object' },
  // Add any other schemas that are exported from mongo.schema.js
}));

// Import the module under test
import * as SchemaModule from '#src/modules/example/schemas/index.js';

describe('Example Schema Index', () => {
  it('should export MongoSchema', () => {
    expect(SchemaModule).toBeDefined();
    expect(typeof SchemaModule).toBe('object');
    expect(SchemaModule).toHaveProperty('MongoSchema');
    expect(typeof SchemaModule.MongoSchema).toBe('object');
  });

  it('should have correct schemas in MongoSchema', () => {
    const expectedSchemas = [
      'createMongoSchema',
      'getMongoSchema',
      // Add any other schemas that are exported from mongo.schema.js
    ];

    expectedSchemas.forEach((schemaName) => {
      expect(SchemaModule.MongoSchema).toHaveProperty(schemaName);
      expect(SchemaModule.MongoSchema[schemaName]).toEqual({ type: 'object' });
    });
  });

  it('should only export MongoSchema', () => {
    const moduleKeys = Object.keys(SchemaModule);
    expect(moduleKeys).toEqual(['MongoSchema']);
  });
});

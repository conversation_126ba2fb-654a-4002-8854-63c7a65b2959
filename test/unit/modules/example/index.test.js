import { describe, expect, it, vi } from 'vitest';

// Mock the actual route files
vi.mock('#src/modules/example/routes/example.route.js', () => ({
  default: function ExampleRoute() {},
}));
vi.mock('#src/modules/example/routes/item.route.js', () => ({
  default: function ItemRoute() {},
}));
vi.mock('#src/modules/example/routes/mongo.route.js', () => ({
  default: function MongoRoute() {},
}));
vi.mock('#src/modules/example/routes/upload.route.js', () => ({
  default: function UploadRoute() {},
}));

// Import the module under test
import * as exampleModule from '#src/modules/example/index.js';

describe('Example Module Index', () => {
  it('should not export any unexpected properties', () => {
    const expectedKeys = [
      'ExampleRoute',
      'ItemRoute',
      'MongoRoute',
      'TypesenseRoute',
      'UploadRoute',
    ];
    const actualKeys = Object.keys(exampleModule);

    expect(actualKeys).toEqual(expect.arrayContaining(expectedKeys));
    expect(actualKeys.length).toBe(expectedKeys.length);
  });
});

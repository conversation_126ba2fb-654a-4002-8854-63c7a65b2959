import { beforeEach, describe, expect, it, vi } from 'vitest';
import mongoModel from '#src/modules/example/models/mongo/mongo.model.js';
import mongoose from 'mongoose';

// Mock mongoose
vi.mock('mongoose', () => ({
  default: {
    Schema: vi.fn(),
  },
}));

describe('Mongo Model', () => {
  let fastifyMock;
  let schemaMock;
  let modelMock;

  beforeEach(() => {
    // Reset mocks
    vi.resetAllMocks();

    // Create mock for Schema
    schemaMock = {
      // Add any methods you need to mock here
    };
    mongoose.Schema.mockReturnValue(schemaMock);

    // Create mock for model
    modelMock = vi.fn();

    // Create mock for fastify
    fastifyMock = {
      mongo: {
        connection: {
          model: modelMock,
        },
      },
    };
  });

  it('should create a mongoose schema with correct fields', () => {
    mongoModel(fastifyMock);

    expect(mongoose.Schema).toHaveBeenCalledWith(
      {
        name: { type: String, required: true },
        timestamp: { type: Date, default: Date.now, required: true },
      },
      { timestamps: true },
    );
  });

  it('should create and return a mongoose model', () => {
    const result = mongoModel(fastifyMock);

    expect(fastifyMock.mongo.connection.model).toHaveBeenCalledWith('Mongo', schemaMock);
    expect(result).toBe(modelMock.mock.results[0].value);
  });
});

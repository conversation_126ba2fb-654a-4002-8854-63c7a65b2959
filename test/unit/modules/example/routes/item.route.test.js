import { beforeEach, describe, expect, it, vi } from 'vitest';
import { formatErrorResponse, formatSuccessResponse } from '#src/utils/response.util.js';
import ItemRoute from '#src/modules/example/routes/item.route.js';

vi.mock('#src/utils/response.util.js', () => ({
  formatErrorResponse: vi.fn(),
  formatSuccessResponse: vi.fn(),
}));

describe('ItemRoute', () => {
  let fastifyMock;
  let replyMock;

  beforeEach(() => {
    fastifyMock = {
      route: vi.fn(),
      log: {
        error: vi.fn(),
      },
    };

    replyMock = {
      send: vi.fn(),
      status: vi.fn().mockReturnThis(),
      notFound: vi.fn(),
      internalServerError: vi.fn(),
    };

    ItemRoute(fastifyMock);
  });

  describe('GET /items', () => {
    it('should return all items', async () => {
      const handler = fastifyMock.route.mock.calls.find(
        (call) => call[0].url === '/items' && call[0].method === 'GET',
      )[0].handler;

      await handler({}, replyMock);

      expect(formatSuccessResponse).toHaveBeenCalledWith(
        'Items retrieved successfully',
        expect.any(Array),
      );
      expect(replyMock.send).toHaveBeenCalled();
    });

    it('should handle errors', async () => {
      const handler = fastifyMock.route.mock.calls.find(
        (call) => call[0].url === '/items' && call[0].method === 'GET',
      )[0].handler;
      replyMock.send.mockImplementationOnce(() => {
        throw new Error('Test error');
      });

      await handler({}, replyMock);

      expect(fastifyMock.log.error).toHaveBeenCalled();
      expect(replyMock.internalServerError).toHaveBeenCalled();
    });
  });

  describe('GET /items/:id', () => {
    it('should return an item by id', async () => {
      const handler = fastifyMock.route.mock.calls.find(
        (call) => call[0].url === '/items/:id' && call[0].method === 'GET',
      )[0].handler;

      await handler({ params: { id: '1' } }, replyMock);

      expect(formatSuccessResponse).toHaveBeenCalledWith(
        'Item retrieved successfully',
        expect.any(Object),
      );
      expect(replyMock.send).toHaveBeenCalled();
    });

    it('should return not found for non-existent item', async () => {
      const handler = fastifyMock.route.mock.calls.find(
        (call) => call[0].url === '/items/:id' && call[0].method === 'GET',
      )[0].handler;

      await handler({ params: { id: '999' } }, replyMock);

      expect(formatErrorResponse).toHaveBeenCalledWith('Item not found', 'ITEM_NOT_FOUND');
      expect(replyMock.notFound).toHaveBeenCalled();
    });
  });

  describe('POST /items', () => {
    it('should create a new item', async () => {
      const handler = fastifyMock.route.mock.calls.find(
        (call) => call[0].url === '/items' && call[0].method === 'POST',
      )[0].handler;

      await handler({ body: { name: 'New Item', amount: 200 } }, replyMock);

      expect(formatSuccessResponse).toHaveBeenCalledWith(
        'Item created successfully',
        expect.any(Object),
      );
      expect(replyMock.status).toHaveBeenCalledWith(201);
      expect(replyMock.send).toHaveBeenCalled();
    });
  });

  describe('PUT /items/:id', () => {
    it('should update an existing item', async () => {
      const handler = fastifyMock.route.mock.calls.find(
        (call) => call[0].url === '/items/:id' && call[0].method === 'PUT',
      )[0].handler;

      await handler(
        { params: { id: '1' }, body: { name: 'Updated Item', amount: 150 } },
        replyMock,
      );

      expect(formatSuccessResponse).toHaveBeenCalledWith(
        'Item updated successfully',
        expect.any(Object),
      );
      expect(replyMock.send).toHaveBeenCalled();
    });

    it('should return not found for non-existent item', async () => {
      const handler = fastifyMock.route.mock.calls.find(
        (call) => call[0].url === '/items/:id' && call[0].method === 'PUT',
      )[0].handler;

      await handler(
        { params: { id: '999' }, body: { name: 'Updated Item', amount: 150 } },
        replyMock,
      );

      expect(replyMock.notFound).toHaveBeenCalled();
    });
  });

  describe('DELETE /items/:id', () => {
    it('should delete an existing item', async () => {
      const handler = fastifyMock.route.mock.calls.find(
        (call) => call[0].url === '/items/:id' && call[0].method === 'DELETE',
      )[0].handler;

      await handler({ params: { id: '2' } }, replyMock);

      expect(formatSuccessResponse).toHaveBeenCalledWith('Item deleted successfully');
      expect(replyMock.send).toHaveBeenCalled();
    });

    it('should return not found for non-existent item', async () => {
      const handler = fastifyMock.route.mock.calls.find(
        (call) => call[0].url === '/items/:id' && call[0].method === 'DELETE',
      )[0].handler;

      await handler({ params: { id: '999' } }, replyMock);

      expect(replyMock.notFound).toHaveBeenCalled();
    });
  });
});

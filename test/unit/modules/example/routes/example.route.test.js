import { beforeEach, describe, expect, it, vi } from 'vitest';
import ExampleRoute from '#src/modules/example/routes/example.route.js';
import { fetchFromCache } from '#src/utils/cache.util.js';

vi.mock('#src/utils/cache.util.js', () => ({
  fetchFromCache: vi.fn(),
}));

describe('ExampleRoute', () => {
  let fastifyMock;

  beforeEach(() => {
    fastifyMock = {
      get: vi.fn(),
      config: {},
      redis: {
        flushall: vi.fn().mockResolvedValue(),
      },
    };
    ExampleRoute(fastifyMock);
  });

  it('should register all routes', () => {
    expect(fastifyMock.get).toHaveBeenCalledTimes(4);
    expect(fastifyMock.get).toHaveBeenCalledWith('/', expect.any(Function));
    expect(fastifyMock.get).toHaveBeenCalledWith('/health-check', expect.any(Function));
    expect(fastifyMock.get).toHaveBeenCalledWith('/cache', expect.any(Function));
    expect(fastifyMock.get).toHaveBeenCalledWith('/clear-all-cache', expect.any(Function));
  });

  describe('Root route', () => {
    it('should return correct message for non-Docker environment', async () => {
      const route = fastifyMock.get.mock.calls.find((call) => call[0] === '/')[1];
      const reply = { status: vi.fn().mockReturnThis(), send: vi.fn() };

      await route({}, reply);

      expect(reply.status).toHaveBeenCalledWith(200);
      expect(reply.send).toHaveBeenCalledWith({
        success: true,
        message: 'Hello World! I have been seen 1 times.',
        data: { count: 1 },
      });
    });

    it('should return correct message for Docker environment', async () => {
      fastifyMock.config.DOCKER_CONTAINER = true;
      const route = fastifyMock.get.mock.calls.find((call) => call[0] === '/')[1];
      const reply = { status: vi.fn().mockReturnThis(), send: vi.fn() };

      await route({}, reply);

      expect(reply.status).toHaveBeenCalledWith(200);
      expect(reply.send).toHaveBeenCalledWith({
        success: true,
        message: 'Hello from Docker! I have been seen 1 times.',
        data: { count: 1 },
      });
    });
  });

  describe('Health check route', () => {
    it('should return success status', async () => {
      const route = fastifyMock.get.mock.calls.find((call) => call[0] === '/health-check')[1];
      const reply = { status: vi.fn().mockReturnThis(), send: vi.fn() };

      await route({}, reply);

      expect(reply.status).toHaveBeenCalledWith(200);
      expect(reply.send).toHaveBeenCalledWith({ success: true });
    });
  });

  describe('Cache route', () => {
    it('should fetch data from cache', async () => {
      const cachedData = { message: 'Cached data', timestamp: '2023-06-25T12:34:56.789Z' };
      fetchFromCache.mockResolvedValue(cachedData);

      const route = fastifyMock.get.mock.calls.find((call) => call[0] === '/cache')[1];
      const reply = { send: vi.fn() };

      await route({}, reply);

      expect(fetchFromCache).toHaveBeenCalledWith(
        fastifyMock.redis,
        'cache_route',
        expect.any(Function),
      );
      expect(reply.send).toHaveBeenCalledWith({ source: 'generated', data: cachedData });
    });
  });

  describe('Clear all cache route', () => {
    it('should clear all cache', async () => {
      const route = fastifyMock.get.mock.calls.find((call) => call[0] === '/clear-all-cache')[1];
      const reply = { send: vi.fn() };

      await route({}, reply);

      expect(fastifyMock.redis.flushall).toHaveBeenCalled();
      expect(reply.send).toHaveBeenCalledWith({ message: 'All cache cleared successfully.' });
    });
  });
});

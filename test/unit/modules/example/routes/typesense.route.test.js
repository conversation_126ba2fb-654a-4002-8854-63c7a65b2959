import { beforeEach, describe, expect, it, vi } from 'vitest';
import TypesenseRoute from '#src/modules/example/routes/typesense.route.js';
import { formatSuccessResponse } from '#src/utils/response.util.js';

describe('TypesenseRoute', () => {
  let fastifyMock;

  beforeEach(() => {
    fastifyMock = {
      route: vi.fn(),
      server: {
        typesense: {
          collections: vi.fn().mockReturnThis(),
          documents: vi.fn().mockReturnThis(),
          create: vi.fn(),
          search: vi.fn(),
        },
      },
    };
  });

  it('should register three routes', () => {
    TypesenseRoute(fastifyMock);
    expect(fastifyMock.route).toHaveBeenCalledTimes(3);
  });

  describe('POST /collection', () => {
    it('should create a collection', async () => {
      const mockCollection = { name: 'testCollection', fields: [] };
      fastifyMock.server.typesense.collections().create.mockResolvedValue(mockCollection);

      let routeHandler;
      fastifyMock.route.mockImplementationOnce(({ handler }) => {
        routeHandler = handler;
      });

      TypesenseRoute(fastifyMock);

      const mockRequest = {
        body: { name: 'testCollection', fields: [] },
        server: fastifyMock.server,
      };
      const mockReply = {
        status: vi.fn().mockReturnThis(),
        send: vi.fn(),
      };

      await routeHandler(mockRequest, mockReply);

      expect(mockReply.status).toHaveBeenCalledWith(200);
      expect(mockReply.send).toHaveBeenCalledWith(
        formatSuccessResponse('Collection created successfully', mockCollection),
      );
    });
  });

  describe('POST /document', () => {
    it('should create a document', async () => {
      const mockDocument = { id: '1', name: 'Test Document' };
      fastifyMock.server.typesense.collections().documents().create.mockResolvedValue(mockDocument);

      let routeHandler;
      fastifyMock.route
        .mockImplementationOnce(() => {})
        .mockImplementationOnce(({ handler }) => {
          routeHandler = handler;
        });

      TypesenseRoute(fastifyMock);

      const mockRequest = {
        body: { collectionName: 'testCollection', document: { name: 'Test Document' } },
        server: fastifyMock.server,
      };
      const mockReply = {
        status: vi.fn().mockReturnThis(),
        send: vi.fn(),
      };

      await routeHandler(mockRequest, mockReply);

      expect(mockReply.status).toHaveBeenCalledWith(200);
      expect(mockReply.send).toHaveBeenCalledWith(
        formatSuccessResponse('Document created successfully', mockDocument),
      );
    });
  });

  describe('GET /document', () => {
    it('should search for documents', async () => {
      const mockSearchResult = {
        found: 1,
        hits: [{ document: { id: '1', name: 'Test Document' } }],
      };
      fastifyMock.server.typesense
        .collections()
        .documents()
        .search.mockResolvedValue(mockSearchResult);

      let routeHandler;
      fastifyMock.route
        .mockImplementationOnce(() => {})
        .mockImplementationOnce(() => {})
        .mockImplementationOnce(({ handler }) => {
          routeHandler = handler;
        });

      TypesenseRoute(fastifyMock);

      const mockRequest = {
        query: { collectionName: 'testCollection', q: 'test', queryBy: 'name' },
        server: fastifyMock.server,
      };
      const mockReply = {
        status: vi.fn().mockReturnThis(),
        send: vi.fn(),
      };

      await routeHandler(mockRequest, mockReply);

      expect(mockReply.status).toHaveBeenCalledWith(200);
      expect(mockReply.send).toHaveBeenCalledWith(
        formatSuccessResponse('Document retrieved successfully', mockSearchResult),
      );
    });
  });
});

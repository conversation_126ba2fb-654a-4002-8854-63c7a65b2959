import { beforeEach, describe, expect, it, vi } from 'vitest';
import { formatErrorResponse, formatSuccessResponse } from '#src/utils/response.util.js';
import UploadRoute from '#src/modules/example/routes/upload.route.js';
import uploadToS3 from '#src/utils/upload.util.js';

// Mock the external dependencies
vi.mock('#src/utils/response.util.js');
vi.mock('#src/utils/upload.util.js');

describe('UploadRoute', () => {
  let mockFastify;
  let mockRequest;
  let mockReply;

  beforeEach(() => {
    // Reset all mocks before each test
    vi.resetAllMocks();

    // Mock Fastify instance
    mockFastify = {
      route: vi.fn(),
      log: {
        error: vi.fn(),
      },
    };

    // Mock request and reply objects
    mockRequest = {
      body: {
        file: {
          toBuffer: vi.fn().mockResolvedValue(Buffer.from('test file content')),
          filename: 'test.jpg',
          mimetype: 'image/jpeg',
        },
      },
    };

    mockReply = {
      status: vi.fn().mockReturnThis(),
      send: vi.fn(),
    };

    // Mock the formatSuccessResponse and formatErrorResponse functions
    formatSuccessResponse.mockImplementation((message, data) => ({ message, data }));
    formatErrorResponse.mockImplementation((message, code) => ({ message, code }));

    // Mock the uploadToS3 function
    uploadToS3.mockResolvedValue('https://example.com/test.jpg');
  });

  it('should register the upload route', () => {
    UploadRoute(mockFastify);
    expect(mockFastify.route).toHaveBeenCalledWith(
      expect.objectContaining({
        method: 'POST',
        url: '/upload',
      }),
    );
  });

  it('should handle successful file upload', async () => {
    UploadRoute(mockFastify);
    const routeHandler = mockFastify.route.mock.calls[0][0].handler;

    await routeHandler(mockRequest, mockReply);

    expect(uploadToS3).toHaveBeenCalledWith(
      expect.any(Buffer),
      'test.jpg',
      'image/jpeg',
      mockFastify,
    );
    expect(mockReply.status).toHaveBeenCalledWith(200);
    expect(mockReply.send).toHaveBeenCalledWith(
      expect.objectContaining({
        message: 'File uploaded successfully',
        data: {
          success: true,
          file: 'https://example.com/test.jpg',
        },
      }),
    );
  });

  it('should handle empty file upload', async () => {
    mockRequest.body.file.toBuffer.mockResolvedValue(Buffer.from(''));
    UploadRoute(mockFastify);
    const routeHandler = mockFastify.route.mock.calls[0][0].handler;

    await routeHandler(mockRequest, mockReply);

    expect(mockReply.status).toHaveBeenCalledWith(400);
    expect(mockReply.send).toHaveBeenCalledWith(
      expect.objectContaining({
        message: 'No file uploaded',
        code: 'BAD_REQUEST',
      }),
    );
  });

  it('should handle upload errors', async () => {
    uploadToS3.mockRejectedValue(new Error('Upload failed'));
    UploadRoute(mockFastify);
    const routeHandler = mockFastify.route.mock.calls[0][0].handler;

    await routeHandler(mockRequest, mockReply);

    expect(mockFastify.log.error).toHaveBeenCalled();
    expect(mockReply.status).toHaveBeenCalledWith(500);
    expect(mockReply.send).toHaveBeenCalledWith(
      expect.objectContaining({
        message: 'Internal server error',
        code: 'INTERNAL_SERVER_ERROR',
      }),
    );
  });
});

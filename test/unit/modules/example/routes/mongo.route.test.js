import { beforeEach, describe, expect, it, vi } from 'vitest';
import Fastify from 'fastify';
import MongoRoute from '#src/modules/example/routes/mongo.route.js';

vi.mock('#src/utils/response.util.js', () => ({
  formatSuccessResponse: vi.fn((message, data) => ({ message, data })),
}));

// Mock ErrorResponse schema
const mockErrorResponse = {
  $id: 'ErrorResponse',
  type: 'object',
  properties: {
    statusCode: { type: 'number' },
    error: { type: 'string' },
    message: { type: 'string' },
  },
};

describe('MongoRoute', () => {
  let fastify;

  beforeEach(() => {
    fastify = Fastify();

    // Register the mock ErrorResponse schema
    fastify.addSchema(mockErrorResponse);

    fastify.mongo = {
      Mongo: vi.fn().mockImplementation(() => ({
        save: vi.fn().mockResolvedValue({ name: 'Test', timestamp: new Date() }),
      })),
    };
    fastify.server = {
      mongo: {
        Mongo: {
          find: vi.fn().mockResolvedValue([{ name: 'Test', timestamp: new Date() }]),
        },
      },
    };
    MongoRoute(fastify);
  });

  describe('GET /', () => {
    it('should handle errors when fetching mongo records', async () => {
      fastify.server.mongo.Mongo.find.mockRejectedValueOnce(new Error('Database error'));

      const response = await fastify.inject({
        method: 'GET',
        url: '/',
      });

      expect(response.statusCode).toBe(500);
      const payload = JSON.parse(response.payload);
      expect(payload.error).toBe('Internal Server Error');
    });
  });

  describe('POST /', () => {
    it('should handle errors when creating a new mongo record', async () => {
      fastify.mongo.Mongo.mockImplementationOnce(() => ({
        save: vi.fn().mockRejectedValueOnce(new Error('Database error')),
      }));

      const response = await fastify.inject({
        method: 'POST',
        url: '/',
        payload: { name: 'New Test' },
      });

      expect(response.statusCode).toBe(500);
      const payload = JSON.parse(response.payload);
      expect(payload.error).toBe('Internal Server Error');
    });
  });
});

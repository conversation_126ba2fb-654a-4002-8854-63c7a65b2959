import { describe, expect, it } from 'vitest';

import * as localisationSchema from '#src/modules/setting/schemas/localisation.schema.js';
import { COMMON_STATUSES, MODULE_NAMES } from '#src/modules/core/constants/core.constant.js';
import { LocalisationConstant } from '#src/modules/setting/constants/index.js';

const { LOCALISATION_CATEGORIES } = LocalisationConstant;
const { LOCALISATION } = MODULE_NAMES;

describe('Localisation Schema', () => {
  describe('index schema', () => {
    it('should have correct tags and summary', () => {
      expect(localisationSchema.index.tags).toEqual([
        'BO / Settings / Currency, Language, Regions',
      ]);
      expect(localisationSchema.index.summary).toBe(`Get a list of ${LOCALISATION}`);
    });

    it('should have correct querystring properties', () => {
      const { properties } = localisationSchema.index.querystring;
      expect(properties).toHaveProperty('filter_localisation.category_eq');
      expect(properties).toHaveProperty('sortBy');
    });

    it('should require filter_category', () => {
      expect(localisationSchema.index.querystring.required).toContain(
        'filter_localisation.category_eq',
      );
    });

    it('should have correct response schema', () => {
      const { properties } = localisationSchema.index.response[200];
      expect(properties).toHaveProperty('message');
      expect(properties).toHaveProperty('data');
      expect(properties).toHaveProperty('meta');
    });
  });

  describe('view schema', () => {
    it('should have correct tags and summary', () => {
      expect(localisationSchema.view.tags).toEqual(['BO / Settings / Currency, Language, Regions']);
      expect(localisationSchema.view.summary).toBe(`View a ${LOCALISATION}`);
    });

    it('should have params', () => {
      expect(localisationSchema.view).toHaveProperty('params');
    });

    it('should have response schema', () => {
      expect(localisationSchema.view).toHaveProperty('response');
    });
  });

  describe('update schema', () => {
    it('should have correct tags and summary', () => {
      expect(localisationSchema.update.tags).toEqual([
        'BO / Settings / Currency, Language, Regions',
      ]);
      expect(localisationSchema.update.summary).toBe(`Update a ${LOCALISATION}`);
    });

    it('should have params', () => {
      expect(localisationSchema.update).toHaveProperty('params');
    });

    it('should have correct body schema', () => {
      const { properties, required } = localisationSchema.update.body;
      expect(properties).toHaveProperty('version');
      expect(required).toContain('version');
    });

    it('should have response schema', () => {
      expect(localisationSchema.update).toHaveProperty('response');
    });
  });

  describe('updateStatus schema', () => {
    it('should have correct tags and summary', () => {
      expect(localisationSchema.updateStatus.tags).toEqual([
        'BO / Settings / Currency, Language, Regions',
      ]);
      expect(localisationSchema.updateStatus.summary).toBe(`Update a ${LOCALISATION} status`);
    });

    it('should have params', () => {
      expect(localisationSchema.updateStatus).toHaveProperty('params');
    });

    it('should have correct body schema', () => {
      const { properties, required } = localisationSchema.updateStatus.body;
      expect(properties).toHaveProperty('status');
      expect(properties).toHaveProperty('version');
      expect(required).toContain('version');
      expect(required).toContain('status');
    });

    it('should have correct status enum', () => {
      const { status } = localisationSchema.updateStatus.body.properties;
      expect(status.enum).toEqual(Object.values(COMMON_STATUSES));
    });

    it('should have response schema', () => {
      expect(localisationSchema.updateStatus).toHaveProperty('response');
    });
  });
});

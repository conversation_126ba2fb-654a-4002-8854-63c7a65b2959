import { describe, expect, it } from 'vitest';

import * as settingSchema from '#src/modules/setting/schemas/setting.schema.js';
import { CoreSchema } from '#src/modules/core/schemas';

describe('Setting Schema', () => {
  describe('index schema', () => {
    it('should have correct tags and summary', () => {
      expect(settingSchema.index.tags).toEqual(['BO / Settings / Personal, Safety, Themes']);
      expect(settingSchema.index.summary).toBe('Get a setting by category');
    });

    it('should have correct params properties', () => {
      const { properties } = settingSchema.index.params;
      expect(properties).toHaveProperty('category');
    });

    it('should have correct response schema', () => {
      const { properties } = settingSchema.index.response[200];
      expect(properties).toHaveProperty('message');
      expect(properties).toHaveProperty('data');
    });
  });

  describe('updateThemes schema', () => {
    it('should have correct tags and summary', () => {
      expect(settingSchema.updateThemes.tags).toEqual(['BO / Settings / Personal, Safety, Themes']);
      expect(settingSchema.updateThemes.summary).toBe('Update a themes setting');
    });

    it('should have correct body schema', () => {
      const { properties, required } = settingSchema.updateThemes.body;

      expect(properties).toHaveProperty('version');
      expect(properties).toHaveProperty('toastMessagePosition');
      expect(properties).toHaveProperty('alertBarPosition');

      expect(required).toEqual(['version', 'toastMessagePosition', 'alertBarPosition']);
    });

    it('should have response schema', () => {
      expect(settingSchema.updateThemes.response).toEqual(CoreSchema.UPDATE_RESPONSE);
    });
  });

  describe('updatePersonal schema', () => {
    it('should have correct tags and summary', () => {
      expect(settingSchema.updatePersonal.tags).toEqual([
        'BO / Settings / Personal, Safety, Themes',
      ]);
      expect(settingSchema.updatePersonal.summary).toBe('Update a personal setting');
    });

    it('should have correct body schema', () => {
      const { properties, required } = settingSchema.updatePersonal.body;

      expect(properties).toHaveProperty('version');
      expect(properties).toHaveProperty('appearance');
      expect(properties).toHaveProperty('recordPerPage');
      expect(properties).toHaveProperty('dateFormat');
      expect(properties).toHaveProperty('timeFormat');
      expect(properties).toHaveProperty('defaultLanguage');
      expect(properties).toHaveProperty('defaultTimezone');

      expect(required).toEqual([
        'version',
        'appearance',
        'recordPerPage',
        'dateFormat',
        'timeFormat',
        'defaultLanguage',
        'defaultTimezone',
      ]);
    });

    it('should have response schema', () => {
      expect(settingSchema.updatePersonal.response).toEqual(CoreSchema.UPDATE_RESPONSE);
    });
  });

  describe('updateSafety schema', () => {
    it('should have correct tags and summary', () => {
      expect(settingSchema.updateSafety.tags).toEqual(['BO / Settings / Personal, Safety, Themes']);
      expect(settingSchema.updateSafety.summary).toBe('Update a safety setting');
    });

    it('should have correct body schema', () => {
      const { properties, required } = settingSchema.updateSafety.body;

      expect(properties).toHaveProperty('version');
      expect(properties).toHaveProperty('sessionLifetimeHours');
      expect(properties).toHaveProperty('passwordExpiryDays');
      expect(properties).toHaveProperty('passwordReuseCount');
      expect(properties).toHaveProperty('passwordMaximumAttempts');
      expect(properties).toHaveProperty('twoFactorSessionTimeoutDays');

      expect(required).toEqual([
        'version',
        'sessionLifetimeHours',
        'passwordExpiryDays',
        'passwordReuseCount',
        'passwordMaximumAttempts',
        'twoFactorSessionTimeoutDays',
      ]);
    });

    it('should have response schema', () => {
      expect(settingSchema.updateSafety.response).toEqual(CoreSchema.UPDATE_RESPONSE);
    });
  });

  describe('options schema', () => {
    it('should have correct tags and summary', () => {
      expect(settingSchema.options.tags).toEqual(['BO / Settings / Personal, Safety, Themes']);
      expect(settingSchema.options.summary).toBe('Get available setting options');
    });

    it('should have correct params properties', () => {
      const { properties } = settingSchema.index.params;
      expect(properties).toHaveProperty('category');
    });

    it('should have response schema', () => {
      expect(settingSchema.options).toHaveProperty('response');
    });
  });
});

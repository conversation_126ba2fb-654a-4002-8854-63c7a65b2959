import * as accessControlSchema from '#src/modules/setting/schemas/access-control.schema.js';
import { describe, expect, it } from 'vitest';
import { AccessControlConstant } from '#src/modules/setting/constants/index.js';
import { REQ_PARAM_UUID } from '#src/modules/core/schemas/core.schema.js';

const { RULE_TYPE, STATUSES } = AccessControlConstant;

describe('Access Control Schema', () => {
  describe('index schema', () => {
    it('should have correct tags and summary', () => {
      const { tags, summary } = accessControlSchema.index;
      expect(tags).toEqual(accessControlSchema.TAGS);
      expect(summary).toBe(`Get a list of ${accessControlSchema.ACCESS_CONTROL_NAME}`);
    });

    it('should have correct querystring properties', () => {
      const { properties } = accessControlSchema.index.querystring;
      expect(properties).toHaveProperty('filter_ruleType_in');
      expect(properties).toHaveProperty('filter_status_in');
      expect(properties).toHaveProperty('filter_validityDate_between');
      expect(properties).toHaveProperty('filter_ipAddress_ipContains');
      expect(properties).toHaveProperty('page');
      expect(properties).toHaveProperty('page');
      expect(properties).toHaveProperty('sortBy');
    });

    it('should have response schema', () => {
      const { properties } = accessControlSchema.index.response[200];
      expect(properties).toHaveProperty('message');
      expect(properties).toHaveProperty('data');
      expect(properties).toHaveProperty('meta');
    });
  });

  describe('view schema', () => {
    it('should have correct tags and summary', () => {
      const { tags, summary } = accessControlSchema.view;
      expect(tags).toEqual(accessControlSchema.TAGS);
      expect(summary).toBe(`View a ${accessControlSchema.ACCESS_CONTROL_NAME}`);
    });

    it('should have params', () => {
      const { params } = accessControlSchema.view;
      expect(params).toEqual(REQ_PARAM_UUID);
    });

    it('should have response schema', () => {
      const { properties } = accessControlSchema.view.response[200];
      expect(properties).toHaveProperty('message');
      expect(properties).toHaveProperty('data');
    });
  });

  describe('create schema', () => {
    it('should have correct tags and summary', () => {
      const { tags, summary } = accessControlSchema.create;
      expect(tags).toEqual(accessControlSchema.TAGS);
      expect(summary).toBe(`Create a ${accessControlSchema.ACCESS_CONTROL_NAME}`);
    });

    it('should have correct body schema', () => {
      const { properties, required } = accessControlSchema.create.body;
      expect(properties).toHaveProperty('ipAddress');
      expect(properties).toHaveProperty('ruleType');
      expect(properties).toHaveProperty('remark');
      expect(properties).toHaveProperty('validityDate');
      expect(properties).toHaveProperty('status');

      expect(required).toEqual(['ruleType', 'ipAddress']);
    });

    it('should have correct ruleType enum', () => {
      const { ruleType } = accessControlSchema.create.body.properties;
      expect(ruleType.enum).toEqual(Object.values(RULE_TYPE));
    });

    it('should have correct status enum', () => {
      const { status } = accessControlSchema.create.body.properties;
      expect(status.enum).toEqual(Object.values(STATUSES));
    });

    it('should have response schema', () => {
      const { properties } = accessControlSchema.create.response[201];
      expect(properties).toHaveProperty('message');
      expect(properties).toHaveProperty('data');
    });
  });

  describe('update schema', () => {
    it('should have correct tags and summary', () => {
      const { tags, summary } = accessControlSchema.update;
      expect(tags).toEqual(accessControlSchema.TAGS);
      expect(summary).toBe(`Update ${accessControlSchema.ACCESS_CONTROL_NAME}`);
    });

    it('should have params', () => {
      const { params } = accessControlSchema.update;
      expect(params).toEqual(REQ_PARAM_UUID);
    });

    it('should have correct body schema', () => {
      const { properties, required } = accessControlSchema.update.body;
      expect(properties).toHaveProperty('ipAddress');
      expect(properties).toHaveProperty('ruleType');
      expect(properties).toHaveProperty('remark');
      expect(properties).toHaveProperty('validityDate');
      expect(required).includes('ipAddress');
    });

    it('should have response schema', () => {
      const { properties } = accessControlSchema.update.response[200];
      expect(properties).toHaveProperty('message');
    });
  });

  describe('updateStatus schema', () => {
    it('should have correct tags and summary', () => {
      const { tags, summary } = accessControlSchema.updateStatus;
      expect(tags).toEqual(accessControlSchema.TAGS);
      expect(summary).toBe(`Update ${accessControlSchema.ACCESS_CONTROL_NAME} status`);
    });

    it('should have params', () => {
      const { params } = accessControlSchema.updateStatus;
      expect(params).toEqual(REQ_PARAM_UUID);
    });

    it('should have correct body schema', () => {
      const { properties, required } = accessControlSchema.updateStatus.body;
      expect(properties).toHaveProperty('status');
      expect(required).includes('status');
    });

    it('should have correct status enum', () => {
      const { status } = accessControlSchema.updateStatus.body.properties;
      expect(status.enum).toEqual(Object.values(STATUSES));
    });

    it('should have response schema', () => {
      const { properties } = accessControlSchema.updateStatus.response[200];
      expect(properties).toHaveProperty('message');
    });
  });
});

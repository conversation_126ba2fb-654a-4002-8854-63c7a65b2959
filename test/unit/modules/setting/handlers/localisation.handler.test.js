import { beforeEach, describe, expect, it, vi } from 'vitest';
import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import {
  index,
  update,
  updateStatus,
  view,
} from '#src/modules/setting/handlers/localisation.handler.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { LocalisationService } from '#src/modules/setting/services/index.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

// Mocks
vi.mock('#src/utils/cache.util.js');
vi.mock('#src/utils/response.util.js');
vi.mock('#src/modules/setting/services/index.js', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    LocalisationService: {
      index: vi.fn(),
      view: vi.fn(),
      update: vi.fn(),
      updateStatus: vi.fn(),
    },
  };
});

describe('Localisation Handler', () => {
  let mockRequest;
  let mockReply;
  const USER = 'UNKNOWN_USER';
  const MODULE = CoreConstant.MODULE_NAMES.LOCALISATION;

  beforeEach(() => {
    mockRequest = {
      params: { id: 'test-id' },
      server: {
        redis: {},
        withAuditLogging: vi.fn().mockResolvedValue(undefined),
      },
      auditEntries: {},
      authInfo: { username: USER },
    };
    mockReply = {};

    vi.clearAllMocks();

    handleServiceResponse.mockImplementation(
      async ({ request, reply, serviceFn, module, method, modelMapping }) => {
        const result = await serviceFn();
        if (request.server?.withAuditLogging) {
          await request.server.withAuditLogging({ request, modelMapping });
        }
        return { status: 200, data: result };
      },
    );
  });

  describe('index', () => {
    it('should handle index with caching and audit', async () => {
      const cacheKey = 'cache_index';
      const mockResult = { rows: [], total: 0 };
      generateCacheKey.mockReturnValue(cacheKey);
      LocalisationService.index.mockResolvedValue(mockResult);
      fetchFromCache.mockImplementation((_, __, cb) => cb());

      await index(mockRequest, mockReply);

      expect(generateCacheKey).toHaveBeenCalledWith(`${MODULE}_index`, mockRequest);
      expect(fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        cacheKey,
        expect.any(Function),
        CoreConstant.CACHE_SECOND.SHORT,
      );
      expect(LocalisationService.index).toHaveBeenCalledWith(mockRequest);
      expect(mockRequest.server.withAuditLogging).toHaveBeenCalled();
      expect(handleServiceResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          module: MODULE,
          method: 'index',
        }),
      );
    });
  });

  describe('view', () => {
    it('should handle view with caching, audit, and modelMapping', async () => {
      const cacheKey = 'cache_view';
      const mockEntry = { id: 'test-id', name: 'Loc1' };
      generateCacheKey.mockReturnValue(cacheKey);
      fetchFromCache.mockResolvedValue(mockEntry);

      await view(mockRequest, mockReply);

      expect(generateCacheKey).toHaveBeenCalledWith(`${MODULE}_view`, mockRequest);
      expect(fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        cacheKey,
        expect.any(Function),
        CoreConstant.CACHE_SECOND.SHORT,
      );
      expect(mockRequest.server.withAuditLogging).toHaveBeenCalledWith(
        expect.objectContaining({
          modelMapping: { Localisation: { beforeState: mockEntry } },
        }),
      );
      expect(handleServiceResponse).toHaveBeenCalled();
    });
  });

  describe('update', () => {
    it('should call update service and handle audit + response', async () => {
      const mockUpdated = { id: 'test-id', value: 'updated' };
      const mockAudit = { beforeState: { id: 'test-id', value: 'old' }, afterState: mockUpdated };

      LocalisationService.update.mockResolvedValue({ result: mockUpdated, audit: mockAudit });

      await update(mockRequest, mockReply);

      expect(LocalisationService.update).toHaveBeenCalledWith(mockRequest, 'test-id');
      expect(mockRequest.server.withAuditLogging).toHaveBeenCalledWith(
        expect.objectContaining({
          modelMapping: { Localisation: mockAudit },
        }),
      );
      expect(handleServiceResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          method: CoreConstant.MODULE_METHODS.UPDATE,
        }),
      );
    });
  });

  describe('updateStatus', () => {
    it('should call updateStatus service and handle audit + response', async () => {
      const mockResult = { id: 'test-id', status: 'inactive' };
      const mockAudit = {
        beforeState: { id: 'test-id', status: 'active' },
        afterState: mockResult,
      };

      LocalisationService.updateStatus.mockResolvedValue({ result: mockResult, audit: mockAudit });

      await updateStatus(mockRequest, mockReply);

      expect(LocalisationService.updateStatus).toHaveBeenCalledWith(mockRequest, 'test-id');
      expect(mockRequest.server.withAuditLogging).toHaveBeenCalledWith(
        expect.objectContaining({
          modelMapping: { Localisation: mockAudit },
        }),
      );
      expect(handleServiceResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          method: CoreConstant.MODULE_METHODS.UPDATE_STATUS,
        }),
      );
    });
  });
});

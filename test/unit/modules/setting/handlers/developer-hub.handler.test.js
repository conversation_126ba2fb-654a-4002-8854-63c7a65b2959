import { beforeEach, describe, expect, it, vi } from 'vitest';

import {
  create,
  index,
  options,
  updateAccessControls,
  updateBasicInformation,
  updatePermissions,
  updateStatus,
  view,
} from '#src/modules/setting/handlers/developer-hub.handler.js';
import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { DeveloperHubService } from '#src/modules/setting/services/index.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

// Mock dependencies
vi.mock('#src/utils/cache.util.js');
vi.mock('#src/utils/response.util.js');
vi.mock('#src/modules/setting/services/index.js');

describe('Developer Hub Handler', () => {
  let mockRequest;
  let mockReply;

  beforeEach(() => {
    mockRequest = {
      server: {
        redis: {},
      },
    };
    mockReply = {};

    // Reset mocks
    vi.resetAllMocks();
  });

  describe('index', () => {
    it('should generate correct cache key and call handleServiceResponse', async () => {
      const mockCacheKey = 'mock_cache_key';
      generateCacheKey.mockReturnValue(mockCacheKey);

      await index(mockRequest, mockReply);

      expect(generateCacheKey).toHaveBeenCalledWith(
        `${CoreConstant.MODULE_NAMES.DEVELOPER_HUB}_${CoreConstant.MODULE_METHODS.INDEX}`,
        mockRequest,
      );
      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.DEVELOPER_HUB,
        method: CoreConstant.MODULE_METHODS.INDEX,
      });

      // Call the serviceFn to verify fetchFromCache is called correctly
      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        CoreConstant.CACHE_SECOND.SHORT,
      );
    });
  });

  describe('view', () => {
    it('should generate correct cache key and call handleServiceResponse', async () => {
      const mockCacheKey = 'mock_cache_key';
      generateCacheKey.mockReturnValue(mockCacheKey);

      await view(mockRequest, mockReply);

      expect(generateCacheKey).toHaveBeenCalledWith(
        `${CoreConstant.MODULE_NAMES.DEVELOPER_HUB}_${CoreConstant.MODULE_METHODS.VIEW}`,
        mockRequest,
      );
      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.DEVELOPER_HUB,
        method: CoreConstant.MODULE_METHODS.VIEW,
      });

      // Call the serviceFn to verify fetchFromCache is called correctly
      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        CoreConstant.CACHE_SECOND.SHORT,
      );
    });
  });

  describe('create', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await create(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: DeveloperHubService.create,
        module: CoreConstant.MODULE_NAMES.DEVELOPER_HUB,
        method: CoreConstant.MODULE_METHODS.CREATE,
      });
    });
  });

  describe('updateBasicInformation', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await updateBasicInformation(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: DeveloperHubService.updateBasicInformation,
        module: CoreConstant.MODULE_NAMES.DEVELOPER_HUB,
        method: CoreConstant.MODULE_METHODS.UPDATE_BASIC_INFORMATION,
      });
    });
  });

  describe('updatePermissions', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await updatePermissions(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: DeveloperHubService.updatePermissions,
        module: CoreConstant.MODULE_NAMES.DEVELOPER_HUB,
        method: CoreConstant.MODULE_METHODS.UPDATE_PERMISSION,
      });
    });
  });

  describe('updateAccessControls', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await updateAccessControls(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: DeveloperHubService.updateAccessControls,
        module: CoreConstant.MODULE_NAMES.DEVELOPER_HUB,
        method: CoreConstant.MODULE_METHODS.UPDATE_ACCESS_CONTROL,
      });
    });
  });

  describe('updateStatus', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await updateStatus(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: DeveloperHubService.updateBasicInformation,
        module: CoreConstant.MODULE_NAMES.DEVELOPER_HUB,
        method: CoreConstant.MODULE_METHODS.UPDATE_STATUS,
      });
    });
  });

  describe('options', () => {
    it('should call handleServiceResponse with correct parameters and return module permissions', async () => {
      await options(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.DEVELOPER_HUB,
        method: CoreConstant.MODULE_METHODS.OPTION,
      });

      // Call the serviceFn to verify the returned data
      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      const result = await serviceFn();

      expect(result).toEqual({
        modulePermissions: expect.arrayContaining([
          expect.objectContaining({
            id: expect.any(String),
            name: expect.any(String),
            availablePermissions: expect.any(Array),
          }),
        ]),
      });
    });
  });
});

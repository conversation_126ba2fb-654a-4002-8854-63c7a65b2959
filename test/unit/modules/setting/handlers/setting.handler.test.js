import { beforeEach, describe, expect, it, vi } from 'vitest';
import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import {
  index,
  options,
  updatePersonal,
  updateSafety,
  updateThemes,
} from '#src/modules/setting/handlers/setting.handler.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { SettingConstant } from '#src/modules/setting/constants/index.js';
import { SettingService } from '#src/modules/setting/services/index.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

// Mock dependencies
vi.mock('#src/utils/cache.util.js');
vi.mock('#src/utils/response.util.js');
vi.mock('#src/modules/setting/services/index.js');
vi.mock('#src/utils/audit-trail.util.js');

describe('Setting Handler', () => {
  let mockRequest;
  let mockReply;

  beforeEach(() => {
    mockRequest = {
      server: {
        redis: {},
        withAuditLogging: vi.fn().mockResolvedValue(undefined),
      },
      params: {
        category: 'personal',
      },
    };

    mockReply = {};

    vi.resetAllMocks();
  });

  describe('index()', () => {
    it('should generate the cache key and return settings from cache or service', async () => {
      const mockCacheKey = 'mock_cache_key';
      generateCacheKey.mockReturnValue(mockCacheKey);

      await index(mockRequest, mockReply);

      expect(generateCacheKey).toHaveBeenCalledWith(
        `${CoreConstant.MODULE_NAMES.SETTING}_${CoreConstant.MODULE_METHODS.INDEX}`,
        mockRequest,
        true,
      );

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.SETTING,
        method: CoreConstant.MODULE_METHODS.INDEX,
      });

      // Simulate cache miss and ensure service fallback
      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      fetchFromCache.mockImplementation((_, __, cb) => cb());
      await serviceFn();

      expect(fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        CoreConstant.CACHE_SECOND.SHORT,
      );

      expect(SettingService.index).toHaveBeenCalledWith(mockRequest, 'personal');
    });
  });

  describe('updatePersonal()', () => {
    it('should call SettingService.update and return result', async () => {
      const mockResult = { result: 'updated', audit: 'audit_info' };
      SettingService.update.mockResolvedValue(mockResult);

      await updatePersonal(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.SETTING,
        method: CoreConstant.MODULE_METHODS.UPDATE_PERSONAL,
      });

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      const result = await serviceFn();
      expect(result).toEqual('updated');

      expect(mockRequest.server.withAuditLogging).toHaveBeenCalledWith({
        request: mockRequest,
        modelMapping: { Setting: 'audit_info' },
        isMultiple: true,
      });

      expect(SettingService.update).toHaveBeenCalledWith(
        mockRequest,
        SettingConstant.SETTING_CATEGORIES.PERSONAL,
      );
    });
  });

  describe('updateSafety()', () => {
    it('should call SettingService.update for safety category', async () => {
      const mockResult = { result: 'updated_safety', audit: 'audit_info' };
      SettingService.update.mockResolvedValue(mockResult);
      mockRequest.params.category = 'safety';

      await updateSafety(mockRequest, mockReply);

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      const result = await serviceFn();
      expect(result).toEqual('updated_safety');

      expect(SettingService.update).toHaveBeenCalledWith(
        mockRequest,
        SettingConstant.SETTING_CATEGORIES.SAFETY,
      );
    });
  });

  describe('updateThemes()', () => {
    it('should call SettingService.update for themes category', async () => {
      const mockResult = { result: 'updated_theme', audit: 'audit_info' };
      SettingService.update.mockResolvedValue(mockResult);
      mockRequest.params.category = 'themes';

      await updateThemes(mockRequest, mockReply);

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      const result = await serviceFn();
      expect(result).toEqual('updated_theme');

      expect(SettingService.update).toHaveBeenCalledWith(
        mockRequest,
        SettingConstant.SETTING_CATEGORIES.THEMES,
      );
    });
  });

  describe('options()', () => {
    it('should retrieve options for the specified category', async () => {
      mockRequest.params.category = 'personal';
      SettingService.list.mockResolvedValue(['opt1', 'opt2']);

      await options(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.SETTING,
        method: CoreConstant.MODULE_METHODS.OPTION,
      });

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      const result = await serviceFn();
      expect(result).toEqual(['opt1', 'opt2']);

      expect(SettingService.list).toHaveBeenCalledWith(mockRequest, 'personal');
    });
  });
});

import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
  create,
  index,
  update,
  updateStatus,
  view,
} from '#src/modules/setting/handlers/access-control.handler.js';
import { fetchFromCache, generateCacheKey } from '#src/utils/cache.util.js';
import { AccessControlService } from '#src/modules/setting/services/index.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { handleServiceResponse } from '#src/utils/response.util.js';

// Mock dependencies
vi.mock('#src/utils/cache.util.js');
vi.mock('#src/utils/response.util.js');
vi.mock('#src/modules/setting/services/index.js');

describe('Access Control Handler', () => {
  let mockRequest;
  let mockReply;

  beforeEach(() => {
    mockRequest = {
      server: {
        redis: {},
      },
    };
    mockReply = {};

    // Reset mocks
    vi.resetAllMocks();
  });

  describe('index', () => {
    it('should generate correct cache key and call handleServiceResponse', async () => {
      const mockCacheKey = 'mock_cache_key';
      generateCacheKey.mockReturnValue(mockCacheKey);

      await index(mockRequest, mockReply);

      expect(generateCacheKey).toHaveBeenCalledWith(
        `${CoreConstant.MODULE_NAMES.ACCESS_CONTROL}_${CoreConstant.MODULE_METHODS.INDEX}`,
        mockRequest,
      );
      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.ACCESS_CONTROL,
        method: CoreConstant.MODULE_METHODS.INDEX,
      });

      // Call the serviceFn to verify fetchFromCache is called correctly
      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        CoreConstant.CACHE_SECOND.SHORT,
      );
    });

    it('should call AccessControlService.index if cache miss', async () => {
      generateCacheKey.mockReturnValue('mock_cache_key');
      fetchFromCache.mockImplementation((redis, key, callback) => callback());

      await index(mockRequest, mockReply);

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(AccessControlService.index).toHaveBeenCalledWith(mockRequest);
    });
  });

  describe('view', () => {
    it('should generate correct cache key and call handleServiceResponse', async () => {
      const mockCacheKey = 'mock_cache_key';
      generateCacheKey.mockReturnValue(mockCacheKey);

      await view(mockRequest, mockReply);

      expect(generateCacheKey).toHaveBeenCalledWith(
        `${CoreConstant.MODULE_NAMES.ACCESS_CONTROL}_${CoreConstant.MODULE_METHODS.VIEW}`,
        mockRequest,
      );
      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: expect.any(Function),
        module: CoreConstant.MODULE_NAMES.ACCESS_CONTROL,
        method: CoreConstant.MODULE_METHODS.VIEW,
      });

      // Call the serviceFn to verify fetchFromCache is called correctly
      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        CoreConstant.CACHE_SECOND.SHORT,
      );
    });

    it('should call AccessControlService.view if cache miss', async () => {
      generateCacheKey.mockReturnValue('mock_cache_key');
      fetchFromCache.mockImplementation((redis, key, callback) => callback());

      await view(mockRequest, mockReply);

      const serviceFn = handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      expect(AccessControlService.view).toHaveBeenCalledWith(mockRequest);
    });
  });

  describe('create', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await create(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: AccessControlService.create,
        module: CoreConstant.MODULE_NAMES.ACCESS_CONTROL,
        method: CoreConstant.MODULE_METHODS.CREATE,
      });
    });
  });

  describe('update', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await update(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: AccessControlService.update,
        module: CoreConstant.MODULE_NAMES.ACCESS_CONTROL,
        method: CoreConstant.MODULE_METHODS.UPDATE,
      });
    });
  });

  describe('updateStatus', () => {
    it('should call handleServiceResponse with correct parameters', async () => {
      await updateStatus(mockRequest, mockReply);

      expect(handleServiceResponse).toHaveBeenCalledWith({
        request: mockRequest,
        reply: mockReply,
        serviceFn: AccessControlService.updateStatus,
        module: CoreConstant.MODULE_NAMES.ACCESS_CONTROL,
        method: CoreConstant.MODULE_METHODS.UPDATE_STATUS,
      });
    });
  });
});

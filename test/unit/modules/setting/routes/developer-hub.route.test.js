import { beforeEach, describe, expect, it, vi } from 'vitest';
import DevelopHubRoute from '#src/modules/setting/routes/developer-hub.route.js';
import { DeveloperHubHandler } from '#src/modules/setting/handlers/index.js';
import { DeveloperHubSchema } from '#src/modules/setting/schemas/index.js';

describe('Developer Hub Route', () => {
  let fastifyMock;

  beforeEach(() => {
    fastifyMock = {
      get: vi.fn(),
      post: vi.fn(),
      patch: vi.fn(),
    };
  });

  const commonAccessConfig = {
    user: true,
    member: false,
    webhook: false,
    public: false,
    ipWhitelist: ['127.0.0.1'],
  };

  it('should register the index route correctly', () => {
    DevelopHubRoute(fastifyMock);

    expect(fastifyMock.get).toHaveBeenCalledWith('/', {
      schema: DeveloperHubSchema.index,
      config: { name: 'developerHub.index', access: commonAccessConfig },
      handler: DeveloperHubHandler.index,
    });
  });

  it('should register the options route correctly', () => {
    DevelopHubRoute(fastifyMock);

    expect(fastifyMock.get).toHaveBeenCalledWith('/options', {
      schema: DeveloperHubSchema.options,
      config: { name: 'developerHub.options', access: commonAccessConfig },
      handler: DeveloperHubHandler.options,
    });
  });

  it('should register the create route correctly', () => {
    DevelopHubRoute(fastifyMock);

    expect(fastifyMock.post).toHaveBeenCalledWith('/', {
      schema: DeveloperHubSchema.create,
      config: { name: 'developerHub.create', access: commonAccessConfig },
      handler: DeveloperHubHandler.create,
    });
  });

  it('should register the view route correctly', () => {
    DevelopHubRoute(fastifyMock);

    expect(fastifyMock.get).toHaveBeenCalledWith('/:id', {
      schema: DeveloperHubSchema.view,
      config: { name: 'developerHub.view', access: commonAccessConfig },
      handler: DeveloperHubHandler.view,
    });
  });

  it('should register the updateBasicInformation route correctly', () => {
    DevelopHubRoute(fastifyMock);

    expect(fastifyMock.patch).toHaveBeenCalledWith('/:id/basic-information', {
      schema: DeveloperHubSchema.updateBasicInformation,
      config: { name: 'developerHub.updateBasicInformation', access: commonAccessConfig },
      handler: DeveloperHubHandler.updateBasicInformation,
    });
  });

  it('should register the updatePermissions route correctly', () => {
    DevelopHubRoute(fastifyMock);

    expect(fastifyMock.patch).toHaveBeenCalledWith('/:id/permissions', {
      schema: DeveloperHubSchema.updatePermissions,
      config: { name: 'developerHub.updatePermissions', access: commonAccessConfig },
      handler: DeveloperHubHandler.updatePermissions,
    });
  });

  it('should register the updateAccessControls route correctly', () => {
    DevelopHubRoute(fastifyMock);

    expect(fastifyMock.patch).toHaveBeenCalledWith('/:id/access-controls', {
      schema: DeveloperHubSchema.updateAccessControls,
      config: { name: 'developerHub.updateAccessControls', access: commonAccessConfig },
      handler: DeveloperHubHandler.updateAccessControls,
    });
  });

  it('should register the updateStatus route correctly', () => {
    DevelopHubRoute(fastifyMock);

    expect(fastifyMock.patch).toHaveBeenCalledWith('/:id/status', {
      schema: DeveloperHubSchema.updateStatus,
      config: { name: 'developerHub.updateStatus', access: commonAccessConfig },
      handler: DeveloperHubHandler.updateStatus,
    });
  });
});

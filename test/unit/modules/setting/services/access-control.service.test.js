/* eslint-disable sonarjs/no-hardcoded-ip */
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import * as ipAccessControlService from '#src/modules/setting/services/access-control.service.js';
import { AccessControlError } from '#src/modules/setting/errors/index.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { IpAccessControlRepository } from '#src/modules/setting/repository/index.js';
import { RemarkRepository } from '#src/modules/core/repository/index.js';
import { withTransaction } from '#src/utils/db-transaction.util.js';

vi.mock('#src/modules/setting/repository/index.js');
vi.mock('#src/modules/core/repository/index.js');
vi.mock('#src/utils/db-transaction.util.js');

const { INACTIVE } = CoreConstant.COMMON_STATUSES;
const {
  REMARKABLE_TYPE: { ACCESS_CONTROL },
  REMARK_TYPE: { NOTE },
} = CoreConstant;

describe('IP Access Control Service', () => {
  let mockRequest;

  beforeEach(() => {
    mockRequest = {
      entity: { id: 'entity1' },
      server: {
        psql: {
          connection: {
            transaction: vi.fn((fn) => fn()),
          },
          Remark: {},
        },
      },
      params: { id: 'ipac1' },
      body: {},
      authInfo: { id: 'user1' },
    };
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('index', () => {
    it('should return IP access control entries with entityId filter', async () => {
      const mockEntries = [{ id: 'ipac1', ipAddress: '***********' }];
      IpAccessControlRepository.findAll.mockResolvedValue({ rows: mockEntries });

      mockRequest.entity = { id: 'entity1' };
      mockRequest.query = { someOtherFilter: 'value' };

      const result = await ipAccessControlService.index(mockRequest);

      expect(result).toEqual({ rows: mockEntries });
      expect(IpAccessControlRepository.findAll).toHaveBeenCalledWith(mockRequest.server, {
        someOtherFilter: 'value',
        filter_parentId_eq: 'entity1',
      });
    });

    it('should merge entityId filter with existing query filters', async () => {
      const mockEntries = [{ id: 'ipac1', ipAddress: '***********' }];
      IpAccessControlRepository.findAll.mockResolvedValue({ rows: mockEntries });

      mockRequest.entity = { id: 'entity1' };
      mockRequest.query = { filter_status_eq: 'ACTIVE' };

      const result = await ipAccessControlService.index(mockRequest);

      expect(result).toEqual({ rows: mockEntries });
      expect(IpAccessControlRepository.findAll).toHaveBeenCalledWith(mockRequest.server, {
        filter_status_eq: 'ACTIVE',
        filter_parentId_eq: 'entity1',
      });
    });
  });

  describe('view', () => {
    it('should return IP access control entry', async () => {
      const mockEntry = { id: 'ipac1', ipAddress: '***********' };
      IpAccessControlRepository.findById.mockResolvedValue(mockEntry);

      const result = await ipAccessControlService.view(mockRequest);

      expect(result).toEqual(mockEntry);
    });

    it('should throw error if entry not found', async () => {
      IpAccessControlRepository.findById.mockResolvedValue(null);

      await expect(ipAccessControlService.view(mockRequest)).rejects.toThrow(
        AccessControlError.notFound('ipac1'),
      );
    });
  });

  describe('create', () => {
    it('should create IP access control entry with remark', async () => {
      const mockCreatedEntry = { id: 'ipac1', ipAddress: '***********', reload: vi.fn() };
      IpAccessControlRepository.create.mockResolvedValue(mockCreatedEntry);
      RemarkRepository.create.mockResolvedValue({ id: 'remark1' });
      withTransaction.mockImplementation((_, __, fn) => fn({ transaction: {} }));

      mockRequest.body = { ipAddress: '***********', remark: 'Test remark' };
      const result = await ipAccessControlService.create(mockRequest);

      expect(result).toEqual(mockCreatedEntry);
      expect(IpAccessControlRepository.create).toHaveBeenCalled();
      expect(RemarkRepository.create).toHaveBeenCalled();
      expect(mockCreatedEntry.reload).toHaveBeenCalled();
    });

    it('should create IP access control entry without remark', async () => {
      const mockCreatedEntry = { id: 'ipac1', ipAddress: '***********' };
      IpAccessControlRepository.create.mockResolvedValue(mockCreatedEntry);
      withTransaction.mockImplementation((_, __, fn) => fn({ transaction: {} }));

      mockRequest.body = { ipAddress: '***********' };
      const result = await ipAccessControlService.create(mockRequest);

      expect(result).toEqual(mockCreatedEntry);
      expect(IpAccessControlRepository.create).toHaveBeenCalled();
      expect(RemarkRepository.create).not.toHaveBeenCalled();
    });
  });

  describe('update', () => {
    it('should update validityDate when set', async () => {
      const mockExistingEntry = {
        id: 'ipac1',
        ipAddress: '***********',
        activeRemark: { id: 'remark1', content: 'Old remark' },
      };
      const mockUpdatedEntry = {
        ...mockExistingEntry,
        ipAddress: '***********',
        validityDate: '2025-01-01 12:12:12',
      };
      IpAccessControlRepository.findById.mockResolvedValue(mockExistingEntry);
      IpAccessControlRepository.update.mockResolvedValue(mockUpdatedEntry);
      RemarkRepository.findActiveByRemarkable.mockResolvedValue({ id: 'remark1' });
      withTransaction.mockImplementation((_, __, fn) => fn({ transaction: {} }));

      mockRequest.body = { ipAddress: '***********', validityDate: '2025-01-01 12:12:12' };
      await ipAccessControlService.update(mockRequest);

      expect(IpAccessControlRepository.update).toHaveBeenCalledWith(
        mockExistingEntry,
        { ipAddress: '***********', validityDate: '2025-01-01 12:12:12' },
        expect.objectContaining({
          transaction: expect.anything(),
          authInfoId: 'user1',
        }),
      );
    });

    it('should not update remark when it is same', async () => {
      const mockExistingEntry = {
        id: 'ipac1',
        ipAddress: '***********',
        activeRemark: { id: 'remark1', content: 'No changes' },
      };
      const mockUpdatedEntry = {
        ...mockExistingEntry,
        ipAddress: '***********',
        validityDate: '2025-01-01 12:12:12',
      };
      IpAccessControlRepository.findById.mockResolvedValue(mockExistingEntry);
      IpAccessControlRepository.update.mockResolvedValue(mockUpdatedEntry);
      RemarkRepository.findActiveByRemarkable.mockResolvedValue({ id: 'remark1' });
      mockRequest.body = { remark: 'No changes' };
      await ipAccessControlService.update(mockRequest);
      expect(RemarkRepository.create).not.toHaveBeenCalled();
    });

    it('should create a new remark when content changes', async () => {
      const mockExistingEntry = {
        id: 'ipac1',
        ipAddress: '***********',
        activeRemark: { id: 'remark1', content: 'Old remark' },
      };
      const mockUpdatedEntry = {
        ...mockExistingEntry,
        ipAddress: '***********',
      };
      IpAccessControlRepository.findById.mockResolvedValue(mockExistingEntry);
      IpAccessControlRepository.update.mockResolvedValue(mockUpdatedEntry);
      RemarkRepository.create.mockResolvedValue({ id: 'newRemark1', content: 'New remark' });
      withTransaction.mockImplementation((_, __, fn) => fn({ transaction: {} }));

      mockRequest.body = { ipAddress: '***********', remark: 'New remark' };
      await ipAccessControlService.update(mockRequest);

      expect(RemarkRepository.create).toHaveBeenCalledWith(
        mockRequest.server,
        {
          remarkableId: 'ipac1',
          remarkableType: 'ip_access_control',
          type: 'note',
          content: 'New remark',
        },
        {
          transaction: expect.anything(),
          authInfoId: 'user1',
        },
      );
    });
  });

  describe('updateStatus', () => {
    it('should update status of IP access control entry', async () => {
      const mockExistingEntry = { id: 'ipac1', ipAddress: '***********', status: 'ACTIVE' };
      const mockUpdatedEntry = { ...mockExistingEntry, status: INACTIVE };
      IpAccessControlRepository.findById.mockResolvedValue(mockExistingEntry);
      IpAccessControlRepository.update.mockResolvedValue(mockUpdatedEntry);

      mockRequest.body = { status: INACTIVE };
      const result = await ipAccessControlService.updateStatus(mockRequest);

      expect(result).toEqual(mockUpdatedEntry);
      expect(IpAccessControlRepository.update).toHaveBeenCalledWith(
        mockExistingEntry,
        { status: INACTIVE },
        { authInfoId: 'user1' },
      );
    });
  });

  describe('bulkManage', () => {
    beforeEach(() => {
      mockRequest = {
        body: {
          parentId: 'parent123',
          ruleType: 'ALLOW',
          ipAccessControls: [
            { ipAddress: '***********', remark: 'Office IP' },
            { ipAddress: '********', remark: 'VPN IP' },
          ],
        },
        server: { psql: {} },
        authInfo: { id: 'user123' },
        entity: { id: 'entity123' },
      };

      // Reset mocks
      vi.resetAllMocks();

      // Mock the withTransaction function
      withTransaction.mockImplementation((server, options, callback) =>
        callback({ commit: vi.fn() }),
      );

      // Mock the IpAccessControlRepository.removeAll method
      IpAccessControlRepository.removeAll.mockResolvedValue();

      // Mock the create function
      vi.spyOn(ipAccessControlService, 'create').mockResolvedValue({});
    });

    it('should handle empty ipAccessControls array', async () => {
      mockRequest.body.ipAccessControls = [];

      await ipAccessControlService.bulkManage(mockRequest);

      expect(IpAccessControlRepository.removeAll).toHaveBeenCalled();
      expect(ipAccessControlService.create).not.toHaveBeenCalled();
    });

    it('should use withTransaction for database operations', async () => {
      await ipAccessControlService.bulkManage(mockRequest);

      expect(withTransaction).toHaveBeenCalledWith(mockRequest.server, {}, expect.any(Function));
    });

    it('should throw an error if removeAll fails', async () => {
      const error = new Error('Database error');
      IpAccessControlRepository.removeAll.mockRejectedValue(error);

      await expect(ipAccessControlService.bulkManage(mockRequest)).rejects.toThrow(
        'Database error',
      );
    });
  });
});

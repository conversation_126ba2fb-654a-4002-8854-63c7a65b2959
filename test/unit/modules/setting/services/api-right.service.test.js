import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import * as apiRightService from '#src/modules/setting/services/api-right.service.js';
import { ApiRightRepository } from '#src/modules/setting/repository/index.js';

vi.mock('#src/modules/setting/repository/index.js');
vi.mock('#src/modules/core/constants/index.js', () => ({
  CoreConstant: {
    PERMISSION_FIELDS: ['canRead', 'canWrite', 'canDelete'],
  },
}));

describe('API Right Service', () => {
  let mockRequest;

  beforeEach(() => {
    mockRequest = {
      body: {
        apiRights: [
          {
            moduleId: 'module1',
            permissions: ['canRead', 'canWrite'],
          },
        ],
        parentId: 'parent1',
      },
      server: {},
      authInfo: { id: 'user1' },
    };
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('create', () => {
    it('should create API rights', async () => {
      const mockCreatedRights = [{ id: 'ar1', moduleId: 'module1' }];
      ApiRightRepository.upsert.mockResolvedValue(mockCreatedRights);

      const result = await apiRightService.create(mockRequest);

      expect(result).toEqual(mockCreatedRights);
      expect(ApiRightRepository.upsert).toHaveBeenCalledWith(
        mockRequest.server,
        [
          {
            parentId: 'parent1',
            moduleId: 'module1',
            canRead: true,
            canWrite: true,
            canDelete: false,
          },
        ],
        { transaction: undefined, authInfoId: 'user1' },
      );
    });

    it('should handle single API right object', async () => {
      mockRequest.body.apiRights = {
        moduleId: 'module1',
        permissions: ['canRead'],
      };
      const mockCreatedRight = { id: 'ar1', moduleId: 'module1' };
      ApiRightRepository.upsert.mockResolvedValue([mockCreatedRight]);

      const result = await apiRightService.create(mockRequest);

      expect(result).toEqual([mockCreatedRight]);
      expect(ApiRightRepository.upsert).toHaveBeenCalledWith(
        mockRequest.server,
        [
          {
            parentId: 'parent1',
            moduleId: 'module1',
            canRead: true,
            canWrite: false,
            canDelete: false,
          },
        ],
        { transaction: undefined, authInfoId: 'user1' },
      );
    });
  });

  describe('update', () => {
    it('should update API rights and remove unused ones', async () => {
      const mockUpdatedRights = [{ id: 'ar1', moduleId: 'module1' }];
      ApiRightRepository.upsert.mockResolvedValue(mockUpdatedRights);
      ApiRightRepository.remove.mockResolvedValue(1);

      const result = await apiRightService.update(mockRequest);

      expect(result).toEqual(mockUpdatedRights);
      expect(ApiRightRepository.upsert).toHaveBeenCalledWith(
        mockRequest.server,
        [
          {
            parentId: 'parent1',
            moduleId: 'module1',
            canRead: true,
            canWrite: true,
            canDelete: false,
          },
        ],
        { transaction: undefined, authInfoId: 'user1' },
      );
      expect(ApiRightRepository.remove).toHaveBeenCalledWith(
        mockRequest.server,
        'parent1',
        ['module1'],
        { transaction: undefined },
      );
    });

    it('should handle transaction option', async () => {
      const mockTransaction = {};
      const options = { transaction: mockTransaction };

      await apiRightService.update(mockRequest, options);

      expect(ApiRightRepository.upsert).toHaveBeenCalledWith(
        mockRequest.server,
        expect.any(Array),
        { transaction: mockTransaction, authInfoId: 'user1' },
      );
      expect(ApiRightRepository.remove).toHaveBeenCalledWith(
        mockRequest.server,
        'parent1',
        ['module1'],
        { transaction: mockTransaction },
      );
    });
  });
});

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { Op } from 'sequelize';

import * as apiRightRepository from '#src/modules/setting/repository/api-right.repository.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';

const { PERMISSION_FIELDS } = CoreConstant;

describe('API Right Repository', () => {
  let mockFastify;

  beforeEach(() => {
    mockFastify = {
      psql: {
        ApiRight: {
          findOrCreate: vi.fn(),
          destroy: vi.fn(),
        },
      },
    };
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('upsert', () => {
    it('should create new ApiRight if not exists', async () => {
      const createData = [{ parentId: 'p1', moduleId: 'm1', view: true, edit: false }];

      const mockApiRight = { update: vi.fn() };

      mockFastify.psql.ApiRight.findOrCreate.mockResolvedValue([mockApiRight, true]);

      const result = await apiRightRepository.upsert(mockFastify, createData);

      expect(mockFastify.psql.ApiRight.findOrCreate).toHaveBeenCalledWith({
        where: { parentId: 'p1', moduleId: 'm1' },
        defaults: createData[0],
      });

      expect(mockApiRight.update).not.toHaveBeenCalled();
      expect(result).toEqual([mockApiRight]);
    });

    it('should update existing ApiRight if permission fields exist', async () => {
      const createData = [{ parentId: 'p1', moduleId: 'm1', canView: true, canEdit: false }];

      const update = vi.fn();

      mockFastify.psql.ApiRight.findOrCreate.mockResolvedValue([{ update }, false]);

      const result = await apiRightRepository.upsert(mockFastify, createData);

      const expectedUpdate = PERMISSION_FIELDS.reduce((acc, field) => {
        if (createData[0][field] !== undefined) {
          acc[field] = createData[0][field];
        }
        return acc;
      }, {});

      expect(update).toHaveBeenCalledWith(expectedUpdate, {});
      expect(result).toHaveLength(1);
    });

    it('should skip update if no permission fields found', async () => {
      const createData = [{ parentId: 'p2', moduleId: 'm2' }];

      const update = vi.fn();

      mockFastify.psql.ApiRight.findOrCreate.mockResolvedValue([{ update }, false]);

      const result = await apiRightRepository.upsert(mockFastify, createData);

      expect(update).not.toHaveBeenCalled();
      expect(result).toHaveLength(1);
    });
  });

  describe('remove', () => {
    it('should destroy ApiRights not in provided module list', async () => {
      const parentId = 'abc';
      const moduleIds = ['mod1', 'mod2'];

      mockFastify.psql.ApiRight.destroy.mockResolvedValue(3);

      const result = await apiRightRepository.remove(mockFastify, parentId, moduleIds);

      expect(mockFastify.psql.ApiRight.destroy).toHaveBeenCalledWith({
        where: {
          parentId,
          moduleId: { [Op.notIn]: moduleIds },
        },
      });

      expect(result).toBe(3);
    });
  });
});

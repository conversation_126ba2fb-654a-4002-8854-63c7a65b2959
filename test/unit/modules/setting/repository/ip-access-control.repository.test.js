/* eslint-disable sonarjs/no-hardcoded-ip */
import { Op, literal } from 'sequelize';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import * as ipAccessControlRepository from '#src/modules/setting/repository/ip-access-control.repository.js';
import { applyOffsetPagination } from '#src/utils/pagination.util.js';
import { buildWhereFromFilters } from '#src/utils/query.util.js';

vi.mock('#src/utils/pagination.util.js');
vi.mock('#src/utils/query.util.js', () => ({
  buildWhereFromFilters: vi.fn(),
}));
vi.mock('#src/modules/core/validation/index.js');

// Mock the literal function from Sequelize
vi.mock('sequelize', () => ({
  Op: {
    and: Symbol('and'),
    in: Symbol('in'),
    ne: Symbol('ne'),
  },
  literal: vi.fn((str) => str),
}));

describe('IP Access Control Repository', () => {
  let mockFastify;

  beforeEach(() => {
    mockFastify = {
      psql: {
        IpAccessControl: {
          findByPk: vi.fn(),
          findOne: vi.fn(),
          create: vi.fn(),
          findAll: vi.fn(),
          sequelize: {
            literal: vi.fn((str) => str),
          },
        },
        Remark: {},
      },
    };
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('findAll', () => {
    it('should call applyOffsetPagination with correct parameters', async () => {
      const mockQuery = {
        page: 1,
        limit: 10,
        filter_status_in: 'active',
      };

      const mockWhereFilter = { status: { [Op.in]: 'active' } };
      const mockIncludeFilter = [
        {
          model: mockFastify.psql.Remark,
          association: 'activeRemark',
          required: false,
        },
      ];

      buildWhereFromFilters.mockReturnValue({
        where: mockWhereFilter,
        include: mockIncludeFilter,
      });

      await ipAccessControlRepository.findAll(mockFastify, mockQuery);

      expect(buildWhereFromFilters).toHaveBeenCalledWith(
        mockQuery,
        mockFastify.psql.IpAccessControl,
        [
          {
            model: mockFastify.psql.Remark,
            association: 'activeRemark',
            required: false,
          },
        ],
      );

      expect(applyOffsetPagination).toHaveBeenCalledWith(
        mockFastify,
        mockFastify.psql.IpAccessControl,
        mockQuery,
        mockWhereFilter,
        mockIncludeFilter,
      );
    });
  });

  describe('findById', () => {
    it('should call IpAccessControl.findByPk with correct parameters', async () => {
      const id = '123';

      await ipAccessControlRepository.findById(mockFastify, id);

      expect(mockFastify.psql.IpAccessControl.findByPk).toHaveBeenCalledWith(id, {
        include: [
          {
            model: mockFastify.psql.Remark,
            as: 'activeRemark',
            required: false,
          },
        ],
      });
    });
  });

  describe('checkOverlap', () => {
    it('should return false if no overlap is found', async () => {
      const parentId = '123';
      const ipOrRange = '***********/24';

      mockFastify.psql.IpAccessControl.findOne.mockResolvedValue(null);

      const result = await ipAccessControlRepository.checkOverlap(mockFastify, parentId, ipOrRange);

      expect(result).toBe(false);
    });

    it('should handle errors gracefully', async () => {
      const parentId = '123';
      const ipOrRange = '***********/24';

      mockFastify.psql.IpAccessControl.findOne.mockRejectedValue(new Error('Database error'));

      await expect(
        ipAccessControlRepository.checkOverlap(mockFastify, parentId, ipOrRange),
      ).rejects.toThrow('Database error');
    });

    it('should return true if an overlap is found with a matching IP range', async () => {
      const parentId = '123';
      const ipOrRange = '***********/24';

      mockFastify.psql.IpAccessControl.findOne.mockResolvedValue({ id: '456' });

      const result = await ipAccessControlRepository.checkOverlap(mockFastify, parentId, ipOrRange);

      expect(result).toBe(true);
      expect(mockFastify.psql.IpAccessControl.findOne).toHaveBeenCalledWith({
        where: {
          parent_id: parentId,
          status: 'active',
          [Op.and]: [
            literal(`ip_address <<= '0.0.0.0/0'::inet`),
            literal(`ip_address && '${ipOrRange}'::inet`),
          ],
        },
      });
    });

    it('should correctly apply the family filter for ipv6 addresses', async () => {
      const parentId = '123';
      const ipOrRange = '2001:0db8:85a3:0000:0000:8a2e:0370:7334/64';
      const mockResult = { id: '789' };

      mockFastify.psql.IpAccessControl.findOne.mockResolvedValue(mockResult);

      const result = await ipAccessControlRepository.checkOverlap(mockFastify, parentId, ipOrRange);

      expect(result).toBe(true);
      expect(mockFastify.psql.IpAccessControl.findOne).toHaveBeenCalledWith({
        where: {
          parent_id: parentId,
          status: 'active',
          [Op.and]: [
            literal(`ip_address <<= '::/0'::inet`),
            literal(`ip_address && '${ipOrRange}'::inet`),
          ],
        },
      });
    });

    it('should return false if the IP address is not within the specified range', async () => {
      const parentId = '123';
      const ipOrRange = '10.0.0.0/24';
      const id = '456';

      mockFastify.psql.IpAccessControl.findOne.mockResolvedValue(null);

      const result = await ipAccessControlRepository.checkOverlap(
        mockFastify,
        parentId,
        ipOrRange,
        id,
      );

      expect(result).toBe(false);
      expect(mockFastify.psql.IpAccessControl.findOne).toHaveBeenCalledWith({
        where: {
          parent_id: parentId,
          status: 'active',
          [Op.and]: [
            literal(`ip_address <<= '0.0.0.0/0'::inet`),
            literal(`ip_address && '${ipOrRange}'::inet`),
          ],
          id: { [Op.ne]: id },
        },
      });
    });

    it('should correctly handle the case when id is provided and matches an existing record', async () => {
      const parentId = '123';
      const ipOrRange = '***********/24';
      const id = '456';
      const mockResult = { id: '456' };

      mockFastify.psql.IpAccessControl.findOne.mockResolvedValue(mockResult);

      const result = await ipAccessControlRepository.checkOverlap(
        mockFastify,
        parentId,
        ipOrRange,
        id,
      );

      expect(result).toBe(true);
      expect(mockFastify.psql.IpAccessControl.findOne).toHaveBeenCalledWith({
        where: {
          parent_id: parentId,
          status: 'active',
          [Op.and]: [
            literal(`ip_address <<= '0.0.0.0/0'::inet`),
            literal(`ip_address && '${ipOrRange}'::inet`),
          ],
          id: { [Op.ne]: id },
        },
      });
    });

    it('should correctly handle the case when id is provided and does not match any existing record', async () => {
      const parentId = '123';
      const ipOrRange = '***********/24';
      const id = '789'; // ID that does not match any existing record

      mockFastify.psql.IpAccessControl.findOne.mockResolvedValue(null);

      const result = await ipAccessControlRepository.checkOverlap(
        mockFastify,
        parentId,
        ipOrRange,
        id,
      );

      expect(result).toBe(false);
      expect(mockFastify.psql.IpAccessControl.findOne).toHaveBeenCalledWith({
        where: {
          parent_id: parentId,
          status: 'active',
          [Op.and]: [
            literal(`ip_address <<= '0.0.0.0/0'::inet`),
            literal(`ip_address && '${ipOrRange}'::inet`),
          ],
          id: { [Op.ne]: id },
        },
      });
    });

    it('should return false if the parentId does not match any active records', async () => {
      const parentId = 'nonexistent';
      const ipOrRange = '***********/24';

      mockFastify.psql.IpAccessControl.findOne.mockResolvedValue(null);

      const result = await ipAccessControlRepository.checkOverlap(mockFastify, parentId, ipOrRange);

      expect(result).toBe(false);
      expect(mockFastify.psql.IpAccessControl.findOne).toHaveBeenCalledWith({
        where: {
          parent_id: parentId,
          status: 'active',
          [Op.and]: [
            literal(`ip_address <<= '0.0.0.0/0'::inet`),
            literal(`ip_address && '${ipOrRange}'::inet`),
          ],
        },
      });
    });

    it('should correctly parse and handle IP addresses without a CIDR suffix', async () => {
      const parentId = '123';
      const ipOrRange = '***********'; // IP address without CIDR suffix
      const mockResult = { id: '456' };

      mockFastify.psql.IpAccessControl.findOne.mockResolvedValue(mockResult);

      const result = await ipAccessControlRepository.checkOverlap(mockFastify, parentId, ipOrRange);

      expect(result).toBe(true);
      expect(mockFastify.psql.IpAccessControl.findOne).toHaveBeenCalledWith({
        where: {
          parent_id: parentId,
          status: 'active',
          [Op.and]: [
            literal(`ip_address <<= '0.0.0.0/0'::inet`),
            literal(`ip_address && '${ipOrRange}'::inet`),
          ],
        },
      });
    });

    it('should handle cases where the ipOrRange is an invalid IP address format', async () => {
      const parentId = '123';
      const invalidIpOrRange = 'invalid-ip-address';

      await expect(
        ipAccessControlRepository.checkOverlap(mockFastify, parentId, invalidIpOrRange),
      ).rejects.toThrow('ipaddr: the address has neither IPv6 nor IPv4 format');
    });

    it('should correctly apply the family filter for ipv4 addresses', async () => {
      const parentId = '123';
      const ipOrRange = '***********/24';
      const mockResult = { id: '789' };

      mockFastify.psql.IpAccessControl.findOne.mockResolvedValue(mockResult);

      const result = await ipAccessControlRepository.checkOverlap(mockFastify, parentId, ipOrRange);

      expect(result).toBe(true);
      expect(mockFastify.psql.IpAccessControl.findOne).toHaveBeenCalledWith({
        where: {
          parent_id: parentId,
          status: 'active',
          [Op.and]: [
            literal(`ip_address <<= '0.0.0.0/0'::inet`),
            literal(`ip_address && '${ipOrRange}'::inet`),
          ],
        },
      });
    });
  });

  describe('create', () => {
    it('should call IpAccessControl.create with correct parameters', async () => {
      const data = { parentId: '789', ruleType: 'allowlist', ipAddress: '********' };
      const options = { transaction: {} };

      await ipAccessControlRepository.create(mockFastify, data, options);

      expect(mockFastify.psql.IpAccessControl.create).toHaveBeenCalledWith(data, options);
    });
  });

  describe('update', () => {
    it('should call update method on model with correct parameters', async () => {
      const mockModelData = {
        update: vi.fn(),
      };
      const updateData = { ruleType: 'allowlist' };
      const options = { userId: '123' };

      await ipAccessControlRepository.update(mockModelData, updateData, options);

      expect(mockModelData.update).toHaveBeenCalledWith(updateData, options);
    });
  });

  describe('removeAll', () => {
    it('should call IpAccessControl.destroy with correct parameters', async () => {
      const parentId = '123';
      const options = { transaction: {} };

      mockFastify.psql.IpAccessControl.destroy = vi.fn().mockResolvedValue(5); // Assuming 5 records were deleted

      const result = await ipAccessControlRepository.removeAll(mockFastify, parentId, options);

      expect(mockFastify.psql.IpAccessControl.destroy).toHaveBeenCalledWith({
        where: {
          parentId,
        },
        individualHooks: true,
        transaction: {},
      });

      expect(result).toBe(5);
    });

    it('should call IpAccessControl.destroy without additional options when not provided', async () => {
      const parentId = '123';

      mockFastify.psql.IpAccessControl.destroy = vi.fn().mockResolvedValue(3);

      await ipAccessControlRepository.removeAll(mockFastify, parentId);

      expect(mockFastify.psql.IpAccessControl.destroy).toHaveBeenCalledWith({
        where: {
          parentId,
        },
        individualHooks: true,
      });
    });

    it('should handle errors and reject the promise', async () => {
      const parentId = '123';
      const error = new Error('Database error');

      mockFastify.psql.IpAccessControl.destroy = vi.fn().mockRejectedValue(error);

      await expect(ipAccessControlRepository.removeAll(mockFastify, parentId)).rejects.toThrow(
        'Database error',
      );
    });
  });
});

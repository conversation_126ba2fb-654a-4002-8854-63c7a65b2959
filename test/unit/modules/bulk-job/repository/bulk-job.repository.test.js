import { afterEach, describe, expect, it, vi } from 'vitest';
import { create } from '#src/modules/bulk-job/repository/bulk-job.repository.js';

describe('Test case for BulkJob repository', () => {
  const fastifyMock = {
    psql: {
      BulkJob: {
        create: vi.fn(),
      },
    },
  };
  const dataMock = { type: 'bulk', model: 'TestModel' };
  const userMock = { id: 'user-123' };

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should call fastify.psql.BulkJob.create with the correct parameters and return the result', async () => {
    const expectedResult = { id: 1, ...dataMock };
    fastifyMock.psql.BulkJob.create.mockResolvedValue(expectedResult);

    const result = await create(fastifyMock, dataMock, userMock);

    expect(fastifyMock.psql.BulkJob.create).toHaveBeenCalledWith(dataMock, { userId: userMock.id });
    expect(result).toEqual(expectedResult);
  });

  it('should return an error object if fastify.psql.BulkJob.create throws', async () => {
    const errorMessage = 'Database error';
    fastifyMock.psql.BulkJob.create.mockRejectedValue(new Error(errorMessage));

    const result = await create(fastifyMock, dataMock, userMock);

    expect(fastifyMock.psql.BulkJob.create).toHaveBeenCalledWith(dataMock, { userId: userMock.id });
    expect(result).toEqual({ error: errorMessage });
  });
});

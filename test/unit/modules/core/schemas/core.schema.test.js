import {
  COMMON_HEADERS,
  COMMON_PROPERTIES,
  ERROR_RESPONSE,
  OFFSET_PAGINATION_META_PROPERTIES,
  OFFSET_PAGINATION_QUERY_PARAMS,
  REQ_PARAM_UUID,
  UPDATE_RESPONSE,
  VIEW_RESPONSE,
  createOffsetPaginationResponseSchema,
} from '#src/modules/core/schemas/core.schema.js';
import { describe, expect, it } from 'vitest';
import ajv from '#src/utils/validation.util.js';

describe('Core Schema', () => {
  describe('COMMON_PROPERTIES', () => {
    it('should have the correct structure', () => {
      expect(COMMON_PROPERTIES).toEqual({
        id: { type: 'string', format: 'uuid' },
        createdAt: { type: 'string', format: 'date-time' },
        createdBy: { type: 'string', format: 'uuid' },
        updatedAt: { type: 'string', format: 'date-time' },
        updatedBy: { type: 'string', format: 'uuid' },
      });
    });
  });

  describe('ERROR_RESPONSE', () => {
    it('should have the correct structure', () => {
      expect(ERROR_RESPONSE).toEqual({
        '4xx': { $ref: 'ErrorResponse' },
        '5xx': { $ref: 'ErrorResponse' },
      });
    });
  });

  describe('OFFSET_PAGINATION_META_PROPERTIES', () => {
    it('should have the correct structure', () => {
      expect(OFFSET_PAGINATION_META_PROPERTIES).toEqual({
        totalCount: { type: 'integer' },
        totalPages: { type: 'integer' },
        currentPage: { type: 'integer' },
        limit: { type: 'integer' },
      });
    });
  });

  describe('OFFSET_PAGINATION_QUERY_PARAMS', () => {
    it('should have the correct structure', () => {
      expect(OFFSET_PAGINATION_QUERY_PARAMS).toEqual({
        page: { type: 'string', description: 'Current page number', default: 1 },
        limit: { type: 'string', description: 'Items per page', default: 25 },
        sortBy: {
          oneOf: [{ type: 'string' }, { type: 'array' }],
          description: 'Sorting field in the format (field:order) (e.g., name:asc)',
        },
      });
    });
  });

  describe('REQ_PARAM_UUID', () => {
    it('should have the correct structure', () => {
      expect(REQ_PARAM_UUID).toEqual({
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' },
        },
        required: ['id'],
      });
    });
  });

  describe('UPDATE_RESPONSE', () => {
    it('should have the correct structure', () => {
      expect(UPDATE_RESPONSE).toEqual({
        200: {
          description: 'Success response',
          type: 'object',
          properties: {
            message: { type: 'string' },
          },
        },
        ...ERROR_RESPONSE,
      });
    });
  });

  describe('VIEW_RESPONSE', () => {
    it('should return the correct structure', () => {
      const customProperties = { customField: { type: 'string' } };
      const result = VIEW_RESPONSE(customProperties);
      expect(result).toEqual({
        200: {
          description: 'Success response',
          type: 'object',
          properties: {
            message: { type: 'string' },
            data: customProperties,
          },
        },
        ...ERROR_RESPONSE,
      });
    });
  });

  describe('createOffsetPaginationResponseSchema', () => {
    it('should return the correct structure', () => {
      const result = createOffsetPaginationResponseSchema();
      expect(result).toEqual({
        type: 'object',
        properties: {
          pagination: {
            type: 'object',
            properties: OFFSET_PAGINATION_META_PROPERTIES,
          },
        },
      });
    });

    it('should return the correct structure with additional properties if provided', () => {
      const additionalProperties = { extraField: { type: 'string' } };
      const result = createOffsetPaginationResponseSchema(additionalProperties);
      expect(result).toEqual({
        type: 'object',
        properties: {
          pagination: {
            type: 'object',
            properties: OFFSET_PAGINATION_META_PROPERTIES,
          },
          extraField: { type: 'string' },
        },
      });
    });
  });

  describe('COMMON_HEADERS', () => {
    it('should have the correct structure', () => {
      const isSchemaValid = ajv.validateSchema(COMMON_HEADERS);

      expect(isSchemaValid).toBe(true);

      expect(COMMON_HEADERS).toEqual({
        type: 'object',
        properties: {
          'Accept-Language': {
            type: 'string',
            description: 'Language of the response',
            enum: ['en', 'zh'],
            default: 'en',
          },
          'X-Forwarded-For': {
            type: 'string',
            oneOf: [{ format: 'ipv4' }, { format: 'ipv6' }],
            description: 'Simulated client IP address for testing (e.g. *************)',
          },
        },
      });
    });
  });
});

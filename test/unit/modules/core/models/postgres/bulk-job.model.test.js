import { beforeAll, describe, expect, it, vi } from 'vitest';
import { BulkJobConstant } from '#src/modules/bulk-job/constants/index.js';
import BulkJobModel from '#src/modules/core/models/postgres/bulk-job.model.js';
import { Sequelize } from 'sequelize';

// Mock mixins
vi.mock('#src/mixins/index.js', () => {
  const mockApplyAuditFields = vi.fn();
  return {
    auditableMixin: {
      applyAuditFields: mockApplyAuditFields,
    },
    __mocks: {
      mockApplyAuditFields,
    },
  };
});

describe('BulkJobModel', () => {
  let sequelize;
  let BulkJob;

  beforeAll(async () => {
    sequelize = new Sequelize('sqlite::memory:', { logging: false });
    const mockFastify = { psql: { connection: sequelize } };
    BulkJob = BulkJobModel(mockFastify);
  });

  it('should initialize BulkJob model', () => {
    expect(BulkJob).toBeDefined();
    expect(BulkJob.tableName).toBe('bulk_jobs');
  });

  it('should have required attributes defined', () => {
    const attrs = BulkJob.rawAttributes;

    expect(attrs.id).toBeDefined();
    expect(attrs.entityId).toBeDefined();
    expect(attrs.type).toBeDefined();
    expect(attrs.dryRun).toBeDefined();
    expect(attrs.title).toBeDefined();
    expect(attrs.parameters).toBeDefined();
    expect(attrs.model).toBeDefined();
    expect(attrs.fileName).toBeDefined();
    expect(attrs.fileUrl).toBeDefined();
    expect(attrs.resultUrl).toBeDefined();
    expect(attrs.errorMessage).toBeDefined();
    expect(attrs.successCount).toBeDefined();
    expect(attrs.totalCount).toBeDefined();
    expect(attrs.status).toBeDefined();
    expect(attrs.startedAt).toBeDefined();
    expect(attrs.completedAt).toBeDefined();
    expect(attrs.createdBy).toBeDefined();
    expect(attrs.updatedBy).toBeDefined();
  });

  it('should have default values set correctly', () => {
    const attrs = BulkJob.rawAttributes;

    expect(attrs.type.defaultValue).toBe(BulkJobConstant.BULK_JOB_TYPES.EXPORT);
    expect(attrs.dryRun.defaultValue).toBe(false);
    expect(attrs.status.defaultValue).toBe(BulkJobConstant.BULK_JOB_STATUSES.PENDING);
  });

  it('should fail if required fields are missing', async () => {
    await expect(BulkJob.create({ title: 'Incomplete Job' })).rejects.toThrow();
  });

  it('should apply auditable mixin', async () => {
    const { auditableMixin, __mocks } = await import('#src/mixins/index.js');

    expect(auditableMixin.applyAuditFields).toBeDefined();
    auditableMixin.applyAuditFields(BulkJob);
    expect(__mocks.mockApplyAuditFields).toHaveBeenCalledWith(BulkJob);
  });
});

import { beforeEach, describe, expect, it, vi } from 'vitest';

import { DataTypes, Model, Sequelize } from 'sequelize';
import DepartmentModuleModel from '#src/modules/core/models/postgres/department-module.model.js';
import { auditableMixin } from '#src/mixins/index.js';

vi.mock('#src/mixins/index.js', () => ({
  auditableMixin: {
    applyAuditFields: vi.fn(),
  },
}));

describe('DepartmentModule Model', () => {
  let mockFastify;
  let DepartmentModule;

  beforeEach(() => {
    vi.resetAllMocks();
    // Mock Sequelize methods
    Model.init = vi.fn();
    Model.belongsTo = vi.fn();
    Model.hasOne = vi.fn();
    // Mock Fastify instance
    mockFastify = {
      psql: {
        connection: {
          define: vi.fn(),
        },
      },
    };
    // Initialize the model
    DepartmentModule = DepartmentModuleModel(mockFastify);
  });

  it('should define correct associations', () => {
    const mockModels = {
      Department: {},
      Module: {},
      PolicySetting: {},
    };
    DepartmentModule.associate(mockModels);
    expect(Model.belongsTo).toHaveBeenCalledWith(mockModels.Department, {
      foreignKey: 'departmentId',
      as: 'department',
    });
    expect(Model.belongsTo).toHaveBeenCalledWith(mockModels.Module, {
      foreignKey: 'moduleId',
      as: 'module',
    });
    expect(Model.hasOne).toHaveBeenCalledWith(mockModels.PolicySetting, {
      foreignKey: 'parentId',
      sourceKey: 'id',
      as: 'policySetting',
    });
  });

  it('should call auditableMixin.applyAuditFields', () => {
    expect(auditableMixin.applyAuditFields).toHaveBeenCalledWith(DepartmentModule);
  });

  describe('Model initialization', () => {
    let fields;
    let initOptions;

    beforeEach(() => {
      [fields, initOptions] = Model.init.mock.calls[0];
    });

    it('should set correct model name and table name', () => {
      expect(initOptions.modelName).toBe('DepartmentModule');
      expect(initOptions.tableName).toBe('department_modules');
    });

    it('should set underscored and timestamps to true', () => {
      expect(initOptions.underscored).toBe(true);
      expect(initOptions.timestamps).toBe(true);
    });

    it('should set the correct sequelize instance', () => {
      expect(initOptions.sequelize).toBe(mockFastify.psql.connection);
    });

    it('should define the correct fields', () => {
      expect(fields).toEqual({
        id: {
          type: DataTypes.UUID,
          defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
          primaryKey: true,
        },
        departmentId: {
          type: DataTypes.UUID,
          allowNull: false,
        },
        moduleId: {
          type: DataTypes.UUID,
          allowNull: false,
        },
        createdBy: {
          type: DataTypes.UUID,
          allowNull: true,
        },
        updatedBy: {
          type: DataTypes.UUID,
          allowNull: true,
        },
      });
    });

    it('should define the correct index', () => {
      expect(initOptions.indexes).toEqual([
        {
          unique: true,
          fields: ['department_id', 'module_id'],
        },
      ]);
    });

    it('should not define any scopes', () => {
      expect(initOptions.scopes).toEqual({});
    });
  });
});

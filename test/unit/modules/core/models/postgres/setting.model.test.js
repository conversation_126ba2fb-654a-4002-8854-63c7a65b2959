import { DataTypes, Model, Sequelize } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import SettingModel from '#src/modules/core/models/postgres/setting.model.js';

vi.mock('#src/modules/core/constants/core.constant.js', () => ({
  ACCESS_LEVELS: {
    ROOT: 'root',
    USER: 'user',
  },
}));

vi.mock('#src/modules/setting/constants/index.js', () => ({
  SettingConstant: {
    SETTING_CATEGORIES: {
      PERSONAL: 'personal',
      SAFETY: 'safety',
      THEMES: 'themes',
    },
  },
}));

describe('Setting Model', () => {
  let mockFastify;
  let Setting;

  beforeEach(() => {
    vi.resetAllMocks();
    // Mock Sequelize methods
    Model.init = vi.fn();
    Model.hasMany = vi.fn();
    // Mock Fastify instance
    mockFastify = {
      psql: {
        connection: {
          define: vi.fn(),
        },
      },
    };
    // Initialize the model
    Setting = SettingModel(mockFastify);
  });

  it('should define correct associations', () => {
    const mockModels = {
      CustomSetting: {},
    };
    Setting.associate(mockModels);
    expect(Model.hasMany).toHaveBeenCalledWith(mockModels.CustomSetting, {
      foreignKey: 'parentId',
      as: 'customSettings',
    });
  });

  describe('Model initialization', () => {
    let initOptions;
    let fields;

    beforeEach(() => {
      [fields, initOptions] = Model.init.mock.calls[0];
    });

    it('should set correct model name and table name', () => {
      expect(initOptions.modelName).toBe('Setting');
      expect(initOptions.tableName).toBe('settings');
    });

    it('should set underscored, timestamps, and paranoid to true', () => {
      expect(initOptions.underscored).toBe(true);
      expect(initOptions.timestamps).toBe(true);
      expect(initOptions.paranoid).toBe(true);
    });

    it('should set the correct sequelize instance', () => {
      expect(initOptions.sequelize).toBe(mockFastify.psql.connection);
    });

    it('should define the correct fields', () => {
      expect(fields).toHaveProperty('id');
      expect(fields).toHaveProperty('category');
      expect(fields).toHaveProperty('field');
      expect(fields).toHaveProperty('accessLevels');
      expect(fields).toHaveProperty('createdBy');
      expect(fields).toHaveProperty('updatedBy');
    });

    it('should define the correct id field', () => {
      expect(fields.id.type).toBe(DataTypes.UUID);
      expect(fields.id.type).toBeInstanceOf(Function);
      expect(fields.id.defaultValue).toEqual(Sequelize.literal('uuid_generate_v1mc()'));
      expect(fields.id.primaryKey).toBe(true);
    });

    it('should define the correct category field', () => {
      expect(fields.category.type).toBeInstanceOf(DataTypes.ENUM);
      expect(fields.category.allowNull).toBe(false);
    });

    it('should define the correct field field', () => {
      expect(fields.field.type).toBeInstanceOf(DataTypes.STRING);
      expect(fields.field.allowNull).toBe(false);
      expect(fields.field.validate).toEqual({
        len: [1, 50],
        notEmpty: true,
      });
    });

    it('should define the correct accessLevels field', () => {
      expect(fields.accessLevels.type).toBeInstanceOf(DataTypes.ARRAY);
      expect(fields.accessLevels.allowNull).toBe(false);
      expect(fields.accessLevels.defaultValue).toEqual(['root']);
    });

    it('should validate accessLevels correctly', () => {
      const { isValidAccessLevel } = fields.accessLevels.validate;

      expect(() => isValidAccessLevel(['root'])).not.toThrow();
      expect(() => isValidAccessLevel(['user'])).not.toThrow();
      expect(() => isValidAccessLevel(['invalid'])).toThrow('Invalid access level');
      expect(() => isValidAccessLevel('not an array')).toThrow('Invalid access level');
    });

    it('should define the correct createdBy and updatedBy fields', () => {
      expect(fields.createdBy.type).toBe(DataTypes.UUID);
      expect(fields.createdBy.type).toBeInstanceOf(Function);
      expect(fields.createdBy.allowNull).toBe(true);
      expect(fields.updatedBy.type).toBe(DataTypes.UUID);
      expect(fields.updatedBy.type).toBeInstanceOf(Function);
      expect(fields.updatedBy.allowNull).toBe(true);
    });

    it('should define the correct index', () => {
      expect(initOptions.indexes).toEqual([
        {
          unique: true,
          fields: ['category', 'field'],
        },
      ]);
    });

    it('should return an object with customSettings cleaned to only include specific fields when customSettings is present', () => {
      const mockSettingInstance = {
        get: vi.fn().mockReturnValue({
          id: 'some-uuid',
          category: 'personal',
          field: 'someField',
          accessLevels: ['root'],
          createdBy: 'creator-uuid',
          updatedBy: 'updater-uuid',
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: new Date(),
          customSettings: [
            {
              value: 'someValue',
              version: 1,
              createdBy: 'creator-uuid',
              updatedBy: 'updater-uuid',
              createdAt: new Date(),
              updatedAt: new Date(),
            },
          ],
        }),
      };

      const result = Setting.prototype.toSafeObject.call(mockSettingInstance);

      expect(result).toEqual({
        id: 'some-uuid',
        category: 'personal',
        field: 'someField',
        accessLevels: ['root'],
        customSettings: {
          value: 'someValue',
          version: 1,
          createdBy: 'creator-uuid',
          updatedBy: 'updater-uuid',
          createdAt: expect.any(Date),
          updatedAt: expect.any(Date),
        },
      });
    });
  });
});

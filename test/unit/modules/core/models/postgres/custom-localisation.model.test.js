import { beforeEach, describe, expect, it, vi } from 'vitest';

import { DataTypes, Model, Sequelize } from 'sequelize';
import { auditableMixin, versionedMixin } from '#src/mixins/index.js';
import { COMMON_STATUSES } from '#src/modules/core/constants/core.constant.js';
import CustomLocalisationModel from '#src/modules/core/models/postgres/custom-localisation.model.js';

vi.mock('#src/mixins/index.js', () => ({
  auditableMixin: {
    applyAuditFields: vi.fn(),
  },
  versionedMixin: {
    applyVersioning: vi.fn(),
  },
}));

describe('CustomLocalisation Model', () => {
  let mockFastify;
  let CustomLocalisation;

  beforeEach(() => {
    vi.resetAllMocks();
    // Mock Sequelize methods
    Model.init = vi.fn();
    Model.belongsTo = vi.fn();
    // Mock Fastify instance
    mockFastify = {
      psql: {
        connection: {
          define: vi.fn(),
        },
      },
    };
    // Initialize the model
    CustomLocalisation = CustomLocalisationModel(mockFastify);
  });

  it('should define correct associations', () => {
    const mockModels = {
      Localisation: {},
    };
    CustomLocalisation.associate(mockModels);
    expect(Model.belongsTo).toHaveBeenCalledWith(mockModels.Localisation, {
      foreignKey: 'parentId',
      as: 'localisation',
      onDelete: 'RESTRICT',
      onUpdate: 'CASCADE',
    });
  });

  it('should call auditableMixin.applyAuditFields and versionedMixin.applyVersioning', () => {
    expect(auditableMixin.applyAuditFields).toHaveBeenCalledWith(CustomLocalisation);
    expect(versionedMixin.applyVersioning).toHaveBeenCalledWith(CustomLocalisation);
  });

  describe('Model initialization', () => {
    let fields;
    let initOptions;

    beforeEach(() => {
      [fields, initOptions] = Model.init.mock.calls[0];
    });

    it('should set correct model name and table name', () => {
      expect(initOptions.modelName).toBe('CustomLocalisation');
      expect(initOptions.tableName).toBe('custom_localisations');
    });

    it('should set underscored and timestamps to true', () => {
      expect(initOptions.underscored).toBe(true);
      expect(initOptions.timestamps).toBe(true);
    });

    it('should set the correct sequelize instance', () => {
      expect(initOptions.sequelize).toBe(mockFastify.psql.connection);
    });

    it('should define the correct index', () => {
      expect(initOptions.indexes).toEqual([
        {
          unique: true,
          fields: ['parent_id', 'entity_id'],
        },
      ]);
    });

    it('should define the correct fields', () => {
      expect(fields).toEqual({
        id: {
          type: DataTypes.UUID,
          defaultValue: Sequelize.literal('uuid_generate_v1mc()'),
          primaryKey: true,
        },
        parentId: {
          type: DataTypes.UUID,
          allowNull: false,
        },
        entityId: {
          type: DataTypes.UUID,
          allowNull: false,
        },
        category: {
          type: DataTypes.VIRTUAL,
          get: expect.any(Function),
        },
        name: {
          type: DataTypes.VIRTUAL,
          get: expect.any(Function),
        },
        code: {
          type: DataTypes.VIRTUAL,
          get: expect.any(Function),
        },
        metadata: {
          type: DataTypes.VIRTUAL,
          get: expect.any(Function),
        },
        status: {
          type: DataTypes.ENUM(Object.values(COMMON_STATUSES)),
          allowNull: false,
          defaultValue: COMMON_STATUSES.ACTIVE,
        },
        exchangeRate: {
          type: DataTypes.DECIMAL(15, 6),
          allowNull: true,
          validate: {
            min: 0,
          },
        },
        version: {
          type: DataTypes.BIGINT,
          allowNull: false,
          defaultValue: 1,
          validate: {
            min: 1,
            notNull: { msg: 'version is required' },
          },
        },
        createdBy: {
          type: DataTypes.UUID,
          allowNull: true,
        },
        updatedBy: {
          type: DataTypes.UUID,
          allowNull: true,
        },
      });
    });
  });

  describe('Virtual fields', () => {
    let fields;

    beforeEach(() => {
      [fields] = Model.init.mock.calls[0];
    });

    it('should correctly get virtual field "category"', () => {
      const categoryGetter = fields.category.get;
      const instance = { localisation: { category: 'testCategory' } };
      expect(categoryGetter.call(instance)).toBe('testCategory');
    });

    it('should correctly get virtual field "name"', () => {
      const nameGetter = fields.name.get;
      const instance = { localisation: { name: 'testName' } };
      expect(nameGetter.call(instance)).toBe('testName');
    });

    it('should correctly get virtual field "code"', () => {
      const codeGetter = fields.code.get;
      const instance = { localisation: { code: 'testCode' } };
      expect(codeGetter.call(instance)).toBe('testCode');
    });

    it('should correctly get virtual field "metadata"', () => {
      const metadataGetter = fields.metadata.get;
      const instance = { localisation: { metadata: { key: 'value' } } };
      expect(metadataGetter.call(instance)).toEqual({ key: 'value' });
    });
  });
});

import { beforeEach, describe, expect, it, vi } from 'vitest';
import getAuditTrailModel, {
  getCollectionName,
} from '#src/modules/core/models/mongo/audit-trail.model.js';
import fastify from 'fastify';
import mongoose from 'mongoose';

vi.mock('mongoose');

describe('Test case for getAuditTrail Model', () => {
  let mockFastify;
  let mockConnection;
  let mockModel;

  beforeEach(() => {
    mockFastify = fastify();
    mockConnection = {
      models: {},
      model: vi.fn((name, schema) => {
        if (!mockConnection.models[name]) {
          mockConnection.models[name] = mockModel;
        }
        return mockModel;
      }),
    };
    mockFastify.mongo = {
      connection: mockConnection,
    };
    mongoose.Schema = vi.fn(() => ({}));
    mockModel = vi.fn(() => ({}));
  });

  it('should return the existing model if it already exists', () => {
    const date = new Date('2025-04-01');
    const collectionName = getCollectionName(date);
    mockConnection.models[collectionName] = mockModel;

    const result = getAuditTrailModel(mockFastify, date);

    expect(result).toBe(mockModel);
    expect(mongoose.Schema).not.toHaveBeenCalled();
    expect(mongoose.model).not.toHaveBeenCalled();
  });

  it('should create a new model if it does not exist', () => {
    const date = new Date('2025-04-01');
    const collectionName = getCollectionName(date);
    delete mockConnection.models[collectionName]; // Ensure no model is initially present

    getAuditTrailModel(mockFastify, date);

    expect(mockConnection.model).toHaveBeenCalledWith(collectionName, expect.any(Object));
  });

  it('should generate the correct collection name for the given date', () => {
    const date = new Date('2025-04-01');
    const collectionName = getCollectionName(date);
    expect(collectionName).toBe('audit_trails_2025_04');
  });

  it('should generate the correct collection name if no date is passed', () => {
    const collectionName = getCollectionName();
    const currentDate = new Date();
    const expectedCollectionName = `audit_trails_${currentDate.getFullYear()}_${(currentDate.getMonth() + 1).toString().padStart(2, '0')}`;
    expect(collectionName).toBe(expectedCollectionName);
  });
});

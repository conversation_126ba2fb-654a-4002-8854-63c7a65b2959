import { beforeEach, describe, expect, it, vi } from 'vitest';
import { Op } from 'sequelize';

import * as moduleRepository from '#src/modules/core/repository/postgres/module.repository.js';

describe('Module Repository', () => {
  let mockServer;

  beforeEach(() => {
    mockServer = {
      psql: {
        Module: {
          findByPk: vi.fn(),
          findAll: vi.fn(),
        },
        PolicySetting: {},
      },
    };
  });

  describe('findById', () => {
    it('should call findByPk with correct parameters', async () => {
      const id = '123';
      const options = { include: ['someAssociation'] };

      await moduleRepository.findById(mockServer, id, options);

      expect(mockServer.psql.Module.findByPk).toHaveBeenCalledWith(id, options);
    });

    it('should use empty options if not provided', async () => {
      await moduleRepository.findById(mockServer, '123');

      expect(mockServer.psql.Module.findByPk).toHaveBeenCalledWith('123', {});
    });
  });

  describe('findModulePolicies', () => {
    it('should call findAll with correct parameters', async () => {
      const hierarchies = ['ROOT', 'ORGANISATION'];

      await moduleRepository.findModulePolicies(mockServer, hierarchies);

      expect(mockServer.psql.Module.findAll).toHaveBeenCalledWith(
        expect.objectContaining({
          where: {
            parentId: null,
            hierarchy: {
              [Op.in]: hierarchies,
            },
          },
          order: expect.any(Array),
          include: expect.any(Array),
        }),
      );
    });

    it('should include PolicySetting and nested Modules', async () => {
      await moduleRepository.findModulePolicies(mockServer, ['ROOT']);

      const callArgs = mockServer.psql.Module.findAll.mock.calls[0][0];

      expect(callArgs.include).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ as: 'policySetting' }),
          expect.objectContaining({
            as: 'children',
            include: expect.arrayContaining([
              expect.objectContaining({ as: 'policySetting' }),
              expect.objectContaining({ as: 'children' }),
            ]),
          }),
        ]),
      );
    });
  });

  describe('findAll', () => {
    it('should call findAll with correct parameters', async () => {
      const options = { where: { someField: 'someValue' } };

      await moduleRepository.findAll(mockServer, options);

      expect(mockServer.psql.Module.findAll).toHaveBeenCalledWith({
        ...options,
        include: [
          {
            model: mockServer.psql.PolicySetting,
            as: 'policySetting',
            required: false,
          },
        ],
        order: [['navigationPosition', 'ASC']],
      });
    });

    it('should use empty options if not provided', async () => {
      await moduleRepository.findAll(mockServer);

      expect(mockServer.psql.Module.findAll).toHaveBeenCalledWith({
        include: [
          {
            model: mockServer.psql.PolicySetting,
            as: 'policySetting',
            required: false,
          },
        ],
        order: [['navigationPosition', 'ASC']],
      });
    });
  });
});

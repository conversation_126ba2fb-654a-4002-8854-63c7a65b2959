import { describe, expect, it } from 'vitest';
import { UserError } from '#src/modules/user/errors/index.js';

describe('UserError', () => {
  it('should create a user not found error', () => {
    const error = new UserError.userNotFound('123');
    expect(error).toBeInstanceOf(Error);
    expect(error.code).toBe('ERR_USER_NOT_FOUND');
    expect(error.message).toBe('User not found with ID: 123');
    expect(error.statusCode).toBe(404);
    expect(error.name).toBe('UserModuleError');
  });
});

import { beforeEach, describe, expect, it, vi } from 'vitest';

import * as cacheUtil from '#src/utils/cache.util.js';
import * as handler from '#src/modules/user/handlers/department-template.handler.js';
import * as responseUtil from '#src/utils/response.util.js';
import { CoreConstant } from '#src/modules/core/constants/index.js';
import { DepartmentService } from '#src/modules/user/services/index.js';

vi.mock('#src/utils/cache.util.js');
vi.mock('#src/modules/user/services/index.js');
vi.mock('#src/utils/response.util.js');

const {
  CACHE_SECOND: { SHORT },
  MODULE_NAMES: { DEPARTMENT_TEMPLATE },
  MODULE_METHODS: {
    CREATE,
    DELETE,
    INDEX,
    OPTION,
    UPDATE_MODULE_POLICY,
    UPDATE_STATUS,
    UPDATE_BASIC_INFORMATION,
    VIEW,
  },
} = CoreConstant;

describe('Department Template Handler', () => {
  const mockRequest = {
    server: { redis: {} },
  };
  const mockReply = {};

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('index', () => {
    it('should call handleServiceResponse with cached service function', async () => {
      const mockCacheKey = 'mock_cache_key';
      cacheUtil.generateCacheKey.mockReturnValue(mockCacheKey);

      await handler.index(mockRequest, mockReply);

      expect(cacheUtil.generateCacheKey).toHaveBeenCalledWith(
        `${DEPARTMENT_TEMPLATE}_${INDEX}`,
        mockRequest,
      );

      expect(responseUtil.handleServiceResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          request: mockRequest,
          reply: mockReply,
          serviceFn: expect.any(Function),
          module: DEPARTMENT_TEMPLATE,
          method: INDEX,
        }),
      );

      // Extract the serviceFn and call it
      const serviceFn = responseUtil.handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      // Verify that fetchFromCache was called with the correct arguments
      expect(cacheUtil.fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        SHORT,
      );

      // Extract the callback function passed to fetchFromCache and call it
      const fetchFromCacheCallback = cacheUtil.fetchFromCache.mock.calls[0][2];
      await fetchFromCacheCallback();

      // Verify that DepartmentService.index was called with the correct arguments
      expect(DepartmentService.index).toHaveBeenCalledWith(mockRequest, true);
    });
  });

  describe('view', () => {
    it('should call handleServiceResponse with cached service function', async () => {
      const mockCacheKey = 'mock_cache_key';
      cacheUtil.generateCacheKey.mockReturnValue(mockCacheKey);

      await handler.view(mockRequest, mockReply);

      expect(cacheUtil.generateCacheKey).toHaveBeenCalledWith(
        `${DEPARTMENT_TEMPLATE}_${VIEW}`,
        mockRequest,
      );

      expect(responseUtil.handleServiceResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          request: mockRequest,
          reply: mockReply,
          serviceFn: expect.any(Function),
          module: DEPARTMENT_TEMPLATE,
          method: VIEW,
        }),
      );

      // Extract the serviceFn and call it
      const serviceFn = responseUtil.handleServiceResponse.mock.calls[0][0].serviceFn;
      await serviceFn();

      // Now verify that fetchFromCache was called
      expect(cacheUtil.fetchFromCache).toHaveBeenCalledWith(
        mockRequest.server.redis,
        mockCacheKey,
        expect.any(Function),
        SHORT,
      );
    });
  });

  describe('create', () => {
    it('should call handleServiceResponse with create service function', async () => {
      await handler.create(mockRequest, mockReply);

      expect(responseUtil.handleServiceResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          request: mockRequest,
          reply: mockReply,
          serviceFn: expect.any(Function),
          module: DEPARTMENT_TEMPLATE,
          method: CREATE,
        }),
      );
    });
  });

  describe('updateBasicInformation', () => {
    it('should call handleServiceResponse with updateBasicInformation service function', async () => {
      await handler.updateBasicInformation(mockRequest, mockReply);

      expect(responseUtil.handleServiceResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          request: mockRequest,
          reply: mockReply,
          serviceFn: DepartmentService.updateBasicInformation,
          module: DEPARTMENT_TEMPLATE,
          method: UPDATE_BASIC_INFORMATION,
        }),
      );
    });
  });

  describe('updateModulePolicy', () => {
    it('should call handleServiceResponse with updateModulePolicy service function', async () => {
      await handler.updateModulePolicy(mockRequest, mockReply);

      expect(responseUtil.handleServiceResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          request: mockRequest,
          reply: mockReply,
          serviceFn: DepartmentService.updateModulePolicy,
          module: DEPARTMENT_TEMPLATE,
          method: UPDATE_MODULE_POLICY,
        }),
      );
    });
  });

  describe('updateStatus', () => {
    it('should call handleServiceResponse with updateStatus service function', async () => {
      await handler.updateStatus(mockRequest, mockReply);

      expect(responseUtil.handleServiceResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          request: mockRequest,
          reply: mockReply,
          serviceFn: DepartmentService.updateStatus,
          module: DEPARTMENT_TEMPLATE,
          method: UPDATE_STATUS,
        }),
      );
    });
  });

  describe('remove', () => {
    it('should call handleServiceResponse with remove service function', async () => {
      await handler.remove(mockRequest, mockReply);

      expect(responseUtil.handleServiceResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          request: mockRequest,
          reply: mockReply,
          serviceFn: DepartmentService.remove,
          module: DEPARTMENT_TEMPLATE,
          method: DELETE,
        }),
      );
    });
  });

  describe('options', () => {
    it('should call handleServiceResponse with options service function', async () => {
      await handler.options(mockRequest, mockReply);

      expect(responseUtil.handleServiceResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          request: mockRequest,
          reply: mockReply,
          serviceFn: expect.any(Function),
          module: DEPARTMENT_TEMPLATE,
          method: OPTION,
        }),
      );
    });
  });
});

import { beforeEach, describe, expect, it, vi } from 'vitest';

import * as UserHandler from '#src/modules/user/handlers/user.handler.js';
import { UserService } from '#src/modules/user/services/index.js';
import { formatSuccessResponse } from '#src/utils/response.util.js';

vi.mock('#src/modules/user/services/index.js', () => ({
  UserService: {
    index: vi.fn(),
    view: vi.fn(),
  },
}));

vi.mock('#src/utils/response.util.js', () => ({
  formatSuccessResponse: vi.fn(),
}));

describe('UserHandler', () => {
  let mockFastify;
  let mockReply;

  beforeEach(() => {
    vi.clearAllMocks();
    vi.spyOn(UserService, 'index');
    vi.spyOn(UserService, 'view');
    mockFastify = {};
    mockReply = {
      send: vi.fn(),
    };
  });

  describe('index function', () => {
    it('should call UserService.index with correct parameters', async () => {
      const mockRequest = {
        query: {
          page: '2',
          size: '20',
          username: 'testuser',
        },
        server: mockFastify,
      };

      const { page, size, username } = mockRequest.query;

      const mockIndexResult = {
        users: [{ id: 1, username: 'testuser' }],
        totalRows: 1,
        totalPages: 1,
        currentPage: 2,
      };

      UserService.index.mockResolvedValue(mockIndexResult);

      const mockFormattedResponse = {
        message: 'Success',
        data: mockIndexResult.users,
      };
      formatSuccessResponse.mockReturnValue(mockFormattedResponse);

      await UserHandler.index(mockRequest, mockReply);

      expect(UserService.index).toHaveBeenCalledWith(mockRequest.server, page, size, username);
      expect(UserService.index).toHaveBeenCalledTimes(1);
      expect(formatSuccessResponse).toHaveBeenCalledWith(
        'Users retrieved successfully',
        mockIndexResult.users,
        {
          totalRows: mockIndexResult.totalRows,
          totalPages: mockIndexResult.totalPages,
          currentPage: mockIndexResult.currentPage,
        },
      );
      expect(mockReply.send).toHaveBeenCalledWith(mockFormattedResponse);
    });
  });

  it('should handle default pagination values when page and size are not provided', async () => {
    const mockRequest = {
      query: {
        username: 'testuser',
      },
      server: mockFastify,
    };

    const mockIndexResult = {
      users: [{ id: 1, username: 'testuser' }],
      totalRows: 1,
      totalPages: 1,
      currentPage: 1,
    };

    UserService.index.mockResolvedValue(mockIndexResult);

    const mockFormattedResponse = {
      message: 'Success',
      data: mockIndexResult.users,
    };
    formatSuccessResponse.mockReturnValue(mockFormattedResponse);

    await UserHandler.index(mockRequest, mockReply);

    expect(UserService.index).toHaveBeenCalledWith(mockRequest.server, 1, 25, 'testuser');
    expect(UserService.index).toHaveBeenCalledTimes(1);
    expect(formatSuccessResponse).toHaveBeenCalledWith(
      'Users retrieved successfully',
      mockIndexResult.users,
      {
        totalRows: mockIndexResult.totalRows,
        totalPages: mockIndexResult.totalPages,
        currentPage: mockIndexResult.currentPage,
      },
    );
    expect(mockReply.send).toHaveBeenCalledWith(mockFormattedResponse);
  });

  it('should handle empty username parameter in index function', async () => {
    const mockRequest = {
      query: {
        page: '3',
        size: '15',
        username: '',
      },
      server: mockFastify,
    };

    const { page, size, username } = mockRequest.query;

    const mockIndexResult = {
      users: [
        { id: 1, username: 'user1' },
        { id: 2, username: 'user2' },
      ],
      totalRows: 2,
      totalPages: 1,
      currentPage: 3,
    };

    UserService.index.mockResolvedValue(mockIndexResult);

    const mockFormattedResponse = {
      message: 'Success',
      data: mockIndexResult.users,
    };
    formatSuccessResponse.mockReturnValue(mockFormattedResponse);

    await UserHandler.index(mockRequest, mockReply);

    expect(UserService.index).toHaveBeenCalledWith(mockRequest.server, page, size, username);
    expect(UserService.index).toHaveBeenCalledTimes(1);
    expect(formatSuccessResponse).toHaveBeenCalledWith(
      'Users retrieved successfully',
      mockIndexResult.users,
      {
        totalRows: mockIndexResult.totalRows,
        totalPages: mockIndexResult.totalPages,
        currentPage: mockIndexResult.currentPage,
      },
    );
    expect(mockReply.send).toHaveBeenCalledWith(mockFormattedResponse);
  });

  it('should handle case when UserService.index returns empty users array', async () => {
    const mockRequest = {
      query: {
        page: '1',
        size: '10',
        username: 'nonexistent',
      },
      server: mockFastify,
    };

    const mockIndexResult = {
      users: [],
      totalRows: 0,
      totalPages: 0,
      currentPage: 1,
    };

    UserService.index.mockResolvedValue(mockIndexResult);

    const mockFormattedResponse = {
      message: 'Users retrieved successfully',
      data: [],
      meta: {
        totalRows: 0,
        totalPages: 0,
        currentPage: 1,
      },
    };
    formatSuccessResponse.mockReturnValue(mockFormattedResponse);

    await UserHandler.index(mockRequest, mockReply);

    expect(UserService.index).toHaveBeenCalledWith(mockRequest.server, '1', '10', 'nonexistent');
    expect(UserService.index).toHaveBeenCalledTimes(1);
    expect(formatSuccessResponse).toHaveBeenCalledWith('Users retrieved successfully', [], {
      totalRows: 0,
      totalPages: 0,
      currentPage: 1,
    });
    expect(mockReply.send).toHaveBeenCalledWith(mockFormattedResponse);
  });

  it('should handle errors thrown by UserService.index', async () => {
    const mockRequest = {
      query: {
        page: '1',
        size: '10',
        username: 'testuser',
      },
      server: mockFastify,
    };

    const mockError = new Error('Database error');
    UserService.index.mockRejectedValue(mockError);

    await expect(UserHandler.index(mockRequest, mockReply)).rejects.toThrow('Database error');

    expect(UserService.index).toHaveBeenCalledWith(mockFastify, '1', '10', 'testuser');
    expect(UserService.index).toHaveBeenCalledTimes(1);
    expect(formatSuccessResponse).not.toHaveBeenCalled();
    expect(mockReply.send).not.toHaveBeenCalled();
  });

  describe('view function', () => {
    it('should call UserService.view with correct parameters and return formatted response', async () => {
      const mockRequest = {
        params: {
          id: '123',
        },
        server: mockFastify,
      };

      const mockViewResult = {
        user: { id: 123, username: 'testuser' },
      };

      UserService.view.mockResolvedValue(mockViewResult);

      const mockFormattedResponse = {
        message: 'Success',
        data: mockViewResult.user,
      };
      formatSuccessResponse.mockReturnValue(mockFormattedResponse);

      await UserHandler.view(mockRequest, mockReply);

      expect(UserService.view).toHaveBeenCalledWith(mockRequest.server, '123');
      expect(UserService.view).toHaveBeenCalledTimes(1);
      expect(formatSuccessResponse).toHaveBeenCalledWith(
        'User retrieved successfully',
        mockViewResult.user,
      );
      expect(mockReply.send).toHaveBeenCalledWith(mockFormattedResponse);
    });

    it('should handle non-existent user ID in view function', async () => {
      const mockRequest = {
        params: {
          id: '999',
        },
        server: mockFastify,
      };

      const mockError = new Error('User not found');
      UserService.view.mockRejectedValue(mockError);

      await expect(UserHandler.view(mockRequest, mockReply)).rejects.toThrow('User not found');

      expect(UserService.view).toHaveBeenCalledWith(mockFastify, '999');
      expect(UserService.view).toHaveBeenCalledTimes(1);
      expect(formatSuccessResponse).not.toHaveBeenCalled();
      expect(mockReply.send).not.toHaveBeenCalled();
    });
  });
});

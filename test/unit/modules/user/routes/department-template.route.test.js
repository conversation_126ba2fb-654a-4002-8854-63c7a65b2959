import { beforeEach, describe, expect, it, vi } from 'vitest';
import { DepartmentTemplateHandler } from '#src/modules/user/handlers/index.js';
import DepartmentTemplateRoute from '#src/modules/user/routes/department-template.route.js';
import { DepartmentTemplateSchema } from '#src/modules/user/schemas/index.js';

describe('DepartmentTemplateRoute', () => {
  let mockFastify;

  beforeEach(() => {
    mockFastify = {
      get: vi.fn(),
      post: vi.fn(),
      patch: vi.fn(),
      delete: vi.fn(),
    };
  });

  it('should register all routes with correct configurations', async () => {
    await DepartmentTemplateRoute(mockFastify);

    const commonAccessConfig = {
      user: true,
      member: false,
      webhook: false,
      public: false,
      ipWhitelist: ['127.0.0.1'],
    };

    const expectedRoutes = [
      {
        method: 'get',
        url: '/',
        schema: DepartmentTemplateSchema.index,
        config: { name: 'department-template.index', access: commonAccessConfig },
        handler: DepartmentTemplateHandler.index,
      },
      {
        method: 'get',
        url: '/:id',
        schema: DepartmentTemplateSchema.view,
        config: { name: 'department-template.view', access: commonAccessConfig },
        handler: DepartmentTemplateHandler.view,
      },
      {
        method: 'post',
        url: '/',
        schema: DepartmentTemplateSchema.create,
        config: { name: 'department-template.create', access: commonAccessConfig },
        handler: DepartmentTemplateHandler.create,
      },
      {
        method: 'patch',
        url: '/:id/basic-information',
        schema: DepartmentTemplateSchema.updateBasicInformation,
        config: { name: 'department-template.update', access: commonAccessConfig },
        handler: DepartmentTemplateHandler.updateBasicInformation,
      },
      {
        method: 'patch',
        url: '/:id/module-policy',
        schema: DepartmentTemplateSchema.updateModulePolicy,
        config: { name: 'department-template.update', access: commonAccessConfig },
        handler: DepartmentTemplateHandler.updateModulePolicy,
      },
      {
        method: 'patch',
        url: '/:id/status',
        schema: DepartmentTemplateSchema.updateStatus,
        config: { name: 'department-template.updateStatus', access: commonAccessConfig },
        handler: DepartmentTemplateHandler.updateStatus,
      },
      {
        method: 'delete',
        url: '/:id',
        schema: DepartmentTemplateSchema.remove,
        config: { name: 'department-template.remove', access: commonAccessConfig },
        handler: DepartmentTemplateHandler.remove,
      },
      {
        method: 'get',
        url: '/options',
        schema: DepartmentTemplateSchema.options,
        config: { name: 'department-template.options', access: commonAccessConfig },
        handler: DepartmentTemplateHandler.options,
      },
    ];

    expectedRoutes.forEach((route) => {
      expect(mockFastify[route.method]).toHaveBeenCalledWith(
        route.url,
        expect.objectContaining({
          schema: route.schema,
          config: route.config,
          handler: route.handler,
        }),
      );
    });

    expect(mockFastify.get).toHaveBeenCalledTimes(3);
    expect(mockFastify.post).toHaveBeenCalledTimes(1);
    expect(mockFastify.patch).toHaveBeenCalledTimes(3);
    expect(mockFastify.delete).toHaveBeenCalledTimes(1);
  });
});

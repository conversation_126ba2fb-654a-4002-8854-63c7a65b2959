import { beforeEach, describe, expect, it, vi } from 'vitest';
import UserRoute from '#src/modules/user/routes/user.route.js';

vi.mock('#src/modules/user/index.js', () => {
  const UserHandler = {
    index: vi.fn(),
    view: vi.fn(),
  };
  const UserSchema = {
    getUsersSchema: { some: 'schema' },
    getUserByIdSchema: { another: 'schema' },
  };
  const UserRoute = (fastify) => {
    fastify.get('/', { schema: UserSchema.getUsersSchema }, UserHandler.index);
    fastify.get('/:id', { schema: UserSchema.getUserByIdSchema }, UserHandler.view);
  };

  return { UserHandler, UserSchema, UserRoute };
});

vi.mock('#src/modules/user/handlers/index.js', () => ({
  UserHandler: {
    index: vi.fn(),
    view: vi.fn(),
  },
}));

vi.mock('#src/modules/user/schemas/index.js', () => ({
  UserSchema: {
    getUsersSchema: { schema: 'getUsersSchema' },
    getUserByIdSchema: { schema: 'getUserByIdSchema' },
  },
}));
describe('UserRoute', () => {
  let fastifyMock;

  beforeEach(() => {
    fastifyMock = {
      get: vi.fn(),
    };
    vi.clearAllMocks();
  });

  it('should register GET / route with correct schema and handler', () => {
    UserRoute(fastifyMock);

    expect(fastifyMock.get).toHaveBeenCalledWith(
      '/',
      { schema: { schema: 'getUsersSchema' } },
      expect.any(Function),
    );
  });

  it('should register GET /:id route with correct schema and handler', () => {
    UserRoute(fastifyMock);

    expect(fastifyMock.get).toHaveBeenCalledWith(
      '/:id',
      { schema: { schema: 'getUserByIdSchema' } },
      expect.any(Function),
    );
  });

  it('should register all routes', () => {
    UserRoute(fastifyMock);

    expect(fastifyMock.get).toHaveBeenCalledTimes(2);
  });

  it('should ignore the handler parameter', () => {
    const mockHandler = { someHandler: vi.fn() };
    UserRoute(fastifyMock, mockHandler);

    expect(fastifyMock.get).toHaveBeenCalledTimes(2);
    expect(fastifyMock.get).not.toHaveBeenCalledWith(
      expect.anything(),
      expect.anything(),
      mockHandler.someHandler,
    );
  });
});

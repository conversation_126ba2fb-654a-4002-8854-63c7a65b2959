import { beforeEach, describe, expect, it, vi } from 'vitest';

import * as departmentRepository from '#src/modules/user/repository/department.repository.js';
import { applyOffsetPagination } from '#src/utils/pagination.util.js';
import { buildWhereFromFilters } from '#src/utils/query.util.js';

vi.mock('#src/utils/pagination.util.js');
vi.mock('#src/utils/query.util.js');

describe('Department Repository', () => {
  let mockServer;
  let mockModelData;

  beforeEach(() => {
    mockServer = {
      psql: {
        Department: {
          findByPk: vi.fn(),
          create: vi.fn(),
        },
        DepartmentModule: {
          findOne: vi.fn(),
          create: vi.fn(),
        },
        PolicySetting: {
          create: vi.fn(),
          findOrCreate: vi.fn(),
        },
      },
    };

    mockModelData = {
      destroy: vi.fn(),
      update: vi.fn(),
    };

    vi.clearAllMocks();
  });

  describe('findAll', () => {
    it('should call buildWhereFromFilters and applyOffsetPagination with correct parameters', async () => {
      const query = { page: 1, limit: 10 };
      const whereFilter = { status: 'active' };
      const includeFilter = [{ model: 'SomeModel' }];

      buildWhereFromFilters.mockReturnValue({ where: whereFilter, include: includeFilter });

      await departmentRepository.findAll(mockServer, query);

      expect(buildWhereFromFilters).toHaveBeenCalledWith(query, mockServer.psql.Department);
      expect(applyOffsetPagination).toHaveBeenCalledWith(
        mockServer,
        mockServer.psql.Department,
        query,
        whereFilter,
        includeFilter,
      );
    });
  });

  describe('findById', () => {
    it('should call findByPk with correct parameters', async () => {
      const id = '123';
      const options = { someOption: true };

      await departmentRepository.findById(mockServer, id, options);

      expect(mockServer.psql.Department.findByPk).toHaveBeenCalledWith(id, {
        include: [
          {
            association: 'departmentModules',
            include: [{ association: 'module' }, { association: 'policySetting' }],
          },
        ],
        someOption: true,
      });
    });
  });

  describe('findAllDepartmentModules', () => {
    it('should call findAll with correct parameters', async () => {
      const departmentId = '123';
      const options = { transaction: {} };

      // Mock the findAll method
      mockServer.psql.DepartmentModule.findAll = vi.fn().mockResolvedValue([]);

      await departmentRepository.findAllDepartmentModules(mockServer, departmentId, options);

      expect(mockServer.psql.DepartmentModule.findAll).toHaveBeenCalledWith({
        where: { departmentId },
        include: [{ model: mockServer.psql.PolicySetting, as: 'policySetting' }],
        transaction: options.transaction,
      });
    });

    it('should return the result of findAll', async () => {
      const departmentId = '123';
      const mockModules = [
        { id: '1', name: 'Module 1' },
        { id: '2', name: 'Module 2' },
      ];

      mockServer.psql.DepartmentModule.findAll = vi.fn().mockResolvedValue(mockModules);

      const result = await departmentRepository.findAllDepartmentModules(mockServer, departmentId);

      expect(result).toEqual(mockModules);
    });

    it('should work without options parameter', async () => {
      const departmentId = '123';

      mockServer.psql.DepartmentModule.findAll = vi.fn().mockResolvedValue([]);

      await departmentRepository.findAllDepartmentModules(mockServer, departmentId);

      expect(mockServer.psql.DepartmentModule.findAll).toHaveBeenCalledWith({
        where: { departmentId },
        include: [{ model: mockServer.psql.PolicySetting, as: 'policySetting' }],
        transaction: undefined,
      });
    });
  });

  describe('createDepartment', () => {
    it('should call create with correct parameters', async () => {
      const data = { name: 'New Department' };
      const options = { someOption: true };

      await departmentRepository.createDepartment(mockServer, data, options);

      expect(mockServer.psql.Department.create).toHaveBeenCalledWith(data, options);
    });
  });

  describe('createDepartmentModule', () => {
    it('should call create with correct parameters', async () => {
      const data = { departmentId: '123', moduleId: '456' };
      const options = { someOption: true };

      await departmentRepository.createDepartmentModule(mockServer, data, options);

      expect(mockServer.psql.DepartmentModule.create).toHaveBeenCalledWith(data, options);
    });
  });

  describe('update', () => {
    it('should call update on the model with provided data and options', async () => {
      const updateData = { name: 'Updated Department' };
      const options = { someOption: true };

      await departmentRepository.update(mockModelData, updateData, options);

      expect(mockModelData.update).toHaveBeenCalledWith(updateData, options);
    });
  });

  describe('remove', () => {
    it('should call destroy on the model with provided options', async () => {
      const options = { someOption: true };

      await departmentRepository.remove(mockModelData, options);

      expect(mockModelData.destroy).toHaveBeenCalledWith(options);
    });
  });

  describe('createPolicySetting', () => {
    it('should call create with correct parameters', async () => {
      const data = { parentId: '123', view: true, edit: false };
      const options = { someOption: true };

      await departmentRepository.createPolicySetting(mockServer, data, options);

      expect(mockServer.psql.PolicySetting.create).toHaveBeenCalledWith(data, options);
    });
  });

  describe('findDepartmentModule', () => {
    it('should call findOne with correct parameters', async () => {
      const departmentId = '123';
      const moduleId = '456';
      const options = { someOption: true };

      await departmentRepository.findDepartmentModule(mockServer, departmentId, moduleId, options);

      expect(mockServer.psql.DepartmentModule.findOne).toHaveBeenCalledWith({
        where: {
          departmentId,
          moduleId,
        },
        someOption: true,
      });
    });

    it('should return the result of findOne', async () => {
      const departmentId = '123';
      const moduleId = '456';
      const mockModule = { id: '1', name: 'Test Module' };

      mockServer.psql.DepartmentModule.findOne.mockResolvedValue(mockModule);

      const result = await departmentRepository.findDepartmentModule(
        mockServer,
        departmentId,
        moduleId,
      );

      expect(result).toEqual(mockModule);
    });

    it('should work without options parameter', async () => {
      const departmentId = '123';
      const moduleId = '456';

      await departmentRepository.findDepartmentModule(mockServer, departmentId, moduleId);

      expect(mockServer.psql.DepartmentModule.findOne).toHaveBeenCalledWith({
        where: {
          departmentId,
          moduleId,
        },
      });
    });
  });

  describe('upsertPolicySetting', () => {
    it('should create a new policy setting if it does not exist', async () => {
      const policyData = {
        parentId: '123',
        view: true,
        edit: false,
      };
      const options = { transaction: {}, authInfoId: '456' };

      const mockPolicySetting = {
        ...policyData,
        id: '789',
      };

      mockServer.psql.PolicySetting.findOrCreate.mockResolvedValue([mockPolicySetting, true]);

      const result = await departmentRepository.upsertPolicySetting(
        mockServer,
        policyData,
        options,
      );

      expect(mockServer.psql.PolicySetting.findOrCreate).toHaveBeenCalledWith({
        where: { parentId: '123' },
        defaults: { view: true, edit: false },
        transaction: {},
        authInfoId: '456',
      });
      expect(result).toEqual({ policySetting: mockPolicySetting, isDirty: true });
    });

    it('should update an existing policy setting if it exists', async () => {
      const policyData = {
        parentId: '123',
        view: true,
        edit: false,
      };
      const options = { transaction: {}, authInfoId: '456' };

      const existingPolicySetting = {
        id: '789',
        parentId: '123',
        view: false,
        edit: true,
        update: vi.fn().mockResolvedValue({ isDirty: true }),
      };

      mockServer.psql.PolicySetting.findOrCreate.mockResolvedValue([existingPolicySetting, false]);

      const result = await departmentRepository.upsertPolicySetting(
        mockServer,
        policyData,
        options,
      );

      expect(mockServer.psql.PolicySetting.findOrCreate).toHaveBeenCalledWith({
        where: { parentId: '123' },
        defaults: { view: true, edit: false },
        transaction: {},
        authInfoId: '456',
      });
      expect(existingPolicySetting.update).toHaveBeenCalledWith(
        { view: true, edit: false },
        options,
      );
      expect(result).toEqual({ policySetting: existingPolicySetting, isDirty: true });
    });

    it('should return isDirty as false if no changes were made to an existing policy setting', async () => {
      const policyData = {
        parentId: '123',
        view: true,
        edit: false,
      };
      const options = { transaction: {}, authInfoId: '456' };

      const existingPolicySetting = {
        id: '789',
        parentId: '123',
        view: true,
        edit: false,
        update: vi.fn().mockResolvedValue({ isDirty: false }),
      };

      mockServer.psql.PolicySetting.findOrCreate.mockResolvedValue([existingPolicySetting, false]);

      const result = await departmentRepository.upsertPolicySetting(
        mockServer,
        policyData,
        options,
      );

      expect(mockServer.psql.PolicySetting.findOrCreate).toHaveBeenCalledWith({
        where: { parentId: '123' },
        defaults: { view: true, edit: false },
        transaction: {},
        authInfoId: '456',
      });
      expect(existingPolicySetting.update).toHaveBeenCalledWith(
        { view: true, edit: false },
        options,
      );
      expect(result).toEqual({ policySetting: existingPolicySetting, isDirty: false });
    });
  });

  describe('removeDepartmentModule', () => {
    it('should remove PolicySetting and DepartmentModule with the given ID', async () => {
      const departmentModuleId = '123';
      const options = { transaction: {} };

      // Mock the destroy methods
      mockServer.psql.PolicySetting.destroy = vi.fn().mockResolvedValue(1);
      mockServer.psql.DepartmentModule.destroy = vi.fn().mockResolvedValue(1);

      const result = await departmentRepository.removeDepartmentModule(
        mockServer,
        departmentModuleId,
        options,
      );

      // Check if PolicySetting.destroy was called with correct parameters
      expect(mockServer.psql.PolicySetting.destroy).toHaveBeenCalledWith({
        where: { parentId: departmentModuleId },
        transaction: options.transaction,
      });

      // Check if DepartmentModule.destroy was called with correct parameters
      expect(mockServer.psql.DepartmentModule.destroy).toHaveBeenCalledWith({
        where: { id: departmentModuleId },
        transaction: options.transaction,
      });

      // Check if the function returns the correct result
      expect(result).toBe(1);
    });

    it('should work without options parameter', async () => {
      const departmentModuleId = '123';

      // Mock the destroy methods
      mockServer.psql.PolicySetting.destroy = vi.fn().mockResolvedValue(1);
      mockServer.psql.DepartmentModule.destroy = vi.fn().mockResolvedValue(1);

      await departmentRepository.removeDepartmentModule(mockServer, departmentModuleId);

      // Check if PolicySetting.destroy was called with correct parameters
      expect(mockServer.psql.PolicySetting.destroy).toHaveBeenCalledWith({
        where: { parentId: departmentModuleId },
        transaction: undefined,
      });

      // Check if DepartmentModule.destroy was called with correct parameters
      expect(mockServer.psql.DepartmentModule.destroy).toHaveBeenCalledWith({
        where: { id: departmentModuleId },
        transaction: undefined,
      });
    });

    it('should return 0 if no DepartmentModule was deleted', async () => {
      const departmentModuleId = '123';

      // Mock the destroy methods
      mockServer.psql.PolicySetting.destroy = vi.fn().mockResolvedValue(0);
      mockServer.psql.DepartmentModule.destroy = vi.fn().mockResolvedValue(0);

      const result = await departmentRepository.removeDepartmentModule(
        mockServer,
        departmentModuleId,
      );

      expect(result).toBe(0);
    });
  });
});

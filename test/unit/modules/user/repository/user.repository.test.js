import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { Op } from 'sequelize';
import { UserRepository } from '#src/modules/user/index.js';

describe('findById', () => {
  let fastifyMock;

  beforeEach(() => {
    fastifyMock = {
      psql: {
        User: {
          findOne: vi.fn(),
        },
      },
    };
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should return a user when a valid ID is provided', async () => {
    const mockUser = { id: 1, username: 'Dev', name: 'Dev' };
    fastifyMock.psql.User.findOne.mockResolvedValue(mockUser);

    const result = await UserRepository.findById(fastifyMock, 1);
    expect(fastifyMock.psql.User.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
    expect(result).toEqual(mockUser);
  });

  it('should return null if no user is found', async () => {
    fastifyMock.psql.User.findOne.mockResolvedValue(null);

    const result = await UserRepository.findById(fastifyMock, 999);

    expect(fastifyMock.psql.User.findOne).toHaveBeenCalledWith({ where: { id: 999 } });
    expect(result).toBeNull();
  });

  it('should throw an error if findOne fails', async () => {
    fastifyMock.psql.User.findOne.mockRejectedValue(new Error('Database error'));

    await expect(UserRepository.findById(fastifyMock, 1)).rejects.toThrow('Database error');
    expect(fastifyMock.psql.User.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
  });
});

describe('UserRepository.findAll', () => {
  let fastifyMock;

  beforeEach(() => {
    fastifyMock = {
      psql: {
        User: {
          findAndCountAll: vi.fn(),
        },
      },
    };
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should return users with default pagination when no parameters are provided', async () => {
    const mockResult = {
      count: 50,
      rows: [
        { id: 1, username: 'user1' },
        { id: 2, username: 'user2' },
      ],
    };
    fastifyMock.psql.User.findAndCountAll.mockResolvedValue(mockResult);

    const result = await UserRepository.findAll(fastifyMock);

    expect(fastifyMock.psql.User.findAndCountAll).toHaveBeenCalledWith({
      limit: 25,
      offset: 0,
      where: null,
    });
    expect(result).toEqual(mockResult);
  });

  it('should apply pagination parameters correctly', async () => {
    const mockResult = {
      count: 50,
      rows: [
        { id: 3, username: 'user3' },
        { id: 4, username: 'user4' },
      ],
    };
    fastifyMock.psql.User.findAndCountAll.mockResolvedValue(mockResult);

    const result = await UserRepository.findAll(fastifyMock, 2, 10);

    expect(fastifyMock.psql.User.findAndCountAll).toHaveBeenCalledWith({
      limit: 10,
      offset: 10,
      where: null,
    });
    expect(result).toEqual(mockResult);
  });

  it('should apply username filter when provided', async () => {
    const mockResult = { count: 1, rows: [{ id: 5, username: 'testuser' }] };
    fastifyMock.psql.User.findAndCountAll.mockResolvedValue(mockResult);

    const result = await UserRepository.findAll(fastifyMock, 1, 25, { username: 'test' });

    expect(fastifyMock.psql.User.findAndCountAll).toHaveBeenCalledWith({
      limit: 25,
      offset: 0,
      where: {
        username: {
          [Op.like]: '%test%',
        },
      },
    });
    expect(result).toEqual(mockResult);
  });

  it('should handle empty result set', async () => {
    const mockResult = { count: 0, rows: [] };
    fastifyMock.psql.User.findAndCountAll.mockResolvedValue(mockResult);

    const result = await UserRepository.findAll(fastifyMock, 1, 25);

    expect(fastifyMock.psql.User.findAndCountAll).toHaveBeenCalledWith({
      limit: 25,
      offset: 0,
      where: null,
    });
    expect(result).toEqual(mockResult);
  });

  it('should throw an error if findAndCountAll fails', async () => {
    const error = new Error('Database error');
    fastifyMock.psql.User.findAndCountAll.mockRejectedValue(error);

    await expect(UserRepository.findAll(fastifyMock)).rejects.toThrow('Database error');
    expect(fastifyMock.psql.User.findAndCountAll).toHaveBeenCalled();
  });
});

import { DataTypes, Model, Sequelize } from 'sequelize';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import UserModel from '#src/modules/user/models/postgres/user.model.js';

const mockInit = vi.fn();

vi.mock('sequelize', () => {
  const MockModel = class {
    static init(...args) {
      mockInit(...args);
      return this;
    }
  };

  return {
    DataTypes: {
      STRING: 'STRING',
    },
    Model: MockModel,
    Sequelize: vi.fn(() => ({
      define: vi.fn(),
    })),
  };
});

describe('UserModel', () => {
  let fastifyMock;
  let User;

  beforeEach(() => {
    vi.clearAllMocks();

    fastifyMock = {
      psql: {
        connection: new Sequelize(),
      },
    };
    User = UserModel(fastifyMock);
  });

  it('should return a User class that extends Sequelize Model', () => {
    expect(User.prototype).toBeInstanceOf(Model);
  });

  it('should call Model.init with correct parameters', () => {
    expect(mockInit).toHaveBeenCalledWith(
      {
        username: {
          type: 'STRING',
          allowNull: false,
        },
        name: {
          type: 'STRING',
          allowNull: false,
        },
      },
      {
        sequelize: fastifyMock.psql.connection,
        modelName: 'User',
        tableName: 'users',
        timestamps: true,
        underscored: true,
      },
    );
  });

  it('should use STRING type for username and name', () => {
    const [[{ username, name }]] = mockInit.mock.calls;
    expect(username.type).toBe(DataTypes.STRING);
    expect(name.type).toBe(DataTypes.STRING);
  });

  it('should set allowNull to false for username and name', () => {
    const [[{ username, name }]] = mockInit.mock.calls;
    expect(username.allowNull).toBe(false);
    expect(name.allowNull).toBe(false);
  });

  it('should use the provided Sequelize instance', () => {
    const [[, options]] = mockInit.mock.calls;
    expect(options.sequelize).toBe(fastifyMock.psql.connection);
  });

  it('should set the correct model name and table name', () => {
    const [[, options]] = mockInit.mock.calls;
    expect(options.modelName).toBe('User');
    expect(options.tableName).toBe('users');
  });

  it('should enable timestamps', () => {
    const [[, options]] = mockInit.mock.calls;
    expect(options.timestamps).toBe(true);
  });
});

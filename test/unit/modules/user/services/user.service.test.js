import { UserError, UserRepository, UserService } from '#src/modules/user/index.js';
import { beforeEach, describe, expect, it, vi } from 'vitest';

describe('index function', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.spyOn(UserRepository, 'findAll');
  });

  it('should return a paginated list of users when valid page and size are provided', async () => {
    const mockFastify = {};
    const mockPage = 1;
    const mockSize = 10;
    const mockUsername = 'testuser';

    const mockCount = 25;
    const mockRows = [
      { id: 1, username: 'testuser1' },
      { id: 2, username: 'testuser2' },
    ];
    UserRepository.findAll.mockResolvedValue({ count: mockCount, rows: mockRows });

    const expectedResponse = {
      users: mockRows,
      totalRows: mockCount,
      totalPages: 3,
      currentPage: mockPage,
    };

    const result = await UserService.index(mockFastify, mockPage, mockSize, mockUsername);

    expect(UserRepository.findAll).toHaveBeenCalledWith(
      mockFastify,
      mockPage,
      mockSize,
      mockUsername,
    );
    expect(result).toEqual(expectedResponse);
  });

  it('should return an empty list of users when no users match the username filter', async () => {
    const mockFastify = {};
    const mockPage = 1;
    const mockSize = 10;
    const mockUsername = 'nonexistentuser';

    UserRepository.findAll.mockResolvedValue({ count: 0, rows: [] });

    const expectedResponse = {
      users: [],
      totalRows: 0,
      totalPages: 0,
      currentPage: mockPage,
    };

    const result = await UserService.index(mockFastify, mockPage, mockSize, mockUsername);

    expect(UserRepository.findAll).toHaveBeenCalledWith(
      mockFastify,
      mockPage,
      mockSize,
      mockUsername,
    );
    expect(result).toEqual(expectedResponse);
  });

  it('should calculate the total number of pages correctly when count is not divisible by size', async () => {
    const mockFastify = {};
    const mockPage = 1;
    const mockSize = 10;
    const mockUsername = 'testuser';

    const mockCount = 25; // Not divisible by size (10)
    const mockRows = [
      { id: 1, username: 'testuser1' },
      { id: 2, username: 'testuser2' },
    ];
    UserRepository.findAll.mockResolvedValue({ count: mockCount, rows: mockRows });

    const expectedResponse = {
      users: mockRows,
      totalRows: mockCount,
      totalPages: 3, // 25 / 10 = 2.5, rounded up to 3
      currentPage: mockPage,
    };

    const result = await UserService.index(mockFastify, mockPage, mockSize, mockUsername);

    expect(UserRepository.findAll).toHaveBeenCalledWith(
      mockFastify,
      mockPage,
      mockSize,
      mockUsername,
    );
    expect(result).toEqual(expectedResponse);
  });

  it('should handle a request with a username filter that matches multiple users', async () => {
    const mockFastify = {};
    const mockPage = 1;
    const mockSize = 10;
    const mockUsername = 'testuser';

    const mockCount = 15;
    const mockRows = [
      { id: 1, username: 'testuser1' },
      { id: 2, username: 'testuser2' },
      { id: 3, username: 'testuser3' },
    ];
    UserRepository.findAll.mockResolvedValue({ count: mockCount, rows: mockRows });

    const expectedResponse = {
      users: mockRows,
      totalRows: mockCount,
      totalPages: 2,
      currentPage: mockPage,
    };

    const result = await UserService.index(mockFastify, mockPage, mockSize, mockUsername);

    expect(UserRepository.findAll).toHaveBeenCalledWith(
      mockFastify,
      mockPage,
      mockSize,
      mockUsername,
    );
    expect(result).toEqual(expectedResponse);
  });

  it('should handle a request with a username filter that matches exactly one user', async () => {
    const mockFastify = {};
    const mockPage = 1;
    const mockSize = 10;
    const mockUsername = 'uniqueuser';

    const mockCount = 1;
    const mockRows = [{ id: 1, username: 'uniqueuser' }];
    UserRepository.findAll.mockResolvedValue({ count: mockCount, rows: mockRows });

    const expectedResponse = {
      users: mockRows,
      totalRows: mockCount,
      totalPages: 1,
      currentPage: mockPage,
    };

    const result = await UserService.index(mockFastify, mockPage, mockSize, mockUsername);

    expect(UserRepository.findAll).toHaveBeenCalledWith(
      mockFastify,
      mockPage,
      mockSize,
      mockUsername,
    );
    expect(result).toEqual(expectedResponse);
  });

  it('should handle a request with a page number that exceeds the total number of pages', async () => {
    const mockFastify = {};
    const mockPage = 5; // Exceeds total pages
    const mockSize = 10;
    const mockUsername = 'testuser';

    const mockCount = 20; // Only 2 pages worth of data
    const mockRows = []; // No rows for a page beyond the total pages
    UserRepository.findAll.mockResolvedValue({ count: mockCount, rows: mockRows });

    const expectedResponse = {
      users: mockRows,
      totalRows: mockCount,
      totalPages: 2, // 20 / 10 = 2
      currentPage: mockPage,
    };

    const result = await UserService.index(mockFastify, mockPage, mockSize, mockUsername);

    expect(UserRepository.findAll).toHaveBeenCalledWith(
      mockFastify,
      mockPage,
      mockSize,
      mockUsername,
    );
    expect(result).toEqual(expectedResponse);
  });
});
describe('view function', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.spyOn(UserRepository, 'findById');
  });

  it('should throw a UserNotFoundError when trying to view a user with a non-existent ID', async () => {
    const mockFastify = {};
    const mockId = 999;
    const mockError = new UserError.userNotFound(mockId, { details: ['extra details'] });

    UserRepository.findById.mockResolvedValue(null);

    await expect(UserService.view(mockFastify, mockId)).rejects.toThrow(mockError);

    expect(UserRepository.findById).toHaveBeenCalledWith(mockFastify, mockId);
  });

  it('should return the correct user details when a valid user ID is provided', async () => {
    const mockFastify = {};
    const mockId = 1;
    const mockUser = { id: 1, username: 'testuser' };

    UserRepository.findById.mockResolvedValue(mockUser);

    const result = await UserService.view(mockFastify, mockId);

    expect(UserRepository.findById).toHaveBeenCalledWith(mockFastify, mockId);
    expect(result).toEqual({ user: mockUser });
  });
});

import { describe, expect, it, vi } from 'vitest';
import fastify from 'fastify';
import successPlugin from '#src/plugins/success-message.plugin.js';

// Simple mocks
vi.mock('#src/utils/success-message.util.js', () => ({
  default: {
    getTemplate: vi.fn(() => 'Test success message'),
  },
}));

vi.mock('#src/utils/response.util.js', () => ({
  formatSuccessResponse: vi.fn(() => ({
    success: true,
    message: 'Test response',
    data: null,
    meta: {},
  })),
}));

vi.mock('#src/modules/core/constants/index.js', () => ({
  CoreConstant: {
    MODULE_METHODS: {
      CREATE: 'CREATE',
    },
  },
}));

describe('Success Plugin', () => {
  it('should add success method to reply (CREATE type = 201)', async () => {
    const app = fastify();
    await app.register(successPlugin);

    app.get('/create', (req, reply) => {
      reply.success('Test', 'CREATE'); // triggers `code = 201`
      return 'OK';
    });

    const response = await app.inject('/create');
    expect(response.statusCode).toBe(201);
    expect(app.hasReplyDecorator('success')).toBe(true);
  });

  it('should add success method to reply (non-CREATE type uses statusCode)', async () => {
    const app = fastify();
    await app.register(successPlugin);

    app.get('/update', (req, reply) => {
      reply.status(200).success('Test', 'UPDATE');
      return 'OK';
    });

    const response = await app.inject('/update');
    expect(response.statusCode).toBe(200);
  });

  it('should add success method to reply', async () => {
    const app = fastify();
    await app.register(successPlugin);

    // Test route using the decorator
    app.get('/', (req, reply) => {
      reply.success('Test', 'CREATE');
      return 'OK';
    });

    const response = await app.inject('/');
    expect(response.statusCode).toBe(201);
    expect(app.hasReplyDecorator('success')).toBe(true);
  });
});

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import Fastify from 'fastify';
import fastifyJwt from '@fastify/jwt';
import jwtPlugin from '#src/plugins/jwt.plugin.js';

vi.mock('@fastify/jwt', () => ({
  __esModule: true,
  default: vi.fn(),
}));

describe('Test case for JWT Plugin', () => {
  let fastify;
  const envBackup = { ...process.env };

  beforeEach(async () => {
    // Set mock env vars
    process.env.JWT_SECRET = 'test_jwt_secret_123';
    process.env.JWT_ALGORITHM = 'HS256';
    process.env.JWT_AUDIENCE = 'test-aud';
    process.env.JWT_ISSUER = 'test-iss';
    process.env.JWT_SECRET = 'test_jwt_secret_123';

    fastify = Fastify();

    // Mock the log object
    fastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    };

    await fastify.register(jwtPlugin);
  });

  afterEach(() => {
    vi.resetAllMocks();
    process.env = { ...envBackup };
  });

  it('should register the @fastify/jwt plugin', () => {
    expect(fastifyJwt).toHaveBeenCalledTimes(1);
    const callArgs = fastifyJwt.mock.calls[0][1];

    expect(callArgs).toEqual({
      secret: 'test_jwt_secret_123',
      sign: {
        algorithm: 'HS256',
        aud: 'test-aud',
        iss: 'test-iss',
      },
      verify: {
        allowedAud: 'test-aud',
        allowedIss: 'test-iss',
      },
    });
  });

  it('should register @fastify/jwt with the correct secret from environment variables', () => {
    const registerCallArgs = fastifyJwt.mock.calls[0][1];
    expect(registerCallArgs).toBeDefined();
    expect(registerCallArgs.secret).toBe('test_jwt_secret_123');
  });

  it('should log a success message after registration', () => {
    expect(fastify.log.info).toHaveBeenCalledTimes(1);
    expect(fastify.log.info).toHaveBeenCalledWith('JWT plugin registered successfully');
  });

  it('should handle missing JWT_SECRET gracefully ', async () => {
    delete process.env.JWT_SECRET;

    const newFastify = Fastify();
    newFastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    };

    await newFastify.register(jwtPlugin);

    const callArgs = fastifyJwt.mock.calls.at(-1)[1];
    expect(callArgs.secret).toBeUndefined();

    expect(newFastify.log.info).toHaveBeenCalledWith('JWT plugin registered successfully');
  });

  it('should not throw if optional sign options are missing', async () => {
    delete process.env.JWT_ALGORITHM;
    delete process.env.JWT_AUDIENCE;
    delete process.env.JWT_ISSUER;

    const newFastify = Fastify();
    newFastify.log = {
      debug: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    };

    await newFastify.register(jwtPlugin);

    const callArgs = fastifyJwt.mock.calls.at(-1)[1];
    expect(callArgs.sign).toEqual({
      algorithm: undefined,
      aud: undefined,
      iss: undefined,
    });
  });
});

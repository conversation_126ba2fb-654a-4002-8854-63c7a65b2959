import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import Fastify from 'fastify';
import ajv from '#src/utils/validation.util.js';
import multipartPlugin from '#src/plugins/multipart.plugin.js';

vi.mock('@fastify/multipart', () => ({
  default: vi.fn().mockImplementation(() => {
    return {
      contentParser: vi.fn(),
    };
  }),
}));

vi.mock('#src/utils/validation.util.js', () => ({
  default: {
    compile: vi.fn(),
  },
}));

describe('Multipart Plugin', () => {
  let fastify;

  beforeEach(() => {
    fastify = Fastify({ logger: false });
    vi.clearAllMocks();
  });

  afterEach(async () => {
    await fastify.close();
  });

  it('should register the multipart plugin', async () => {
    const registerSpy = vi.spyOn(fastify, 'register');

    await fastify.register(multipartPlugin);

    expect(registerSpy).toHaveBeenCalledWith(expect.any(Function), {
      attachFieldsToBody: true,
      limits: {
        fileSize: 5 * 1024 * 1024, // 5 MB file size limit
      },
    });
  });

  it('should set the validator compiler', async () => {
    const setValidatorCompilerSpy = vi.spyOn(fastify, 'setValidatorCompiler');

    await fastify.register(multipartPlugin);

    expect(setValidatorCompilerSpy).toHaveBeenCalled();
    const validatorCompiler = setValidatorCompilerSpy.mock.calls[0][0];
    expect(typeof validatorCompiler).toBe('function');

    const schema = { type: 'object' };
    validatorCompiler({ schema });
    expect(ajv.compile).toHaveBeenCalledWith(schema);
  });
});

import { beforeEach, describe, expect, it, vi } from 'vitest';
import envPlugin from '#src/plugins/env.plugin.js';
import fastifyEnv from '@fastify/env';

vi.mock('@fastify/env');

describe('Environment Plugin', () => {
  let mockFastify;

  beforeEach(() => {
    mockFastify = {
      register: vi.fn(),
      log: {
        error: vi.fn(),
      },
    };
    vi.clearAllMocks();
  });

  it('should register fastifyEnv with correct options', async () => {
    await envPlugin(mockFastify);

    expect(mockFastify.register).toHaveBeenCalledWith(fastifyEnv, {
      confKey: 'config',
      data: process.env,
      dotenv: true,
      schema: expect.any(Object),
    });
  });

  it('should pass a valid schema to fastifyEnv', async () => {
    await envPlugin(mockFastify);

    const schema = mockFastify.register.mock.calls[0][1].schema;
    expect(schema).toHaveProperty('type', 'object');
    expect(schema).toHaveProperty('properties');
    // Add more specific checks for expected schema properties
  });
});

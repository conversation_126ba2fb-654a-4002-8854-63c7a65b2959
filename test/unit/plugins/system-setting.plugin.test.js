import { beforeEach, describe, expect, it, vi } from 'vitest';
import { LocalisationRepository } from '#src/modules/setting/repository/index.js';
import systemSettingPlugin from '#src/plugins/system-setting.plugin.js';

vi.mock('#src/modules/setting/repository/index.js', () => ({
  LocalisationRepository: {
    findBaseCurrency: vi.fn(),
  },
}));

// Correctly mock the fastify-plugin
vi.mock('fastify-plugin', () => ({
  default: (fn) => fn,
}));

describe('System Setting Plugin', () => {
  let mockFastify;

  beforeEach(() => {
    mockFastify = {
      decorate: vi.fn(),
      log: {
        info: vi.fn(),
        error: vi.fn(),
      },
    };
    vi.resetAllMocks();
  });

  it('should load base currency and decorate fastify with system settings', async () => {
    const mockBaseCurrency = { code: 'USD' };
    LocalisationRepository.findBaseCurrency.mockResolvedValue(mockBaseCurrency);

    await systemSettingPlugin(mockFastify);

    expect(LocalisationRepository.findBaseCurrency).toHaveBeenCalledWith(mockFastify);
    expect(mockFastify.decorate).toHaveBeenCalledWith('systemSetting', {
      baseCurrency: mockBaseCurrency,
    });
    expect(mockFastify.log.info).toHaveBeenCalledWith('System settings initialised: baseCurrency');
  });

  it('should log an error and throw if a loader fails', async () => {
    const error = new Error('Base currency not found');
    LocalisationRepository.findBaseCurrency.mockRejectedValue(error);

    await expect(systemSettingPlugin(mockFastify)).rejects.toThrow(error);
    expect(mockFastify.log.error).toHaveBeenCalledWith(error, 'System setting loader failed');
  });
});

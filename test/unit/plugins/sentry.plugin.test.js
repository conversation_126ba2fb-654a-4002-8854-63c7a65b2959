import * as Sentry from '@sentry/node';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import sentryPlugin from '#src/plugins/sentry.plugin.js';

vi.mock('@sentry/node');
vi.mock('@sentry/profiling-node');

describe('Sentry Plugin', () => {
  let mockFastify;

  beforeEach(() => {
    mockFastify = {
      config: {
        NODE_ENV: 'development',
        SENTRY_DSN: 'https://<EMAIL>/1234567',
      },
      log: {
        info: vi.fn(),
      },
      decorate: vi.fn(),
    };

    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should not initialize Sentry in non-production environment', async () => {
    await sentryPlugin(mockFastify);

    expect(Sentry.init).not.toHaveBeenCalled();
    expect(mockFastify.log.info).toHaveBeenCalledWith(
      'Sentry not initialized (non-production environment)',
    );
  });

  it('should decorate fastify instance with sentry object', async () => {
    await sentryPlugin(mockFastify);

    expect(mockFastify.decorate).toHaveBeenCalledWith(
      'sentry',
      expect.objectContaining({
        captureException: expect.any(Function),
      }),
    );
  });

  it('should not call Sentry.captureException in non-production environment', async () => {
    await sentryPlugin(mockFastify);

    const decoratedSentry = mockFastify.decorate.mock.calls[0][1];
    const error = new Error('Test error');

    decoratedSentry.captureException(error);

    expect(Sentry.captureException).not.toHaveBeenCalled();
  });

  it('should call Sentry.captureException in production environment', async () => {
    mockFastify.config.NODE_ENV = 'production';
    await sentryPlugin(mockFastify);

    const decoratedSentry = mockFastify.decorate.mock.calls[0][1];
    const error = new Error('Test error');

    decoratedSentry.captureException(error);

    expect(Sentry.captureException).toHaveBeenCalledWith(error);
  });

  it('should not initialize Sentry if SENTRY_DSN is missing', async () => {
    mockFastify.config.SENTRY_DSN = undefined;
    await sentryPlugin(mockFastify);

    expect(Sentry.init).not.toHaveBeenCalled();
    expect(mockFastify.log.info).toHaveBeenCalledWith(
      expect.stringContaining('Sentry not initialized'),
    );
  });
});

import { beforeEach, describe, expect, it, vi } from 'vitest';
import Fastify from 'fastify';
import fastifySanitiseHtml from '#src/plugins/sanitiser.plugin.js';

describe('sanitiser plugin', () => {
  let fastify;

  beforeEach(() => {
    fastify = Fastify();
    vi.clearAllMocks();
  });

  it('should register the plugin successfully', async () => {
    await fastify.register(fastifySanitiseHtml);
    expect(fastify.sanitiseHtml).toBeDefined();
    expect(fastify.sanitiseData).toBeDefined();
  });

  it('should sanitize HTML string', async () => {
    await fastify.register(fastifySanitiseHtml);
    const dirtyHtml = '<script>alert("xss")</script><p>Hello</p>';
    const cleanHtml = fastify.sanitiseHtml(dirtyHtml);
    expect(cleanHtml).toBe('<p>Hello</p>');
  });

  it('should sanitize nested object data', async () => {
    await fastify.register(fastifySanitiseHtml);
    const dirtyData = {
      name: '<script>alert("xss")</script>John',
      description: '<p>Description</p>',
      nested: {
        content: '<img src="x">',
      },
    };
    const cleanData = fastify.sanitiseData(dirtyData);
    expect(cleanData).toEqual({
      name: 'John',
      description: '<p>Description</p>',
      nested: {
        content: '<img src="x">',
      },
    });
  });

  it('should respect ignore fields', async () => {
    await fastify.register(fastifySanitiseHtml);
    const dirtyData = {
      name: '<script>alert("xss")</script>John',
      htmlContent: '<script>alert("xss")</script>John',
    };
    const cleanData = fastify.sanitiseData(dirtyData, ['htmlContent']);
    expect(cleanData).toEqual({
      name: 'John',
      htmlContent: '<script>alert("xss")</script>John',
    });
  });

  it('should apply custom configuration', async () => {
    await fastify.register(fastifySanitiseHtml);
    const dirtyData = {
      name: '<object>Test</object>',
      description: '<script>Test</script>',
    };
    const cleanData = fastify.sanitiseData(dirtyData, ['description'], {
      ALLOWED_TAGS: ['script'],
    });
    expect(cleanData).toEqual({
      name: 'Test',
      description: '<script>Test</script>',
    });
  });

  it('should handle null and undefined values', async () => {
    await fastify.register(fastifySanitiseHtml);
    expect(fastify.sanitiseHtml(null)).toBeNull();
    expect(fastify.sanitiseHtml(undefined)).toBeUndefined();
    expect(fastify.sanitiseData(null)).toBeNull();
    expect(fastify.sanitiseData(undefined)).toBeUndefined();
  });

  it('should merge custom configuration with ADD_ prefix', async () => {
    await fastify.register(fastifySanitiseHtml);
    const dirtyData = {
      content: '<a href="http://example.com">Link</a><custom-tag>Custom content</custom-tag>',
      content2: ['<custom-tag>abc</custom-tag>', '<script>alert("xss")</script>'],
    };

    const cleanData = fastify.sanitiseData(dirtyData, [], {
      ADD_ALLOWED_TAGS: ['custom-tag'],
    });

    expect(cleanData).toEqual({
      content: '<a href="http://example.com">Link</a><custom-tag>Custom content</custom-tag>',
      content2: ['<custom-tag>abc</custom-tag>', ''],
    });
  });

  it('should sanitize array data', async () => {
    await fastify.register(fastifySanitiseHtml);
    const dirtyData = [
      {
        name: '<script>alert("xss")</script>John',
        description: '<p>Description</p>',
      },
      [
        '<script>console.log("nested")</script>',
        { nestedContent: '<strong>Valid</strong><script>alert("xss")</script>' },
      ],
    ];

    const cleanData = fastify.sanitiseData(dirtyData);

    expect(cleanData).toEqual([
      {
        name: 'John',
        description: '<p>Description</p>',
      },
      ['', { nestedContent: '<strong>Valid</strong>' }],
    ]);
  });

  it('should preserve non-string and non-object values', async () => {
    await fastify.register(fastifySanitiseHtml);
    const dirtyData = {
      number: 42,
      boolean: true,
      null: null,
      undefined: undefined,
      string: 'Hello',
      object: { key: 'value' },
    };

    const cleanData = fastify.sanitiseData(dirtyData);

    expect(cleanData).toEqual(dirtyData);
    expect(cleanData.number).toBe(42);
    expect(cleanData.boolean).toBe(true);
    expect(cleanData.null).toBeNull();
    expect(cleanData.undefined).toBeUndefined();
  });
});

/* eslint-disable sonarjs/no-hardcoded-ip */
import { beforeEach, describe, expect, it, vi } from 'vitest';
import rateLimit from '@fastify/rate-limit';
import rateLimitPlugin from '#src/plugins/rate-limit.plugin.js';

// Mock the config
vi.mock('#config/rate-limit.config.js', () => ({
  default: {
    rateLimitGroups: {
      default: { max: 100, timeWindow: '30 seconds', name: 'default' },
      api: { max: 50, timeWindow: '1 minute', name: 'api' },
    },
    groupPrefixes: {
      api: ['/api/bo'],
    },
    whitelist: ['127.0.0.1'],
    addHeadersOnExceeding: true,
    addHeaders: true,
    errorResponseBuilder: () => ({ error: 'Custom error' }),
    skipOnError: false,
    nameSpace: 'test',
    redis: null,
    cache: 5000,
    hook: 'onRequest',
  },
}));

/**
 * Test suite for Rate Limit Plugin.
 * This describe block contains tests for the rate limit plugin to ensure it is
 * registered correctly and functions as expected with various configurations.
 */
describe('Rate Limit Plugin', () => {
  let fastifyMock;

  beforeEach(() => {
    fastifyMock = {
      register: vi.fn(),
      log: {
        error: vi.fn(),
      },
    };
  });

  /**
   * This test ensures that the rate limit plugin is registered with the correct options,
   * including custom functions for max, timeWindow, keyGenerator, and allowList.
   */
  it('should register the rate limit plugin with correct options', async () => {
    await rateLimitPlugin(fastifyMock);

    expect(fastifyMock.register).toHaveBeenCalledWith(
      rateLimit,
      expect.objectContaining({
        max: expect.any(Function),
        timeWindow: expect.any(Function),
        keyGenerator: expect.any(Function),
        allowList: expect.any(Function),
        addHeadersOnExceeding: true,
        addHeaders: true,
        errorResponseBuilder: expect.any(Function),
        skipOnError: false,
        nameSpace: 'test',
        redis: null,
        cache: 5000,
        hook: 'onRequest',
      }),
    );
  });

  /**
   * This test verifies that the plugin correctly determines the rate limit group
   * based on the URL, applying different limits for different URL patterns.
   */
  it('should determine correct rate limit group based on URL', async () => {
    await rateLimitPlugin(fastifyMock);
    const options = fastifyMock.register.mock.calls[0][1];

    expect(options.max({ url: '/api/users' })).toBe(100);
    expect(options.max({ url: '/api/bo' })).toBe(50);

    expect(options.timeWindow({ url: '/api/users' })).toBe('30 seconds');
    expect(options.timeWindow({ url: '/api/bo' })).toBe('1 minute');
  });

  /**
   * This test checks if the plugin generates the correct key for rate limiting,
   * which is a combination of IP address and URL.
   */
  it('should generate correct key for rate limiting', async () => {
    await rateLimitPlugin(fastifyMock);
    const options = fastifyMock.register.mock.calls[0][1];

    expect(options.keyGenerator({ ip: '***********', url: '/api/users' })).toBe(
      '***********:/api/users',
    );
  });

  /**
   * This test ensures that the plugin correctly allows whitelisted IP addresses
   * to bypass rate limiting, while non-whitelisted IPs are subject to limits.
   */
  it('should allow whitelisted IPs', async () => {
    await rateLimitPlugin(fastifyMock);
    const options = fastifyMock.register.mock.calls[0][1];

    expect(options.allowList({ ip: '127.0.0.1' })).toBe(true);
    expect(options.allowList({ ip: '***********' })).toBe(false);
  });

  /**
   * This test verifies that the plugin uses the custom error response builder
   * defined in the configuration when rate limits are exceeded.
   */
  it('should use custom error response builder', async () => {
    await rateLimitPlugin(fastifyMock);
    const options = fastifyMock.register.mock.calls[0][1];

    expect(options.errorResponseBuilder()).toEqual({ error: 'Custom error' });
  });

  /**
   * This test ensures that any additional options passed to the plugin
   * are correctly applied and included in the final configuration.
   */
  it('should apply additional options passed to the plugin', async () => {
    const additionalOpts = { customOption: 'value' };
    await rateLimitPlugin(fastifyMock, additionalOpts);

    expect(fastifyMock.register).toHaveBeenCalledWith(
      rateLimit,
      expect.objectContaining(additionalOpts),
    );
  });

  it('should add headers when rate limit is exceeded', async () => {
    await rateLimitPlugin(fastifyMock);
    const options = fastifyMock.register.mock.calls[0][1];

    // Simulate rate limit exceeded scenario
    const headers = options.addHeadersOnExceeding ? { 'x-ratelimit-limit': 100 } : {};
    expect(headers).toHaveProperty('x-ratelimit-limit', 100);
  });
});

import { beforeEach, describe, expect, it, vi } from 'vitest';
import { DatabaseFactory } from '#src/factories/database.factory.js';
import { Sequelize } from 'sequelize';
import { SequelizePlugin } from '#src/plugins/sequelize.plugin.js';

vi.mock('sequelize', () => ({
  Sequelize: vi.fn(),
}));

vi.mock('lodash', () => ({
  default: {
    cloneDeep: vi.fn((obj) => ({ ...obj })),
  },
}));

vi.mock('#config/sequelize.config.js', () => {
  return {
    default: {
      host: 'localhost',
      database: 'testdb',
      replication: {
        read: [],
        write: { host: 'localhost' },
      },
    },
  };
});

vi.mock('#src/factories/database.factory.js', () => ({
  DatabaseFactory: class {
    getReadReplicas() {}
    static createPlugin() {}
  },
}));

describe('SequelizePlugin', () => {
  let sequelizePlugin;
  let mockFastify;

  beforeEach(() => {
    mockFastify = {
      log: {
        info: vi.fn(),
      },
      decorate: vi.fn(),
    };

    sequelizePlugin = new SequelizePlugin();
    sequelizePlugin.fastify = mockFastify;

    vi.clearAllMocks();
  });

  describe('connect', () => {
    it('should establish a database connection', async () => {
      const mockAuthenticate = vi.fn().mockResolvedValue();
      const mockAddHook = vi.fn();
      Sequelize.mockImplementation(() => ({
        authenticate: mockAuthenticate,
        addHook: mockAddHook,
      }));

      await sequelizePlugin.connect();

      expect(Sequelize).toHaveBeenCalled();
      expect(mockAuthenticate).toHaveBeenCalled();
      expect(mockFastify.log.info).toHaveBeenCalledWith('Database connection established');
    });

    it('should set read replicas in the config if getReadReplicas returns a non-empty array', async () => {
      const mockReadReplicas = [
        {
          host: 'replica1_host',
          port: '5432',
          database: 'replica1_db',
          username: 'replica1_user',
          // eslint-disable-next-line sonarjs/no-hardcoded-passwords
          password: 'replica1_password',
        },
      ];

      vi.spyOn(sequelizePlugin, 'getReadReplicas').mockReturnValue(mockReadReplicas);

      const configHolder = {};
      const mockClonedConfig = {
        replication: {
          read: [],
          write: { host: 'localhost' },
        },
      };

      // Capture the object passed to Sequelize constructor
      Sequelize.mockImplementation((configArg) => {
        Object.assign(configHolder, configArg);
        return {
          authenticate: vi.fn().mockResolvedValue(),
          addHook: vi.fn(),
        };
      });

      // Patch cloneDeep to return our mutable object
      const _ = await import('lodash');
      _.default.cloneDeep.mockReturnValue(mockClonedConfig);

      await sequelizePlugin.connect();

      expect(mockClonedConfig.replication.read).toEqual(mockReadReplicas);
    });
  });

  describe('decorate', () => {
    it('should decorate fastify with psql object', () => {
      const mockSequelize = {};
      sequelizePlugin.sequelize = mockSequelize;
      sequelizePlugin.decorate();

      expect(mockFastify.decorate).toHaveBeenCalledWith('psql', {
        connection: mockSequelize,
      });
    });
  });

  describe('static createPlugin', () => {
    it('should call DatabaseFactory.createPlugin with correct arguments', () => {
      const spy = vi.spyOn(DatabaseFactory, 'createPlugin');
      SequelizePlugin.createPlugin('postgres', true);

      expect(spy).toHaveBeenCalledWith('postgres', true);
    });
  });
});

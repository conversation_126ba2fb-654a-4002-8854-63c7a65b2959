import { beforeEach, describe, expect, it, vi } from 'vitest';
import { DatabaseFactory } from '#src/factories/database.factory.js';
import { Sequelize } from 'sequelize';
import { SequelizePlugin } from '#src/plugins/sequelize.plugin.js';

// Mock the dependencies
vi.mock('sequelize', () => ({
  Sequelize: vi.fn(),
}));

vi.mock('#src/factories/database.factory.js', () => ({
  DatabaseFactory: class {
    getReadReplicas() {}
    static createPlugin() {}
  },
}));

vi.mock('#config/sequelize.config.js', () => ({
  default: {
    replication: {},
  },
}));

describe('SequelizePlugin', () => {
  let sequelizePlugin;
  let mockFastify;

  beforeEach(() => {
    mockFastify = {
      log: {
        info: vi.fn(),
      },
      decorate: vi.fn(),
    };

    sequelizePlugin = new SequelizePlugin();
    sequelizePlugin.fastify = mockFastify;

    vi.clearAllMocks();
  });

  describe('connect', () => {
    it('should establish a database connection', async () => {
      const mockAuthenticate = vi.fn().mockResolvedValue();
      Sequelize.mockImplementation(() => ({
        authenticate: mockAuthenticate,
      }));

      vi.spyOn(sequelizePlugin, 'getReadReplicas').mockReturnValue([]);

      await sequelizePlugin.connect();

      expect(Sequelize).toHaveBeenCalledWith(expect.any(Object));
      expect(mockAuthenticate).toHaveBeenCalled();
      expect(mockFastify.log.info).toHaveBeenCalledWith('Database connection established');
    });

    it('should configure read replicas if available', async () => {
      const mockReadReplicas = [{ host: 'replica1' }, { host: 'replica2' }];
      vi.spyOn(sequelizePlugin, 'getReadReplicas').mockReturnValue(mockReadReplicas);

      const mockAuthenticate = vi.fn().mockResolvedValue();
      Sequelize.mockImplementation(() => ({
        authenticate: mockAuthenticate,
      }));

      await sequelizePlugin.connect();

      expect(Sequelize).toHaveBeenCalledWith(
        expect.objectContaining({
          replication: {
            read: mockReadReplicas,
          },
        }),
      );
    });
  });

  describe('decorate', () => {
    it('should decorate fastify with psql object', () => {
      sequelizePlugin.sequelize = new Sequelize();
      sequelizePlugin.decorate();

      expect(mockFastify.decorate).toHaveBeenCalledWith('psql', {
        connection: expect.any(Sequelize),
      });
    });
  });

  describe('static createPlugin', () => {
    it('should call DatabaseFactory.createPlugin with correct arguments', () => {
      const spy = vi.spyOn(DatabaseFactory, 'createPlugin');
      SequelizePlugin.createPlugin('postgres', true);

      expect(spy).toHaveBeenCalledWith('postgres', true);
    });
  });
});

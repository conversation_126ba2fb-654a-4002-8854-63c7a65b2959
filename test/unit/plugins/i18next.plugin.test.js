import Backend from 'i18next-http-backend';
import _ from 'lodash';
import i18next from 'i18next';

import i18nextPlugin from '#src/plugins/i18next.plugin.js';

import { beforeEach, describe, expect, it, vi } from 'vitest';
import { createI18nextOptions } from '#config/i18next.config.js';

vi.mock('i18next');
vi.mock('i18next-http-backend');

/**
 * Test suite for the i18next Plugin.
 * This suite tests the initialization and functionality of the i18next plugin,
 * including its integration with Fastify and various localization features.
 */
describe('i18next Plugin', () => {
  let fastifyMock;
  let i18nextOptions;

  beforeEach(() => {
    fastifyMock = {
      decorate: vi.fn(),
      decorateRequest: vi.fn(),
      locale: 'en-MY',
      config: {
        LLS_CDN_BASE_URL: 'https://default-cdn-url.com',
      },
    };

    vi.mocked(i18next.use).mockReturnThis();
    vi.mocked(i18next.init).mockResolvedValue(undefined);
    vi.mocked(i18next.t).mockImplementation((key) => `translated_${key}`);

    // Generate i18nextOptions using the createI18nextOptions function
    i18nextOptions = createI18nextOptions(fastifyMock);
  });

  /**
   * Test case: Initializing i18next with Backend and options.
   * Verifies that i18next is properly initialized with the correct backend and options.
   */
  it('should initialize i18next with Backend and options', async () => {
    await i18nextPlugin(fastifyMock);

    expect(i18next.use).toHaveBeenCalledWith(Backend);

    // Log the actual arguments passed to i18next.init
    const initCallArgs = i18next.init.mock.calls[0][0];

    // Deep clone both objects
    const clonedInitCallArgs = _.cloneDeep(initCallArgs);
    const clonedI18nextOptions = _.cloneDeep(i18nextOptions);

    // Compare JSON strings
    const jsonInitCallArgs = JSON.stringify(clonedInitCallArgs);
    const jsonI18nextOptions = JSON.stringify(clonedI18nextOptions);

    // Use a custom comparator if needed
    const areObjectsEqual = _.isEqual(jsonInitCallArgs, jsonI18nextOptions);

    // Use a deep comparison to ensure the objects are equal
    expect(areObjectsEqual).toBe(true);
  });

  /**
   * Test case: Decorating Fastify instance with t function.
   * Ensures that the Fastify instance is decorated with a general translation function.
   */
  it('should decorate fastify instance with t function', async () => {
    await i18nextPlugin(fastifyMock);

    expect(fastifyMock.decorate).toHaveBeenCalledWith('t', expect.any(Function));

    const tFunction = fastifyMock.decorate.mock.calls.find((call) => call[0] === 't')[1];
    expect(tFunction('test_key')).toBe('translated_test_key');
  });

  /**
   * Test case: Decorating Fastify instance with namespace-specific t functions.
   * Verifies that namespace-specific translation functions are added to the Fastify instance.
   */
  it('should decorate fastify instance with namespace-specific t functions', async () => {
    await i18nextPlugin(fastifyMock);

    expect(fastifyMock.decorate).toHaveBeenCalledWith('t_api', expect.any(Function));

    const tApiFunction = fastifyMock.decorate.mock.calls.find((call) => call[0] === 't_api')[1];

    expect(tApiFunction('test_key')).toBe('translated_test_key');
    expect(i18next.t).toHaveBeenCalledWith('test_key', { ns: 'api' });
  });

  /**
   * Test case: Decorating request with locale.
   * Checks if the request object is properly decorated with a locale property.
   */

  it('should decorate request with locale', async () => {
    await i18nextPlugin(fastifyMock);

    expect(fastifyMock.decorateRequest).toHaveBeenCalledWith('locale', null);
  });

  /**
   * Test case: Decorating Fastify instance with formatDate function.
   * Ensures that a date formatting function is added to the Fastify instance
   * and produces correctly formatted dates.
   */
  it('should decorate fastify instance with formatDate function', async () => {
    await i18nextPlugin(fastifyMock);

    expect(fastifyMock.decorate).toHaveBeenCalledWith('formatDate', expect.any(Function));

    const formatDateCall = fastifyMock.decorate.mock.calls.find((call) => call[0] === 'formatDate');
    const formatDateFunction = formatDateCall[1];

    const mockDate = new Date('2024-12-10T14:35:00Z');
    const formattedDate = formatDateFunction(mockDate, {
      dateStyle: 'short',
      timeZone: 'Asia/Kuala_Lumpur',
    });

    expect(formattedDate).toBe('10/12/2024, 10:35 pm');
  });

  /**
   * Test case: Decorating Fastify instance with formatNumber function.
   * Verifies that a number formatting function is added to the Fastify instance
   * and correctly formats numbers with currency.
   */
  it('should decorate fastify instance with formatNumber function', async () => {
    await i18nextPlugin(fastifyMock);

    expect(fastifyMock.decorate).toHaveBeenCalledWith('formatNumber', expect.any(Function));

    const formatNumberCall = fastifyMock.decorate.mock.calls.find(
      (call) => call[0] === 'formatNumber',
    );
    const formatNumberFunction = formatNumberCall[1];

    const formattedNumber = formatNumberFunction(1234567.89, null, 'USD');

    expect(formattedNumber).toBe('US$1,234,567.89');
  });
});

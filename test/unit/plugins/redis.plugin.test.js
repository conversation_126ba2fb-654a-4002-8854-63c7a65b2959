import { beforeEach, describe, expect, it, vi } from 'vitest';
import Fastify from 'fastify';
import redisPlugin from '#src/plugins/redis.plugin.js';

import { fastifyRedis } from '@fastify/redis';

vi.mock('@fastify/redis', () => ({
  fastifyRedis: vi.fn(),
}));

describe('Redis Plugin', () => {
  let fastify;

  beforeEach(() => {
    fastify = Fastify();
    fastify.config = {
      REDIS_HOST: 'localhost',
      REDIS_PORT: 6379,
    };
    fastify.log = {
      error: vi.fn(),
    };
    fastify.register = vi.fn(); // Mock the register method
    vi.clearAllMocks();
  });

  it('should register the Redis plugin on the Fastify instance', async () => {
    await redisPlugin(fastify);

    expect(fastify.register).toHaveBeenCalledWith(fastifyRedis, {
      host: 'localhost',
      port: 6379,
      db: 2,
    });
  });

  it('should log an error and throw if Redis registration fails', async () => {
    const error = new Error('Redis connection failed');
    fastify.register.mockImplementationOnce(() => {
      throw error;
    });

    await expect(redisPlugin(fastify)).rejects.toThrow('Redis connection failed');
    expect(fastify.log.error).toHaveBeenCalledWith(error, 'Failed to initialize Redis plugin:');
  });

  it('should use the correct Redis configuration from fastify.config', async () => {
    fastify.config.REDIS_HOST = 'custom-host';
    fastify.config.REDIS_PORT = 1234;

    await redisPlugin(fastify);

    expect(fastify.register).toHaveBeenCalledWith(fastifyRedis, {
      host: 'custom-host',
      port: 1234,
      db: 2,
    });
  });

  it('should select the correct Redis database', async () => {
    await redisPlugin(fastify);

    expect(fastify.register).toHaveBeenCalledWith(
      expect.any(Function),
      expect.objectContaining({
        db: 2,
      }),
    );
  });
});

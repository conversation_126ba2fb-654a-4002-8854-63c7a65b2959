import { beforeEach, describe, expect, it, vi } from 'vitest';
import { formatErrorResponse } from '#src/utils/response.util.js';
import { translateValidationErrors } from '#src/utils/i18next.util.js';

// Mock the dependencies
vi.mock('#src/utils/response.util.js', () => ({
  formatErrorResponse: vi.fn(),
}));

vi.mock('#src/utils/i18next.util.js', () => ({
  translateValidationErrors: vi.fn(),
}));

vi.mock('fastify-plugin', () => ({
  __esModule: true,
  default: (fn) => fn,
}));

// Import the plugin after mocking dependencies
const errorHandlerPlugin = (await import('#src/plugins/error-handler.plugin.js')).default;

describe('Error Handler Plugin', () => {
  let mockFastify;

  beforeEach(() => {
    mockFastify = {
      setNotFoundHandler: vi.fn(),
      setErrorHandler: vi.fn(),
    };
    vi.clearAllMocks();
  });

  it('should set up not found handler', async () => {
    await errorHandlerPlugin(mockFastify);

    expect(mockFastify.setNotFoundHandler).toHaveBeenCalled();

    const notFoundHandler = mockFastify.setNotFoundHandler.mock.calls[0][0];
    const mockRequest = {};
    const mockReply = {
      status: vi.fn().mockReturnThis(),
      send: vi.fn(),
    };

    formatErrorResponse.mockReturnValue({ message: 'Not Found' });

    await notFoundHandler(mockRequest, mockReply);

    expect(formatErrorResponse).toHaveBeenCalledWith('Not Found', 'INTERNAL_SERVER_ERROR', {});
    expect(mockReply.status).toHaveBeenCalledWith(404);
    expect(mockReply.send).toHaveBeenCalledWith({ message: 'Not Found' });
  });

  it('should set up error handler', async () => {
    await errorHandlerPlugin(mockFastify);

    expect(mockFastify.setErrorHandler).toHaveBeenCalled();

    const errorHandler = mockFastify.setErrorHandler.mock.calls[0][0];
    const mockError = new Error('Test error');
    mockError.statusCode = 400;
    mockError.code = 'BAD_REQUEST';
    mockError.metaData = { foo: 'bar' };
    const mockRequest = {};
    const mockReply = {
      status: vi.fn().mockReturnThis(),
      send: vi.fn(),
    };

    formatErrorResponse.mockReturnValue({
      message: 'Test error',
      errorCode: 'BAD_REQUEST',
      meta: { foo: 'bar' },
    });

    await errorHandler(mockError, mockRequest, mockReply);

    expect(formatErrorResponse).toHaveBeenCalledWith('Test error', 'BAD_REQUEST', { foo: 'bar' });
    expect(mockReply.status).toHaveBeenCalledWith(400);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: 'Test error',
      errorCode: 'BAD_REQUEST',
      meta: { foo: 'bar' },
    });
  });

  it('should handle errors without statusCode', async () => {
    await errorHandlerPlugin(mockFastify);

    const errorHandler = mockFastify.setErrorHandler.mock.calls[0][0];
    const mockError = new Error('Internal error');
    const mockRequest = {};
    const mockReply = {
      status: vi.fn().mockReturnThis(),
      send: vi.fn(),
    };

    formatErrorResponse.mockReturnValue({
      message:
        'Opps, something went wrong! Please try again later or contact our support team for assistance.',
    });

    await errorHandler(mockError, mockRequest, mockReply);

    expect(formatErrorResponse).toHaveBeenCalledWith(
      'Opps, something went wrong! Please try again later or contact our support team for assistance.',
      'INTERNAL_SERVER_ERROR',
      {},
    );
    expect(mockReply.status).toHaveBeenCalledWith(500);
    expect(mockReply.send).toHaveBeenCalledWith({
      message:
        'Opps, something went wrong! Please try again later or contact our support team for assistance.',
    });
  });

  it('should handle validation errors', async () => {
    await errorHandlerPlugin(mockFastify);

    const errorHandler = mockFastify.setErrorHandler.mock.calls[0][0];
    const mockError = new Error('Validation Error');
    mockError.validation = [{ message: 'Invalid input' }];
    const mockRequest = {};
    const mockReply = {
      status: vi.fn().mockReturnThis(),
      send: vi.fn(),
    };

    translateValidationErrors.mockReturnValue(['Translated error']);
    formatErrorResponse.mockReturnValue({
      message: 'Validation Error',
      errorCode: 'INTERNAL_SERVER_ERROR',
      meta: { details: ['Translated error'] },
    });

    await errorHandler(mockError, mockRequest, mockReply);

    expect(translateValidationErrors).toHaveBeenCalledWith([{ message: 'Invalid input' }]);
    expect(formatErrorResponse).toHaveBeenCalledWith('Validation Error', 'INTERNAL_SERVER_ERROR', {
      details: ['Translated error'],
    });
    expect(mockReply.status).toHaveBeenCalledWith(500);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: 'Validation Error',
      errorCode: 'INTERNAL_SERVER_ERROR',
      meta: { details: ['Translated error'] },
    });
  });
});

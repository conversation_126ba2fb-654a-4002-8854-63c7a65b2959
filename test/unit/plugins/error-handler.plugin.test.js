import { beforeEach, describe, expect, it, vi } from 'vitest';
import { formatErrorResponse } from '#src/utils/response.util.js';
import { translateValidationErrors } from '#src/utils/i18next.util.js';

// Mock the dependencies
vi.mock('#src/utils/response.util.js', () => ({
  formatErrorResponse: vi.fn(),
}));

vi.mock('#src/utils/i18next.util.js', () => ({
  translateValidationErrors: vi.fn(),
}));

vi.mock('fastify-plugin', () => ({
  __esModule: true,
  default: (fn) => fn,
}));

// Import the plugin after mocking dependencies
const errorHandlerPlugin = (await import('#src/plugins/error-handler.plugin.js')).default;

describe('Error Handler Plugin', () => {
  let mockFastify;
  let mockReply;
  const mockRequest = {};

  beforeEach(() => {
    vi.clearAllMocks();

    mockFastify = {
      setNotFoundHandler: vi.fn(),
      setErrorHandler: vi.fn(),
      t: vi.fn().mockImplementation((key) => `t_${key}`),
      config: {
        NODE_ENV: 'production',
      },
    };

    mockReply = {
      status: vi.fn().mockReturnThis(),
      send: vi.fn(),
    };
  });

  it('should set up not found handler', async () => {
    await errorHandlerPlugin(mockFastify);

    expect(mockFastify.setNotFoundHandler).toHaveBeenCalled();

    formatErrorResponse.mockReturnValue({ message: 'Not Found' });

    const notFoundHandler = mockFastify.setNotFoundHandler.mock.calls[0][0];
    await notFoundHandler(mockRequest, mockReply);

    expect(formatErrorResponse).toHaveBeenCalledWith(
      't_error.sentence.itemNotFound',
      'INTERNAL_SERVER_ERROR',
      {},
    );
    expect(mockReply.status).toHaveBeenCalledWith(404);
    expect(mockReply.send).toHaveBeenCalledWith({ message: 'Not Found' });
  });

  it('should set up error handler', async () => {
    await errorHandlerPlugin(mockFastify);

    expect(mockFastify.setErrorHandler).toHaveBeenCalled();

    const mockError = new Error('Test error');
    mockError.statusCode = 400;
    mockError.code = 'BAD_REQUEST';
    mockError.metaData = { foo: 'bar' };

    formatErrorResponse.mockReturnValue({
      message: 'Test error',
      errorCode: 'BAD_REQUEST',
      meta: { foo: 'bar' },
    });

    const errorHandler = mockFastify.setErrorHandler.mock.calls[0][0];
    await errorHandler(mockError, mockRequest, mockReply);

    expect(formatErrorResponse).toHaveBeenCalledWith('Test error', 'BAD_REQUEST', { foo: 'bar' });
    expect(mockReply.status).toHaveBeenCalledWith(400);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: 'Test error',
      errorCode: 'BAD_REQUEST',
      meta: { foo: 'bar' },
    });
  });

  it('should handle errors without statusCode', async () => {
    await errorHandlerPlugin(mockFastify);

    const mockError = new Error('Internal error');
    const mockFormattedResponse = { message: 'Mocked' };

    formatErrorResponse.mockReturnValue(mockFormattedResponse);

    const errorHandler = mockFastify.setErrorHandler.mock.calls[0][0];
    await errorHandler(mockError, mockRequest, mockReply);

    expect(formatErrorResponse).toHaveBeenCalledWith(
      mockFastify.t('error.sentence.internal'),
      'INTERNAL_SERVER_ERROR',
      {},
    );
    expect(mockReply.status).toHaveBeenCalledWith(500);
    expect(mockReply.send).toHaveBeenCalledWith(mockFormattedResponse);
  });

  it('should reveal real 500 error for development env', async () => {
    mockFastify.config.NODE_ENV = 'development';
    await errorHandlerPlugin(mockFastify);

    const realErrorMessage = 'Real error';
    const mockError = new Error(realErrorMessage);

    const errorHandler = mockFastify.setErrorHandler.mock.calls[0][0];
    await errorHandler(mockError, mockRequest, mockReply);

    expect(formatErrorResponse).toHaveBeenCalledWith(realErrorMessage, 'INTERNAL_SERVER_ERROR', {});
  });

  it('should translate custom error message with tMessageFn function', async () => {
    await errorHandlerPlugin(mockFastify);

    const customErrorMessage = 'Custom error';
    const translatedMessage = `t_${customErrorMessage}`;

    const mockError = new Error(customErrorMessage);
    mockError.statusCode = 400;
    mockError.tMessageFn = vi.fn().mockReturnValue(translatedMessage);

    const errorHandler = mockFastify.setErrorHandler.mock.calls[0][0];
    await errorHandler(mockError, mockRequest, mockReply);

    expect(formatErrorResponse).toHaveBeenCalledWith(
      translatedMessage,
      'INTERNAL_SERVER_ERROR',
      {},
    );
  });

  it('should handle validation errors', async () => {
    await errorHandlerPlugin(mockFastify);

    const mockError = new Error('Validation Error');
    mockError.validation = [{ message: 'Invalid input' }];

    mockFastify.t.mockReturnValue('Validation Error');
    translateValidationErrors.mockReturnValue(['Translated error']);
    formatErrorResponse.mockReturnValue({
      message: 'Validation Error',
      errorCode: 'INTERNAL_SERVER_ERROR',
      meta: { details: ['Translated error'] },
    });

    const errorHandler = mockFastify.setErrorHandler.mock.calls[0][0];
    await errorHandler(mockError, mockRequest, mockReply);

    expect(translateValidationErrors).toHaveBeenCalledWith([{ message: 'Invalid input' }]);
    expect(formatErrorResponse).toHaveBeenCalledWith('Validation Error', 'INTERNAL_SERVER_ERROR', {
      details: ['Translated error'],
    });
    expect(mockReply.status).toHaveBeenCalledWith(500);
    expect(mockReply.send).toHaveBeenCalledWith({
      message: 'Validation Error',
      errorCode: 'INTERNAL_SERVER_ERROR',
      meta: { details: ['Translated error'] },
    });
  });
});

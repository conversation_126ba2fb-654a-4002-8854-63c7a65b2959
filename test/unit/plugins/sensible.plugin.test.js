import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import Fastify from 'fastify';
import sensible from '@fastify/sensible';
import sensiblePlugin from '#src/plugins/sensible.plugin.js';

vi.mock('@fastify/sensible', () => ({
  default: vi.fn().mockImplementation((instance, opts, done) => {
    instance.decorate('httpErrors', {
      notFound: vi.fn(),
      badRequest: vi.fn(),
    });
    instance.decorate('assert', {
      equal: vi.fn(),
    });
    if (typeof done === 'function') {
      done();
    }
    return Promise.resolve();
  }),
}));

describe('Sensible Plugin', () => {
  let fastify;

  beforeEach(() => {
    fastify = Fastify({ logger: false });
    vi.clearAllMocks();
  });

  afterEach(async () => {
    await fastify.close();
  });

  it('should register the sensible plugin', async () => {
    await fastify.register(sensiblePlugin);

    expect(sensible).toHaveBeenCalled();
  });

  it('should have the correct plugin name', () => {
    expect(sensiblePlugin[Symbol.for('fastify.display-name')]).toBe('sensible');
  });
});

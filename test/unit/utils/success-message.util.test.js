import { beforeEach, describe, expect, it, vi } from 'vitest';

import { CoreConstant } from '#src/modules/core/constants/index.js';
import instance from '#src/utils/success-message.util.js'; // Import the instance directly

describe('SuccessMessage Utility', () => {
  let SuccessMessage;
  let mockFastify;

  beforeEach(async () => {
    // Clear the require cache and re-import to get fresh instance
    vi.resetModules();

    mockFastify = {
      t: vi.fn().mockImplementation((key, opts = {}) => {
        return `t_${key}`;
      }),
    };

    const module = await import('#src/utils/success-message.util.js');
    SuccessMessage = module.default.constructor;
  });

  describe('Singleton Pattern', () => {
    it('should create only one instance', () => {
      const instance1 = SuccessMessage.getInstance();
      const instance2 = SuccessMessage.getInstance();
      expect(instance1).toBe(instance2);
    });

    it('should initialize with message templates', () => {
      expect(instance.templates).toBeDefined();
      expect(typeof instance.templates[CoreConstant.MODULE_METHODS.INDEX]).toBe('string');
    });
  });

  describe('getTemplate()', () => {
    const module = 'user';
    const testCases = Object.keys(CoreConstant.MODULE_METHODS).map((key) => {
      return {
        type: CoreConstant.MODULE_METHODS[key],
        expected: `t_common.sentence.${CoreConstant.MODULE_METHODS[key]}Success`,
      };
    });

    testCases.forEach(({ type, expected }) => {
      it(`should return correct message for ${type} operation`, () => {
        const result = instance.getTemplate(mockFastify, module, type);
        expect(result).toBe(expected);
      });
    });

    it('should translate success message with module name', () => {
      const translatedModuleName = mockFastify.t(`common.label.${module}`);
      instance.getTemplate(mockFastify, module, CoreConstant.MODULE_METHODS.INDEX);

      expect(mockFastify.t).toHaveBeenCalledWith('common.sentence.indexSuccess', {
        module: translatedModuleName,
      });
    });

    it(`should return default message for unexpected operation type`, () => {
      const result = instance.getTemplate(mockFastify, module, 'unknownType');
      expect(result).toBe('t_common.sentence.actionSuccess');
    });
  });
});

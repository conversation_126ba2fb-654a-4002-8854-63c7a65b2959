import { beforeEach, describe, expect, it, vi } from 'vitest';
import { modifyPayload, shouldModifyResponse } from '#src/utils/response-modifier.util.js';

/**
 * Mocks the file.util.js module for testing purposes.
 *
 * This mock replaces the actual implementation of the getPackageJson function
 * from the file.util.js module. It returns a fixed object representing
 * a simplified package.json structure.
 *
 */
vi.mock('#src/utils/file.util.js', () => ({
  getPackageJson: () => ({
    name: 'test-app',
    version: '1.0.0',
  }),
}));

describe('Response Modifier Utility', () => {
  describe('shouldModifyResponse Function', () => {
    let mockReply;

    beforeEach(() => {
      mockReply = {
        getHeader: vi.fn(),
      };
    });

    it('should return true when content-type header includes application/json', () => {
      mockReply.getHeader.mockReturnValue('application/json; charset=utf-8');
      const result = shouldModifyResponse(mockReply);
      expect(result).toBe(true);
      expect(mockReply.getHeader).toHaveBeenCalledWith('content-type');
    });

    it('should handle case-insensitive content-type header name', () => {
      mockReply.getHeader.mockImplementation((headerName) => {
        if (headerName.toLowerCase() === 'content-type') {
          return 'APPLICATION/JSON; charset=utf-8';
        }
        return null;
      });

      const result = shouldModifyResponse(mockReply);

      expect(mockReply.getHeader).toHaveBeenCalledWith('content-type');
      expect(result).toBe(true);
    });

    it('should handle case-insensitive "application/json" content type', () => {
      mockReply.getHeader.mockReturnValue('ApPlIcAtIoN/JsOn; charset=utf-8');
      const result = shouldModifyResponse(mockReply);
      expect(result).toBe(true);
      expect(mockReply.getHeader).toHaveBeenCalledWith('content-type');
    });

    it('should return false for non-JSON content types like text/plain', () => {
      mockReply.getHeader.mockReturnValue('text/plain');
      const result = shouldModifyResponse(mockReply);
      expect(result).toBe(false);
      expect(mockReply.getHeader).toHaveBeenCalledWith('content-type');
    });

    it('should handle content-type headers with multiple values', () => {
      mockReply.getHeader.mockReturnValue('text/html; charset=UTF-8, application/json');
      const result = shouldModifyResponse(mockReply);
      expect(result).toBe(true);
      expect(mockReply.getHeader).toHaveBeenCalledWith('content-type');
    });

    it('should handle content-type headers with additional parameters', () => {
      mockReply.getHeader.mockReturnValue('application/json; charset=utf-8');
      const result = shouldModifyResponse(mockReply);
      expect(result).toBe(true);
      expect(mockReply.getHeader).toHaveBeenCalledWith('content-type');
    });

    it('should return false for empty content-type header value', () => {
      mockReply.getHeader.mockReturnValue('');
      const result = shouldModifyResponse(mockReply);
      expect(result).toBe(false);
      expect(mockReply.getHeader).toHaveBeenCalledWith('content-type');
    });
  });

  /**
   * Test suite for the modifyPayload utility function.
   * This describe block contains tests that verify the behavior of the modifyPayload function
   * under various scenarios, including handling of valid and invalid payloads, environment variables,
   * and preservation of existing debug information.
   */
  describe('modifyPayload Function', () => {
    let mockFastify;
    let mockRequest;
    let mockReply;

    beforeEach(() => {
      vi.resetModules();
      process.env.APP_NAME = 'test-env-app';
      process.env.NODE_ENV = 'test';

      mockFastify = {
        log: {
          debug: vi.fn(),
        },
      };

      mockRequest = {
        id: 'test-request-id',
      };

      mockReply = {
        getHeader: vi.fn().mockReturnValue('application/json'),
      };
    });

    it('should modify valid JSON payload with debug information', () => {
      const payload = JSON.stringify({ data: 'test' });
      const result = modifyPayload(mockFastify, mockRequest, mockReply, payload);
      const parsedResult = JSON.parse(result);

      expect(parsedResult).toEqual({
        data: 'test',
        debug: {
          appName: 'test-env-app',
          environment: 'test',
          requestId: 'test-request-id',
          version: '1.0.0',
        },
      });
    });

    it('should use fallback values when environment variables are not set', () => {
      delete process.env.APP_NAME;
      delete process.env.NODE_ENV;

      const payload = JSON.stringify({ data: 'test' });
      const result = modifyPayload(mockFastify, mockRequest, mockReply, payload);
      const parsedResult = JSON.parse(result);

      expect(parsedResult.debug).toEqual({
        appName: 'test-app',
        environment: 'development',
        requestId: 'test-request-id',
        version: '1.0.0',
      });
    });

    it('should return unmodified payload for non-string input', () => {
      const payload = { data: 'test' };
      const result = modifyPayload(mockFastify, mockRequest, mockReply, payload);

      expect(result).toBe(payload);
    });

    it('should return unmodified payload when shouldModifyResponse is false', () => {
      const payload = JSON.stringify({ data: 'test' });
      mockReply.getHeader.mockReturnValue('text/plain');

      const result = modifyPayload(mockFastify, mockRequest, mockReply, payload);

      expect(result).toBe(payload);
      expect(mockFastify.log.debug).not.toHaveBeenCalled();
    });

    it('should return unmodified payload for invalid JSON string', () => {
      const payload = 'invalid json';
      const result = modifyPayload(mockFastify, mockRequest, mockReply, payload);

      expect(result).toBe(payload);
      expect(mockFastify.log.debug).toHaveBeenCalledWith(
        expect.any(Error),
        'Failed to parse JSON payload for a JSON content-type response',
      );
    });

    it('should handle empty JSON object', () => {
      const payload = '{}';
      const result = modifyPayload(mockFastify, mockRequest, mockReply, payload);
      const parsedResult = JSON.parse(result);

      expect(parsedResult).toEqual({
        debug: {
          appName: 'test-env-app',
          environment: 'test',
          requestId: 'test-request-id',
          version: '1.0.0',
        },
      });
    });

    it('should use package.json name when APP_NAME is not set', () => {
      delete process.env.APP_NAME;
      const payload = JSON.stringify({ data: 'test' });
      const result = modifyPayload(mockFastify, mockRequest, mockReply, payload);
      const parsedResult = JSON.parse(result);

      expect(parsedResult).toEqual({
        data: 'test',
        debug: {
          appName: 'test-app',
          environment: 'test',
          requestId: 'test-request-id',
          version: '1.0.0',
        },
      });
    });
  });
});

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
  formatDropdownResponse,
  formatErrorResponse,
  formatSuccessResponse,
  handleServiceResponse,
} from '#src/utils/response.util.js';
import { translateDropdownItem, translateMessage } from '#src/utils/i18next.util.js';

// Mock the translateMessage function
vi.mock('#src/utils/i18next.util.js', () => ({
  translateMessage: vi.fn(),
  translateDropdownItem: vi.fn(),
}));

describe('Response Utility Functions', () => {
  describe('formatSuccessResponse', () => {
    it('should format a success response correctly', () => {
      translateMessage.mockReturnValue('Translated: Success message');

      const result = formatSuccessResponse(
        'Success message',
        { id: 1 },
        { total: 1 },
        { param: 'value' },
      );
      expect(result).toEqual({
        message: 'Translated: Success message',
        data: { id: 1 },
        meta: { total: 1 },
      });
      expect(translateMessage).toHaveBeenCalledWith('Success message', { param: 'value' });
    });

    it('should handle default parameters', () => {
      translateMessage.mockReturnValue('Translated: ');

      const result = formatSuccessResponse();
      expect(result).toEqual({
        message: 'Translated: ',
        data: [],
        meta: {},
      });
    });

    it('should translate meta.details when present', () => {
      translateMessage.mockImplementation((message) => `Translated: ${message}`);

      const result = formatSuccessResponse(
        'Success message',
        { id: 1 },
        { total: 1, details: 'Some details' },
        { param: 'value' },
      );
      expect(result).toEqual({
        message: 'Translated: Success message',
        data: { id: 1 },
        meta: {
          total: 1,
          details: 'Translated: Some details',
        },
      });
    });
  });

  describe('formatErrorResponse', () => {
    it('should format an error response correctly', () => {
      translateMessage.mockReturnValue('Translated: Error message');

      const result = formatErrorResponse(
        'Error message',
        'ERR001',
        { details: 'Some details' },
        { param: 'value' },
      );
      expect(result).toEqual({
        message: 'Translated: Error message',
        errorCode: 'ERR001',
        meta: { details: 'Some details' },
      });
      expect(translateMessage).toHaveBeenCalledWith('Error message', { param: 'value' });
    });

    it('should handle default parameters', () => {
      translateMessage.mockReturnValue('Translated: ');

      const result = formatErrorResponse();
      expect(result).toEqual({
        message: 'Translated: ',
        errorCode: '',
        meta: {},
      });
    });
  });

  describe('formatDropdownResponse', () => {
    beforeEach(() => {
      translateDropdownItem.mockImplementation((data) =>
        // eslint-disable-next-line sonarjs/no-nested-functions
        data.map((item) => `Translated Dropdown: ${item}`),
      );
    });

    it('should format a dropdown response correctly', () => {
      const mockData = ['Option 1', 'Option 2'];
      translateMessage.mockImplementation((message, params) => `Translated: ${message}`);

      const result = formatDropdownResponse(
        'Dropdown message',
        mockData,
        { total: 2 },
        { param: 'value' },
      );
      expect(result).toEqual({
        message: 'Translated: Dropdown message',
        data: ['Translated Dropdown: Option 1', 'Translated Dropdown: Option 2'],
        meta: { total: 2 },
      });
      expect(translateMessage).toHaveBeenCalledWith('Dropdown message', { param: 'value' });
      expect(translateDropdownItem).toHaveBeenCalledWith(mockData);
    });

    it('should handle default parameters', () => {
      translateMessage.mockImplementation((message, params) => 'Translated: ');

      const result = formatDropdownResponse();
      expect(result).toEqual({
        message: 'Translated: ',
        data: [],
        meta: {},
      });
    });

    it('should translate meta.details when present', () => {
      translateMessage.mockImplementation((message, params) => `Translated: ${message}`);

      const result = formatDropdownResponse(
        'Dropdown message',
        ['Option 1', 'Option 2'],
        { total: 2, details: 'Some dropdown details' },
        { param: 'value' },
      );
      expect(result).toEqual({
        message: 'Translated: Dropdown message',
        data: ['Translated Dropdown: Option 1', 'Translated Dropdown: Option 2'],
        meta: {
          total: 2,
          details: 'Translated: Some dropdown details',
        },
      });
    });
  });

  describe('handleServiceResponse', () => {
    const mockRequest = {};
    const mockReply = {
      success: vi.fn(),
    };

    afterEach(() => {
      vi.clearAllMocks();
    });

    it('should handle index method and call reply.success with rows and pagination', async () => {
      const serviceFn = vi.fn().mockResolvedValue({
        rows: [{ id: 1 }],
        pagination: { total: 1 },
      });

      await handleServiceResponse({
        request: mockRequest,
        reply: mockReply,
        serviceFn,
        module: 'user',
        method: 'index',
      });

      expect(serviceFn).toHaveBeenCalledWith(mockRequest);
      expect(mockReply.success).toHaveBeenCalledWith('user', 'index', [{ id: 1 }], {
        pagination: { total: 1 },
      });
    });

    it('should handle non-index method and call reply.success with result only', async () => {
      const serviceFn = vi.fn().mockResolvedValue({ id: 1 });

      await handleServiceResponse({
        request: mockRequest,
        reply: mockReply,
        serviceFn,
        module: 'user',
        method: 'create',
      });

      expect(serviceFn).toHaveBeenCalledWith(mockRequest);
      expect(mockReply.success).toHaveBeenCalledWith('user', 'create', { id: 1 });
    });
  });
});

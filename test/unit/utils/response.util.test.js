import { describe, expect, it, vi } from 'vitest';
import {
  formatDropdownResponse,
  formatErrorResponse,
  formatSuccessResponse,
} from '#src/utils/response.util.js';
import { translateMessage } from '#src/utils/i18next.util.js';

// Mock the translateMessage function
vi.mock('#src/utils/i18next.util.js', () => ({
  translateMessage: vi.fn(),
}));

describe('Response Utility Functions', () => {
  describe('formatSuccessResponse', () => {
    it('should format a success response correctly', () => {
      translateMessage.mockReturnValue('Translated: Success message');

      const result = formatSuccessResponse(
        'Success message',
        { id: 1 },
        { total: 1 },
        { param: 'value' },
      );
      expect(result).toEqual({
        message: 'Translated: Success message',
        data: { id: 1 },
        meta: { total: 1 },
      });
      expect(translateMessage).toHaveBeenCalledWith('Success message', { param: 'value' });
    });

    it('should handle default parameters', () => {
      translateMessage.mockReturnValue('Translated: ');

      const result = formatSuccessResponse();
      expect(result).toEqual({
        message: 'Translated: ',
        data: [],
        meta: {},
      });
    });

    it('should translate meta.details when present', () => {
      translateMessage.mockImplementation((message) => `Translated: ${message}`);

      const result = formatSuccessResponse(
        'Success message',
        { id: 1 },
        { total: 1, details: 'Some details' },
        { param: 'value' },
      );
      expect(result).toEqual({
        message: 'Translated: Success message',
        data: { id: 1 },
        meta: {
          total: 1,
          details: 'Translated: Some details',
        },
      });
    });
  });

  describe('formatErrorResponse', () => {
    it('should format an error response correctly', () => {
      translateMessage.mockReturnValue('Translated: Error message');

      const result = formatErrorResponse(
        'Error message',
        'ERR001',
        { details: 'Some details' },
        { param: 'value' },
      );
      expect(result).toEqual({
        message: 'Translated: Error message',
        errorCode: 'ERR001',
        meta: { details: 'Some details' },
      });
      expect(translateMessage).toHaveBeenCalledWith('Error message', { param: 'value' });
    });

    it('should handle default parameters', () => {
      translateMessage.mockReturnValue('Translated: ');

      const result = formatErrorResponse();
      expect(result).toEqual({
        message: 'Translated: ',
        errorCode: '',
        meta: {},
      });
    });
  });

  describe('formatDropdownResponse', () => {
    it('should format a dropdown response correctly', () => {
      const mockData = ['Option 1', 'Option 2'];
      translateMessage.mockImplementation((message, params, type) => {
        if (type === 'dropdown') {
          return message.map(translateDropdownItem);
        }
        return `Translated: ${message}`;
      });

      function translateDropdownItem(item) {
        return `Translated Dropdown: ${item}`;
      }

      const result = formatDropdownResponse(
        'Dropdown message',
        mockData,
        { total: 2 },
        { param: 'value' },
      );
      expect(result).toEqual({
        message: 'Translated: Dropdown message',
        data: ['Translated Dropdown: Option 1', 'Translated Dropdown: Option 2'],
        meta: { total: 2 },
      });
      expect(translateMessage).toHaveBeenCalledWith('Dropdown message', { param: 'value' });
      expect(translateMessage).toHaveBeenCalledWith(mockData, {}, 'dropdown');
    });

    it('should handle default parameters', () => {
      translateMessage.mockImplementation((message, params, type) => {
        if (type === 'dropdown') {
          return message;
        }
        return 'Translated: ';
      });

      const result = formatDropdownResponse();
      expect(result).toEqual({
        message: 'Translated: ',
        data: [],
        meta: {},
      });
    });

    it('should translate meta.details when present', () => {
      translateMessage.mockImplementation((message, params, type) => {
        if (type === 'dropdown') {
          return message.map(translateDropdownItem);
        }
        return `Translated: ${message}`;
      });

      function translateDropdownItem(item) {
        return `Translated Dropdown: ${item}`;
      }

      const result = formatDropdownResponse(
        'Dropdown message',
        ['Option 1', 'Option 2'],
        { total: 2, details: 'Some dropdown details' },
        { param: 'value' },
      );
      expect(result).toEqual({
        message: 'Translated: Dropdown message',
        data: ['Translated Dropdown: Option 1', 'Translated Dropdown: Option 2'],
        meta: {
          total: 2,
          details: 'Translated: Some dropdown details',
        },
      });
    });
  });
});

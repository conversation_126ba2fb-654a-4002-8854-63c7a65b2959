import { beforeEach, describe, expect, it, vi } from 'vitest';
import { withTransaction } from '#src/utils/db-transaction.util.js';

describe('withTransaction', () => {
  let mockServer;
  let mockCallback;

  beforeEach(() => {
    mockServer = {
      psql: {
        connection: {
          transaction: vi.fn(),
        },
      },
    };
    mockCallback = vi.fn();
  });

  it('should use the provided external transaction if available', async () => {
    const externalTransaction = { commit: vi.fn(), rollback: vi.fn() };
    const options = { transaction: externalTransaction };
    const expectedResult = { success: true };
    mockCallback.mockResolvedValue(expectedResult);

    const result = await withTransaction(mockServer, options, mockCallback);

    expect(mockCallback).toHaveBeenCalledWith(externalTransaction);
    expect(mockServer.psql.connection.transaction).not.toHaveBeenCalled();
    expect(result).toEqual(expectedResult);
  });

  it('should create a new transaction if no external transaction is provided', async () => {
    const internalTransaction = { commit: vi.fn(), rollback: vi.fn() };
    const expectedResult = { success: true };
    mockCallback.mockResolvedValue(expectedResult);
    mockServer.psql.connection.transaction.mockImplementation((cb) => cb(internalTransaction));

    const result = await withTransaction(mockServer, {}, mockCallback);

    expect(mockServer.psql.connection.transaction).toHaveBeenCalled();
    expect(mockCallback).toHaveBeenCalledWith(internalTransaction);
    expect(result).toEqual(expectedResult);
  });

  it('should handle options being undefined', async () => {
    const internalTransaction = { commit: vi.fn(), rollback: vi.fn() };
    const expectedResult = { success: true };
    mockCallback.mockResolvedValue(expectedResult);
    mockServer.psql.connection.transaction.mockImplementation((cb) => cb(internalTransaction));

    const result = await withTransaction(mockServer, undefined, mockCallback);

    expect(mockServer.psql.connection.transaction).toHaveBeenCalled();
    expect(mockCallback).toHaveBeenCalledWith(internalTransaction);
    expect(result).toEqual(expectedResult);
  });

  it('should return the result of the callback', async () => {
    const expectedResult = { success: true };
    mockCallback.mockResolvedValue(expectedResult);
    mockServer.psql.connection.transaction.mockImplementation((cb) =>
      cb({ commit: vi.fn(), rollback: vi.fn() }),
    );

    const result = await withTransaction(mockServer, {}, mockCallback);

    expect(result).toEqual(expectedResult);
  });

  it('should propagate errors thrown in the callback', async () => {
    const expectedError = new Error('Test error');
    mockCallback.mockRejectedValue(expectedError);
    mockServer.psql.connection.transaction.mockImplementation((cb) =>
      cb({ commit: vi.fn(), rollback: vi.fn() }),
    );

    await expect(withTransaction(mockServer, {}, mockCallback)).rejects.toThrow(expectedError);
  });
});

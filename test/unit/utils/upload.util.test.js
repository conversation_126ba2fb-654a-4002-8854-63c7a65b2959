import { PutObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import uploadToS3 from '#src/utils/upload.util.js';

/**
 * Mocks the '@aws-sdk/client-s3' module for testing purposes.
 *
 * This mock replaces the actual S3Client and PutObjectCommand
 * implementations with Jest mock functions. It allows for
 * controlled testing of S3-related functionality without
 * making actual AWS API calls.
 *
 * @param {Function} factory - A function that returns an object
 * with mocked S3Client and PutObjectCommand properties.
 * @returns {Object} An object containing mocked S3Client and
 * PutObjectCommand functions.
 */
vi.mock('@aws-sdk/client-s3', () => ({
  S3Client: vi.fn(),
  PutObjectCommand: vi.fn(),
}));

/**
 * Test suite for Upload Utility Functions.
 * This describe block contains tests for file upload functionality,
 * including successful uploads and error cases.
 */
describe('Upload Utility Functions', () => {
  /** @type {Object} Mock Fastify instance with AWS configuration */
  let mockFastify;
  /** @type {Object} Mock S3 client for testing */
  let mockS3Client;

  /**
   * Set up the test environment before each test.
   * This function initializes mock objects and sets up fake timers.
   */
  beforeEach(() => {
    mockFastify = {
      config: {
        AWS_REGION: 'us-west-2',
        AWS_ACCESS_KEY_ID: 'mock-access-key',
        AWS_SECRET_ACCESS_KEY: 'mock-secret-key',
        AWS_BUCKET: 'mock-bucket',
        AWS_S3_PATH: 'https://mock-bucket.s3.amazonaws.com',
      },
    };

    mockS3Client = {
      send: vi.fn().mockResolvedValue({}),
    };

    S3Client.mockImplementation(() => mockS3Client);
    PutObjectCommand.mockImplementation((params) => params);

    vi.useFakeTimers();
    vi.setSystemTime(new Date('2024-01-01'));
  });

  /**
   * Clean up after each test.
   * This function clears all mocks and restores real timers.
   */
  afterEach(() => {
    vi.clearAllMocks();
    vi.useRealTimers();
  });

  it('should generate unique filenames for each upload', async () => {
    const fileContent = Buffer.from('test file content');
    const originalFilename = 'test.jpg';
    const mimeType = 'image/jpeg';

    const result1 = await uploadToS3(fileContent, originalFilename, mimeType, mockFastify);

    vi.setSystemTime(new Date('2024-01-02'));
    const result2 = await uploadToS3(fileContent, originalFilename, mimeType, mockFastify);

    expect(result1).not.toBe(result2);
  });

  it('should upload a file successfully and return a valid URL', async () => {
    const fileContent = Buffer.from('test file content');
    const originalFilename = 'test.jpg';
    const mimeType = 'image/jpeg';

    const result = await uploadToS3(fileContent, originalFilename, mimeType, mockFastify);

    expect(S3Client).toHaveBeenCalledWith({
      region: 'us-west-2',
      credentials: {
        accessKeyId: 'mock-access-key',
        secretAccessKey: 'mock-secret-key',
      },
    });

    expect(PutObjectCommand).toHaveBeenCalledWith({
      Bucket: mockFastify.config.AWS_BUCKET,
      Key: expect.stringMatching(/^[a-z0-9]+-[a-f0-9]+\.jpg$/),
      Body: fileContent,
      ContentType: mimeType,
      ACL: 'public-read',
    });

    expect(mockS3Client.send).toHaveBeenCalled();
    expect(mockS3Client.send.mock.calls[0][0]).toEqual(
      expect.objectContaining({
        Bucket: mockFastify.config.AWS_BUCKET,
        Key: expect.stringMatching(/^[a-z0-9]+-[a-f0-9]+\.jpg$/),
        Body: fileContent,
        ContentType: mimeType,
        ACL: 'public-read',
      }),
    );

    expect(result).toMatch(`${mockFastify.config.AWS_S3_PATH}/`);
    expect(result).toMatch(/^https:\/\/mock-bucket\.s3\.amazonaws\.com\/[a-z0-9]+-[a-f0-9]+\.jpg$/);
  });

  it('should throw an error if file size exceeds limit', async () => {
    const fileContent = Buffer.alloc(6 * 1024 * 1024); // 6 MB
    const originalFilename = 'large_file.jpg';
    const mimeType = 'image/jpeg';

    await expect(uploadToS3(fileContent, originalFilename, mimeType, mockFastify)).rejects.toThrow(
      'Failed to upload file: File size exceeds 5 MB limit',
    );
  });

  it('should throw an error if file type is not allowed', async () => {
    const fileContent = Buffer.from('test file content');
    const originalFilename = 'test.gif';
    const mimeType = 'image/gif';

    await expect(uploadToS3(fileContent, originalFilename, mimeType, mockFastify)).rejects.toThrow(
      'Failed to upload file: Invalid file type. Allowed types are JPEG, JPG, PNG, and PDF.',
    );
  });
});

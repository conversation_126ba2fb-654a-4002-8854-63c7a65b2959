import { describe, expect, it, vi } from 'vitest';
import { decodeJWT } from '#src/utils/jwt.util.js';

describe('decodeJWT', () => {
  const mockDecoded = { id: 1, name: '<PERSON>' };

  it('should decode a valid token', async () => {
    const mockFastify = {
      log: { debug: vi.fn() },
      jwt: { verify: vi.fn().mockResolvedValue(mockDecoded) },
    };

    const token = 'valid.jwt.token';
    const result = await decodeJWT(token, mockFastify);

    expect(mockFastify.jwt.verify).toHaveBeenCalledWith(token);
    expect(result).toEqual(mockDecoded);
    expect(mockFastify.log.debug).not.toHaveBeenCalled();
  });

  it('should return null if no token is passed', async () => {
    const mockFastify = {
      log: { debug: vi.fn() },
      jwt: { verify: vi.fn() },
    };
    const result = await decodeJWT(undefined, mockFastify);

    expect(result).toBeNull();
    expect(mockFastify.log.debug).toHaveBeenCalledWith('No JWT token provided');
    expect(mockFastify.jwt.verify).not.toHaveBeenCalled();
  });

  it('should log and return undefined on decode error', async () => {
    const mockFastify = {
      log: { debug: vi.fn() },
      jwt: { verify: vi.fn().mockRejectedValue(new Error('Invalid token')) },
    };

    const result = await decodeJWT('invalid.token', mockFastify);

    expect(result).toBeNull();
    expect(mockFastify.jwt.verify).toHaveBeenCalledWith('invalid.token');
    expect(mockFastify.log.debug).toHaveBeenCalledWith(
      expect.any(Error),
      'Failed to verify JWT token',
    );
  });
});

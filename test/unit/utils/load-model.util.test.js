import { access, readdir } from 'node:fs/promises';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { extname, join } from 'node:path';
import { getModulesPath } from '#src/utils/file.util.js';
import { pathToFileURL } from 'node:url';

vi.mock('node:fs/promises');
vi.mock('node:path');
vi.mock('node:url');
vi.mock('#src/utils/file.util.js');
vi.mock('sequelize');

import * as loadModelHelper from '#src/utils/load-model.util.js';

describe('load-model.helper', () => {
  let fastifyMock;

  beforeEach(() => {
    fastifyMock = {
      log: {
        error: vi.fn(),
        info: vi.fn(),
      },
      mongo: {},
      psql: {},
    };
    vi.resetAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('autoloadModels', () => {
    it('should handle different scenarios for autoloading models', async () => {
      // Test case 1: Unsupported model type
      await loadModelHelper.autoloadModels(fastifyMock, 'unsupported');
      expect(fastifyMock.log.error).toHaveBeenCalledWith('Unsupported model type: unsupported');

      // Reset mock calls
      vi.clearAllMocks();

      // Test case 2: Successfully load models for each module
      getModulesPath.mockReturnValue('/path/to/modules');
      readdir.mockResolvedValue(['module1', 'module2']);

      await loadModelHelper.autoloadModels(fastifyMock, 'mongo');

      expect(readdir).toHaveBeenCalledWith('/path/to/modules');
      expect(fastifyMock.log.error).not.toHaveBeenCalled();

      // Reset mock calls
      vi.clearAllMocks();

      // Test case 3: Error reading modules directory
      getModulesPath.mockReturnValue('/path/to/modules');
      readdir.mockRejectedValue(new Error('Failed to read directory'));

      await loadModelHelper.autoloadModels(fastifyMock, 'mongo');

      expect(fastifyMock.log.error).toHaveBeenCalledWith(
        expect.any(Error),
        'Error autoloading models',
      );
    });
  });

  describe('loadModuleModels', () => {
    it('should load models for a specific module', async () => {
      const modulesDir = '/path/to/modules';
      const module = 'testModule';
      const type = 'mongo';
      const modelConfig = loadModelHelper.MODEL_TYPES[type];

      access.mockResolvedValue(undefined);
      readdir.mockResolvedValue(['model1.js', 'model2.js']);

      await loadModelHelper.loadModuleModels(
        fastifyMock,
        type,
        modulesDir,
        module,
        null,
        modelConfig,
      );

      expect(access).toHaveBeenCalledWith(join(modulesDir, module, 'models', type));
      expect(readdir).toHaveBeenCalledWith(join(modulesDir, module, 'models', type));
    });

    it('should handle ENOENT error when accessing model directory', async () => {
      const modulesDir = '/path/to/modules';
      const module = 'testModule';
      const type = 'mongo';
      const modelConfig = loadModelHelper.MODEL_TYPES[type];

      const enoentError = new Error('ENOENT');
      enoentError.code = 'ENOENT';
      access.mockRejectedValue(enoentError);

      await loadModelHelper.loadModuleModels(
        fastifyMock,
        type,
        modulesDir,
        module,
        null,
        modelConfig,
      );

      expect(fastifyMock.log.error).not.toHaveBeenCalled();
    });

    it('should handle non-ENOENT errors when accessing model directory', async () => {
      const modulesDir = '/path/to/modules';
      const module = 'testModule';
      const type = 'mongo';
      const modelConfig = loadModelHelper.MODEL_TYPES[type];

      access.mockRejectedValue(new Error('Some other error'));

      await loadModelHelper.loadModuleModels(
        fastifyMock,
        type,
        modulesDir,
        module,
        null,
        modelConfig,
      );

      expect(fastifyMock.log.error).toHaveBeenCalledWith(
        expect.any(Error),
        `Error accessing model directory for module ${module}`,
      );
    });
  });

  describe('loadModelFile', () => {
    it('should skip non-JavaScript files', async () => {
      extname.mockReturnValue('.txt');

      await loadModelHelper.loadModelFile(
        fastifyMock,
        '/path/to/models',
        'example.txt',
        'exampleModule',
        null,
        loadModelHelper.MODEL_TYPES.mongo,
      );

      expect(fastifyMock.log.info).not.toHaveBeenCalled();
    });

    it('should handle errors when loading model file', async () => {
      extname.mockReturnValue('.js');
      join.mockReturnValue('/path/to/models/example.model.js');
      pathToFileURL.mockReturnValue(new URL('file:///path/to/models/example.model.js'));

      vi.mock(
        'file:///path/to/models/example.model.js',
        () => {
          throw new Error('Model loading error');
        },
        { virtual: true },
      );

      await loadModelHelper.loadModelFile(
        fastifyMock,
        '/path/to/models',
        'example.model.js',
        'exampleModule',
        null,
        loadModelHelper.MODEL_TYPES.mongo,
      );

      expect(fastifyMock.log.error).toHaveBeenCalledWith(
        expect.any(Error),
        'Error loading model example.model.js from module exampleModule',
      );
    });
  });
});

describe('loadModelFile', () => {
  let fastifyMock, modelConfig;

  beforeEach(() => {
    fastifyMock = {
      log: {
        error: vi.fn(),
        info: vi.fn(),
      },
      mongo: {},
    };

    modelConfig = {
      decoratorName: 'mongo',
      getModelName: vi.fn(),
      isValidModel: vi.fn(),
    };

    vi.spyOn(loadModelHelper, 'dynamicImport');
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should handle non-JS files', async () => {
    await loadModelHelper.loadModelFile(
      fastifyMock,
      '/path/to/models',
      'model.txt',
      'testModule',
      null,
      modelConfig,
    );

    expect(loadModelHelper.dynamicImport).not.toHaveBeenCalled();
  });
});

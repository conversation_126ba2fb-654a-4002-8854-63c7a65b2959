import { beforeEach, describe, expect, it, vi } from 'vitest';
import i18next from 'i18next';

import * as v from '#src/utils/validation-message.util.js';

vi.mock('i18next');

describe('Validation Message Utility', () => {
  const keyPrefix = 'error.validation.sentence.';

  beforeEach(() => {
    vi.clearAllMocks();

    i18next.t = vi.fn().mockImplementation((key, options) => {
      if (!options?.attribute) {
        return `${key.split('.').pop()}`;
      }

      const { attribute, ...other } = options;
      let message = `${key};${options.attribute};`;
      Object.keys(other).forEach((key) => {
        message += `${key}:${other[key]};`;
      });
      return message;
    });
  });

  describe('createAttributeMessage', () => {
    const { createAttributeMessage } = v.exportForUnitTest;

    it('should translate validation message with attribute', () => {
      const key = 'key';
      const attribute = 'attribute';

      const result = createAttributeMessage(i18next, key, attribute);

      expect(result).toEqual(`${keyPrefix}${key};${attribute};`);
    });

    it('should translate validation message with attribute and extra parameters', () => {
      const key = 'key';
      const attribute = 'attribute';
      const extra = { key: 'value' };

      const result = createAttributeMessage(i18next, key, attribute, extra);

      expect(result).toEqual(`${keyPrefix}${key};${attribute};key:value;`);
    });
  });

  describe('m_required', () => {
    it('should translated required validation message without extra parameters', () => {
      const key = 'required';
      const attribute = 'username';

      const result = v.m_required({ i18next, attribute });

      expect(result).toEqual(`${keyPrefix}${key};${attribute};`);
    });
  });

  describe('m_enum', () => {
    it('should translated enum validation message with extra parameters', () => {
      const key = 'enum';
      const attribute = 'status';
      const params = {
        allowedValues: ['active', 'inactive'],
      };

      const result = v.m_enum({ i18next, attribute, params });

      expect(result).toEqual(`${keyPrefix}${key};${attribute};values:activecomma inactive;`);
    });

    describe('m_format', () => {
      it('should translated format validation message with extra parameters', () => {
        const key = 'format';
        const attribute = 'id';
        const params = {
          format: 'uuid',
        };

        const result = v.m_format({ i18next, attribute, params });

        expect(result).toEqual(`${keyPrefix}${key};${attribute};format:uuid;`);
      });
    });

    describe('m_type', () => {
      it('should translated type validation message with extra parameters', () => {
        const key = 'type';
        const attribute = 'amount';
        const params = {
          type: 'number',
        };

        const result = v.m_type({ i18next, attribute, params });

        expect(result).toEqual(`${keyPrefix}${key};${attribute};type:number;`);
      });
    });

    describe('m_minLength', () => {
      it('should translated minLength validation message with extra parameters', () => {
        const key = 'minLength';
        const attribute = 'amount';
        const params = {
          limit: 10,
        };

        const result = v.m_minLength({ i18next, attribute, params });

        expect(result).toEqual(`${keyPrefix}${key};${attribute};limit:10;`);
      });
    });

    describe('m_maxLength', () => {
      it('should translated maxLength validation message with extra parameters', () => {
        const key = 'maxLength';
        const attribute = 'amount';
        const params = {
          limit: 10,
        };

        const result = v.m_maxLength({ i18next, attribute, params });

        expect(result).toEqual(`${keyPrefix}${key};${attribute};limit:10;`);
      });
    });

    describe('m_minimum', () => {
      it('should translated minimum validation message with extra parameters', () => {
        const key = 'minimum';
        const attribute = 'amount';
        const params = {
          limit: 10,
        };

        const result = v.m_minimum({ i18next, attribute, params });

        expect(result).toEqual(`${keyPrefix}${key};${attribute};limit:10;`);
      });
    });

    describe('m_maximum', () => {
      it('should translated maximum validation message with extra parameters', () => {
        const key = 'maximum';
        const attribute = 'amount';
        const params = {
          limit: 10,
        };

        const result = v.m_maximum({ i18next, attribute, params });

        expect(result).toEqual(`${keyPrefix}${key};${attribute};limit:10;`);
      });
    });
  });
});

import { describe, expect, it } from 'vitest';
import { createCustomError } from '#src/utils/error.util.js';

describe('createCustomError', () => {
  it('should create a custom error with the correct name format based on the module name', () => {
    const code = 'CUSTOM_ERROR';
    const message = 'An error occurred';
    const statusCode = 400;
    const moduleName = 'Test';

    const CustomError = createCustomError(code, message, moduleName, statusCode);
    const errorInstance = new CustomError();

    expect(errorInstance).toBeInstanceOf(Error);
    expect(errorInstance.code).toBe('CUSTOM_ERROR');
    expect(errorInstance.name).toBe('TestModuleError');
    expect(errorInstance.message).toBe('An error occurred');
    expect(errorInstance.statusCode).toBe(statusCode);
  });

  it('should include additional metadata when provided as the last argument', () => {
    const code = 'CUSTOM_ERROR';
    const message = 'An error occurred: %s';
    const statusCode = 400;
    const moduleName = 'Test';
    const metaData = { detail: 'Additional error details' };

    const CustomError = createCustomError(code, message, moduleName, statusCode);
    const errorInstance = new CustomError('Test error message', metaData);

    expect(errorInstance).toBeInstanceOf(Error);
    expect(errorInstance.name).toBe('TestModuleError');
    expect(errorInstance.message).toBe('An error occurred: Test error message');
    expect(errorInstance.statusCode).toBe(statusCode);
    expect(errorInstance.metaData).toEqual(metaData);
  });

  it('should not include metadata if the last argument is not an object', () => {
    const code = 'CUSTOM_ERROR';
    const message = 'An error occurred: %s';
    const statusCode = 400;
    const moduleName = 'Test';

    const CustomError = createCustomError(code, message, moduleName, statusCode);
    const errorInstance = new CustomError('Test error message', 1);

    expect(errorInstance).toBeInstanceOf(Error);
    expect(errorInstance.name).toBe('TestModuleError');
    expect(errorInstance.message).toBe('An error occurred: Test error message 1');
    expect(errorInstance.statusCode).toBe(statusCode);
    expect(errorInstance.metaData).toEqual({});
  });

  it('should correctly instantiate a custom error with a message template and arguments', () => {
    const code = 'TEMPLATE_ERROR';
    const message = 'An error occurred: %s';
    const statusCode = 500;
    const moduleName = 'Template';

    const CustomError = createCustomError(code, message, moduleName, statusCode);
    const errorInstance = new CustomError('Template argument');

    expect(errorInstance).toBeInstanceOf(Error);
    expect(errorInstance.name).toBe('TemplateModuleError');
    expect(errorInstance.message).toBe('An error occurred: Template argument');
    expect(errorInstance.statusCode).toBe(statusCode);
    expect(errorInstance.metaData).toEqual({});
  });
});

import { beforeEach, describe, expect, it, vi } from 'vitest';
import i18next from 'i18next';

import * as v from '#src/utils/validation-message.util.js';
import {
  exportForUnitTest,
  translateDropdownItem,
  translateMessage,
  translateValidationErrors,
} from '#src/utils/i18next.util.js';

vi.mock('#src/utils/validation-message.util.js');

describe('validationKeywordHandlers', () => {
  const { validationKeywordHandlers } = exportForUnitTest;

  it('should return array in keyword: function format', () => {
    expect(validationKeywordHandlers).toEqual({
      required: v.m_required,
      enum: v.m_enum,
      format: v.m_format,
      type: v.m_type,
      minLength: v.m_minLength,
      maxLength: v.m_maxLength,
      minimum: v.m_minimum,
      maximum: v.m_maximum,
    });
  });
});

describe('translateDropdownItem', () => {
  const mockT = vi.fn();
  const mockI18next = { t: mockT };
  const mockTranslated = 'mock translated';

  beforeEach(() => {
    vi.clearAllMocks();

    mockT.mockReturnValue(mockTranslated);
  });

  it('should translate dropdown item with name correctly', () => {
    const data = [{ id: 1, name: 'Name' }];
    const result = translateDropdownItem(data, mockI18next);

    expect(result).toEqual([{ ...data[0], name: mockTranslated }]);
    expect(mockT).toHaveBeenCalledWith(data[0].name);
  });

  it('should translate dropdown item with name wrapped in dataValues correctly', () => {
    const data = [{ dataValues: { id: 2, name: 'Name' } }];
    const result = translateDropdownItem(data, mockI18next);

    expect(result).toEqual([{ ...data[0].dataValues, name: mockTranslated }]);
    expect(mockT).toHaveBeenCalledWith(data[0].dataValues.name);
  });

  it('should translate multiple record correctly', () => {
    const data = [
      { id: 1, name: 'Name 1' },
      { id: 2, name: 'Name 2' },
    ];
    const result = translateDropdownItem(data, mockI18next);

    expect(result).toEqual([
      { ...data[0], name: mockTranslated },
      { ...data[1], name: mockTranslated },
    ]);
    expect(mockT).toHaveBeenCalledWith(data[0].name);
    expect(mockT).toHaveBeenCalledWith(data[1].name);
  });

  // add 2 record translate

  it('should return for invalid data format as is', () => {
    const item = [{ id: 3, key: 'Name' }];
    const result = translateDropdownItem(item, mockI18next);

    expect(result).toEqual(item);
  });
});

/**
 * Test suite for translateMessage function.
 * This describe block contains tests for various scenarios of message translation.
 */
describe('translateMessage', () => {
  const mockT = vi.fn();
  const mockI18next = { t: mockT };

  beforeEach(() => {
    mockT.mockClear();
  });

  /**
   * Test case: Translating a string message.
   * Verifies that a simple string message is correctly translated.
   */
  it('should translate a string message', () => {
    mockT.mockReturnValue('Translated message');
    const result = translateMessage('message.key', {}, mockI18next);
    expect(result).toBe('Translated message');
    expect(mockT).toHaveBeenCalledWith('message.key', {});
  });

  /**
   * Test case: Translating a string message.
   * Verifies that a simple string message is correctly translated.
   */
  it('should return original message if translation is the same', () => {
    mockT.mockReturnValue('message.key');
    const result = translateMessage('message.key', {}, mockI18next);
    expect(result).toBe('message.key');
    expect(mockT).toHaveBeenCalledWith('message.key', {});
  });

  /**
   * Test case: Handling interpolation parameters.
   * Checks if interpolation parameters are correctly handled during translation.
   */
  it('should handle interpolation params', () => {
    mockT.mockReturnValue('Hello, John!');
    const result = translateMessage('greeting', { name: 'John' }, mockI18next);
    expect(result).toBe('Hello, John!');
    expect(mockT).toHaveBeenCalledWith('greeting', { name: 'John' });
  });

  /**
   * Test case: Translating an array of strings.
   * Verifies that an array of strings is correctly translated.
   */
  it('should handle array of strings', () => {
    mockT.mockImplementation((key, options) => {
      if (key === 'untranslatable') {
        return key;
      }
      return `Translated ${key} ${options.extra || ''}`.trim();
    });

    const input = ['key1', 'key2', 'untranslatable', 'key3'];
    const interpolationParams = {
      details: [{ extra: 'param1' }, {}, { extra: 'ignored' }, { extra: 'param3' }],
    };

    const result = translateMessage(input, interpolationParams, mockI18next);

    expect(result).toEqual([
      'Translated key1 param1',
      'Translated key2',
      'untranslatable',
      'Translated key3 param3',
    ]);
  });

  /**
   * Test case: Handling non-string, non-array messages.
   * Ensures that non-string and non-array messages are returned as-is without translation.
   */
  it('should return non-string, non-array message as is', () => {
    const input = { key: 'value' };
    const result = translateMessage(input, {}, mockI18next);
    expect(result).toEqual(input);
    expect(mockT).not.toHaveBeenCalled();
  });

  /**
   * Test case: Using default i18next instance.
   * Ensures that the default i18next instance is used when no custom instance is provided.
   */
  it('should use default i18next instance if not provided', () => {
    const spy = vi.spyOn(i18next, 't').mockReturnValue('Default Translated');
    const result = translateMessage('key');
    expect(result).toBe('Default Translated');
    expect(spy).toHaveBeenCalledWith('key', {});
    spy.mockRestore();
  });
});

/**
 * Test suite for translateValidationErrors function.
 * This describe block contains tests for translating validation errors.
 */
describe('translateValidationErrors', () => {
  const mockT = vi.fn();
  const mockI18next = { t: mockT };
  const mockValidationMessage = 'mock validation message';

  beforeEach(() => {
    mockT.mockClear();

    v.m_required.mockReturnValue(mockValidationMessage);
  });

  /**
   * Test case: Translating a single validation error.
   * Verifies that a single validation error is correctly translated,
   * including the attribute name (username) in the translated message.
   */
  it('should translate a single validation error', () => {
    const errors = [{ instancePath: '/username', keyword: 'required' }];
    const expected = [{ username: mockValidationMessage }];

    const result = translateValidationErrors(errors, mockI18next);

    expect(v.m_required).toHaveBeenCalledWith({
      i18next: mockI18next,
      attribute: 'username',
      params: errors[0].params,
    });
    expect(result).toEqual(expected);
  });

  /**
   * Test case: Translating multiple validation errors.
   * Ensures that multiple validation errors are correctly translated
   * into an array of objects, with each error message including its respective attribute name.
   */
  it('should translate multiple validation errors', () => {
    const errors = [
      { instancePath: '/username', keyword: 'required' },
      { instancePath: '/password', keyword: 'required' },
    ];
    const expected = [{ username: mockValidationMessage }, { password: mockValidationMessage }];

    const result = translateValidationErrors(errors, mockI18next);

    expect(v.m_required).toHaveBeenCalledWith({
      i18next: mockI18next,
      attribute: 'username',
      params: errors[0].params,
    });
    expect(v.m_required).toHaveBeenCalledWith({
      i18next: mockI18next,
      attribute: 'password',
      params: errors[1].params,
    });
    expect(result).toEqual(expected);
  });

  /**
   * Test case: Handling an empty validation errors array.
   * Verifies that an empty array of errors results in an empty array.
   */
  it('should handle empty validation errors array', () => {
    const errors = [];
    const expected = [];
    const result = translateValidationErrors(errors, mockI18next);

    expect(v.m_required).not.toHaveBeenCalled();
    expect(result).toEqual(expected);
  });

  /**
   * Test case: Handling errors without instancePath.
   * Ensures that errors without an instancePath are still translated correctly,
   * returning the message with a 'general' attribute.
   */
  it('should handle validation errors without instancePath', () => {
    const mockAttribute = 'email';
    const errors = [{ keyword: 'required', params: { missingProperty: mockAttribute } }];
    const expected = [{ email: mockValidationMessage }];

    const result = translateValidationErrors(errors, mockI18next);

    expect(v.m_required).toHaveBeenCalledWith({
      i18next: mockI18next,
      attribute: mockAttribute,
      params: errors[0].params,
    });
    expect(result).toEqual(expected);
  });

  /**
   * Test case: Handling unsupported validation keywords.
   * Verifies that when an unsupported validation keyword is encountered,
   * the system falls back to a default error message format that includes
   * the keyword and original validation message.
   */
  it('should fallback to default validation error message for unsupported keyword', () => {
    const errors = [
      {
        instancePath: '/email',
        keyword: 'unsupported',
        message: 'default validation error message',
      },
    ];
    const expected = [{ email: `VE(${errors[0].keyword}): ${errors[0].message}` }];

    const result = translateValidationErrors(errors, mockI18next);

    expect(result).toEqual(expected);
  });
});

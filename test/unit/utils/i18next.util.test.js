import { beforeEach, describe, expect, it, vi } from 'vitest';
import { translateMessage, translateValidationErrors } from '#src/utils/i18next.util.js';
import i18next from 'i18next';

/**
 * Test suite for translateMessage function.
 * This describe block contains tests for various scenarios of message translation.
 */
describe('translateMessage', () => {
  const mockT = vi.fn();
  const mockI18next = { t: mockT };

  beforeEach(() => {
    mockT.mockClear();
  });

  /**
   * Test case: Translating a string message.
   * Verifies that a simple string message is correctly translated.
   */
  it('should translate a string message', () => {
    mockT.mockReturnValue('Translated message');
    const result = translateMessage('message.key', {}, 'message', mockI18next);
    expect(result).toBe('Translated message');
    expect(mockT).toHaveBeenCalledWith('message.key', {});
  });

  /**
   * Test case: Translating a string message.
   * Verifies that a simple string message is correctly translated.
   */
  it('should return original message if translation is the same', () => {
    mockT.mockReturnValue('message.key');
    const result = translateMessage('message.key', {}, 'message', mockI18next);
    expect(result).toBe('message.key');
    expect(mockT).toHaveBeenCalledWith('message.key', {});
  });

  /**
   * Test case: Handling interpolation parameters.
   * Checks if interpolation parameters are correctly handled during translation.
   */
  it('should handle interpolation params', () => {
    mockT.mockReturnValue('Hello, John!');
    const result = translateMessage('greeting', { name: 'John' }, 'message', mockI18next);
    expect(result).toBe('Hello, John!');
    expect(mockT).toHaveBeenCalledWith('greeting', { name: 'John' });
  });

  /**
   * Test case: Translating an array of strings.
   * Verifies that an array of strings is correctly translated.
   */
  it('should handle array of strings', () => {
    mockT.mockImplementation((key, options) => {
      if (key === 'untranslatable') {
        return key;
      }
      return `Translated ${key} ${options.extra || ''}`.trim();
    });

    const input = ['key1', 'key2', 'untranslatable', 'key3'];
    const interpolationParams = {
      details: [{ extra: 'param1' }, {}, { extra: 'ignored' }, { extra: 'param3' }],
    };

    const result = translateMessage(input, interpolationParams, 'message', mockI18next);

    expect(result).toEqual([
      'Translated key1 param1',
      'Translated key2',
      'untranslatable',
      'Translated key3 param3',
    ]);
  });

  /**
   * Test case: Handling dropdown type with object items.
   * Ensures that dropdown type messages with object items are correctly translated.
   */
  it('should handle dropdown type with object items', () => {
    mockT.mockReturnValue('Translated Name');
    const input = [{ id: 1, name: 'Original Name' }];
    const result = translateMessage(input, {}, 'dropdown', mockI18next);
    expect(result).toEqual([{ id: 1, name: 'Translated Name' }]);
    expect(mockT).toHaveBeenCalledWith('Original Name', { ns: 'string' });
  });

  /**
   * Test case: Handling dropdown type with dataValues.
   * Verifies that dropdown type messages with dataValues are correctly translated.
   */
  it('should handle dropdown type with dataValues', () => {
    mockT.mockReturnValue('Translated Name');
    const input = [{ dataValues: { id: 1, name: 'Original Name' } }];
    const result = translateMessage(input, {}, 'dropdown', mockI18next);
    expect(result).toEqual([{ id: 1, name: 'Translated Name' }]);
    expect(mockT).toHaveBeenCalledWith('Original Name', { ns: 'string' });
  });

  /**
   * Test case: Handling non-string, non-array messages.
   * Ensures that non-string and non-array messages are returned as-is without translation.
   */
  it('should return non-string, non-array message as is', () => {
    const input = { key: 'value' };
    const result = translateMessage(input, {}, 'message', mockI18next);
    expect(result).toEqual(input);
    expect(mockT).not.toHaveBeenCalled();
  });

  /**
   * Test case: Using default i18next instance.
   * Ensures that the default i18next instance is used when no custom instance is provided.
   */
  it('should use default i18next instance if not provided', () => {
    const spy = vi.spyOn(i18next, 't').mockReturnValue('Default Translated');
    const result = translateMessage('key');
    expect(result).toBe('Default Translated');
    expect(spy).toHaveBeenCalledWith('key', {});
    spy.mockRestore();
  });
});

/**
 * Test suite for translateValidationErrors function.
 * This describe block contains tests for translating validation errors.
 */
describe('translateValidationErrors', () => {
  const mockT = vi.fn();
  const mockI18next = { t: mockT };

  beforeEach(() => {
    mockT.mockClear();
    mockI18next.t.mockImplementation((key, params) => {
      const stringKey = String(key);
      if (stringKey.includes('{{attribute}}')) {
        return `${params.attribute} ${stringKey.replace('{{attribute}} ', '')}`;
      }
      return stringKey;
    });
  });

  /**
   * Test case: Translating a single validation error.
   * Verifies that a single validation error is correctly translated,
   * including the attribute name (username) in the translated message.
   */
  it('should translate a single validation error', () => {
    const errors = [
      {
        instancePath: '/username',
        message: 'must NOT have fewer than {{limit}} characters',
        params: { limit: 3 },
      },
    ];
    const expected = [{ username: 'username must NOT have fewer than {{limit}} characters' }];
    const result = translateValidationErrors(errors, mockI18next);
    expect(result).toEqual(expected);
  });

  /**
   * Test case: Translating multiple validation errors.
   * Ensures that multiple validation errors are correctly translated
   * into an array of objects, with each error message including its respective attribute name.
   */
  it('should translate multiple validation errors', () => {
    const errors = [
      {
        instancePath: '/username',
        message: 'must NOT have fewer than {{limit}} characters',
        params: { limit: 3 },
      },
      {
        instancePath: '/password',
        message: 'must NOT have fewer than {{limit}} characters',
        params: { limit: 8 },
      },
    ];
    const expected = [
      {
        username: 'username must NOT have fewer than {{limit}} characters',
      },
      {
        // eslint-disable-next-line sonarjs/no-hardcoded-passwords
        password: 'password must NOT have fewer than {{limit}} characters',
      },
    ];
    const result = translateValidationErrors(errors, mockI18next);
    expect(result).toEqual(expected);
  });

  /**
   * Test case: Handling an empty validation errors array.
   * Verifies that an empty array of errors results in an empty array.
   */
  it('should handle empty validation errors array', () => {
    const errors = [];
    const expected = [];
    const result = translateValidationErrors(errors, mockI18next);
    expect(result).toEqual(expected);
  });

  /**
   * Test case: Handling errors without instancePath.
   * Ensures that errors without an instancePath are still translated correctly,
   * returning the message with a 'general' attribute.
   */
  it('should handle errors without instancePath', () => {
    const errors = [
      {
        message: "must have required property '{{missingProperty}}'",
        params: { missingProperty: 'email' },
      },
    ];

    mockI18next.t.mockImplementation((key, params) => {
      return key.replace(/{{(\w+)}}/g, (_, p) => params[p] || _);
    });

    const expected = [{ email: "must have required property 'email'" }];

    const result = translateValidationErrors(errors, mockI18next);
    expect(result).toEqual(expected);
  });
});

import { beforeEach, describe, expect, it, vi } from 'vitest';
import { DatabaseFactory } from '#src/factories/database.factory.js';
import { autoloadModels } from '#src/utils/load-model.util.js';
import fp from 'fastify-plugin';

// Mock the external dependencies
vi.mock('#src/utils/load-model.util.js');
vi.mock('fastify-plugin');

describe('DatabaseFactory', () => {
  let mockFastify;

  beforeEach(() => {
    // Reset all mocks before each test
    vi.resetAllMocks();

    // Mock Fastify instance
    mockFastify = {
      decorate: vi.fn(),
    };

    // Mock fastify-plugin
    fp.mockImplementation((fn) => fn);
  });

  it('should create an instance with fastify', () => {
    const factory = new DatabaseFactory(mockFastify);
    expect(factory.fastify).toBe(mockFastify);
  });

  it('should throw an error when connect is called on base class', async () => {
    const factory = new DatabaseFactory(mockFastify);
    await expect(factory.connect()).rejects.toThrow(
      'connect method must be implemented by subclasses',
    );
  });

  it('should throw an error when decorate is called on base class', () => {
    const factory = new DatabaseFactory(mockFastify);
    expect(() => factory.decorate()).toThrow('decorate method must be implemented by subclasses');
  });

  describe('createPlugin', () => {
    it('should create a plugin that connects, decorates, and loads models', async () => {
      class TestDatabaseFactory extends DatabaseFactory {
        async connect() {}
        decorate() {}
      }

      const plugin = TestDatabaseFactory.createPlugin('test', true);
      await plugin(mockFastify);

      expect(autoloadModels).toHaveBeenCalledWith(mockFastify, 'test', null);
    });

    it('should not load models if loadModels is false', async () => {
      class TestDatabaseFactory extends DatabaseFactory {
        async connect() {}
        decorate() {}
      }

      const plugin = TestDatabaseFactory.createPlugin('test', false);
      await plugin(mockFastify);

      expect(autoloadModels).not.toHaveBeenCalled();
    });

    it('should call connect and decorate methods of subclass', async () => {
      const connectMock = vi.fn();
      const decorateMock = vi.fn();

      class TestDatabaseFactory extends DatabaseFactory {
        async connect() {
          connectMock();
        }
        decorate() {
          decorateMock();
        }
      }

      const plugin = TestDatabaseFactory.createPlugin('test');
      await plugin(mockFastify);

      expect(connectMock).toHaveBeenCalled();
      expect(decorateMock).toHaveBeenCalled();
    });
  });
});

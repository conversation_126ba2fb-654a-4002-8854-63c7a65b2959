import { beforeEach, describe, expect, it, vi } from "vitest";
import { Kafka } from 'kafkajs';
import { lambdaHandler, localHandler } from '../../src/handler.js';
import * as utils from '../../src/util.js';

vi.mock('kafkajs', () => {
  const Kafka = vi.fn();
  Kafka.prototype.consumer = vi.fn();
  return { Kafka };
});

const clientMock = vi.fn();

describe("localHandler unit test", () => {
  const sampleLog1 = '{"timestamp":"2025-03-01T12:34:56Z","details":{"request":{}}}';
  const sampleLog2 = '{"timestamp":"2025-04-01T12:34:56Z","details":{"request":{}}}';
  const kafkaMsgsMock = [
    { value: Buffer.from(sampleLog1) },
    { value: Buffer.from(sampleLog2) }
  ];
  const contextMock = { awsRequestId: 'test-request-id' };
  const consumerMock = vi.mocked(Kafka.prototype.consumer);
  const heartbeatMock = vi.fn();
  let processLogMock = '';
  let saveLogMock = '';

  beforeEach(() => {
    vi.resetAllMocks();
    processLogMock = vi.spyOn(utils, 'processLog');
    saveLogMock = vi.spyOn(utils, 'saveLog').mockResolvedValue('mock');

    consumerMock.mockReturnValue({
      connect: vi.fn(),
      subscribe: vi.fn(),
      disconnect: vi.fn(),
      run: vi.fn().mockImplementation(({ eachBatch }) => {
        return eachBatch({
          batch: { messages: kafkaMsgsMock },
          heartbeat: heartbeatMock,
        });
      }),
    });

    process.env.KAFKA_BROKERS = 'localhost:9092';
    process.env.KAFKA_GROUP_ID = 'log-consumer-lambda';
    process.env.KAFKA_TOPIC = 'audit-trails';
  });

  it("should initiate Kafka with correct options", async () => {

    const kafkaOptions = { clientId: 'test-request-id', brokers: ['localhost:9092'] };
    const consumerOptions = { groupId: 'log-consumer-lambda' };
    const subscribeOptions = { topic: 'audit-trails', fromBeginning: true };

    await localHandler(contextMock);

    const consumerInstance = Kafka.mock.results[0].value.consumer();

    expect(Kafka).toHaveBeenCalledWith(kafkaOptions);
    expect(consumerMock).toHaveBeenCalledWith(consumerOptions);

    expect(consumerInstance.subscribe).toHaveBeenCalledTimes(1);
    expect(consumerInstance.subscribe).toHaveBeenCalledWith(subscribeOptions);

    expect(consumerInstance.disconnect).toHaveBeenCalledTimes(1);
  });

  it("should process the log and send the log to save function with correct format", async () => {

    await localHandler(contextMock, clientMock);

    const consumerInstance = Kafka.mock.results[0].value.consumer(); // Access the mocked consumer

    expect(consumerInstance.run).toHaveBeenCalledTimes(1);

    expect(processLogMock).toHaveBeenCalledTimes(kafkaMsgsMock.length);

    const expectedSaveLogParams = {};
    expectedSaveLogParams["2025_03"] = [{
      ...JSON.parse(sampleLog1),
      timestamp: new Date(JSON.parse(sampleLog1).timestamp)
    }];
    expectedSaveLogParams["2025_04"] = [{
      ...JSON.parse(sampleLog2),
      timestamp: new Date(JSON.parse(sampleLog2).timestamp)
    }];

    expect(saveLogMock).toHaveBeenCalledWith(expectedSaveLogParams, clientMock);
  });

  it("should invoke heartbeat on each processes row", async () => {

    await localHandler(contextMock);

    expect(heartbeatMock).toHaveBeenCalledTimes(kafkaMsgsMock.length);
  });

  it("should handle error during processing", async () => {

    heartbeatMock.mockImplementation(() => { throw new Error('Mock Error'); });

    await localHandler(contextMock);

    const consumerInstance = Kafka.mock.results[0].value.consumer();
    expect(consumerInstance.disconnect).toHaveBeenCalledTimes(1);
  });
});

describe("lambdaHandler unit test", () => {

  const sampleLog1 = '{"timestamp":"2025-03-01T12:34:56Z","details":{"request":{}}}';
  const sampleLog2 = '{"timestamp":"2025-04-01T12:34:56Z","details":{"request":{}}}';
  const mskEventsMock = {
    "records": {
      "audit-trails-0": [
        { value: Buffer.from(sampleLog1).toString('base64') },
        { value: Buffer.from(sampleLog2).toString('base64') }
      ]
    }
  };

  it("should process the log and send the log to save function with correct format", async () => {

    const processLogMock = vi.spyOn(utils, 'processLog');
    const saveLogMock = vi.spyOn(utils, 'saveLog').mockResolvedValue('mock');

    await lambdaHandler(mskEventsMock, clientMock);

    expect(processLogMock).toHaveBeenCalledTimes(mskEventsMock.records["audit-trails-0"].length);

    const expectedSaveLogParams = {};
    expectedSaveLogParams["2025_03"] = [{
      ...JSON.parse(sampleLog1),
      timestamp: new Date(JSON.parse(sampleLog1).timestamp)
    }];
    expectedSaveLogParams["2025_04"] = [{
      ...JSON.parse(sampleLog2),
      timestamp: new Date(JSON.parse(sampleLog2).timestamp)
    }];

    expect(saveLogMock).toHaveBeenCalledWith(expectedSaveLogParams, clientMock);
  });
});
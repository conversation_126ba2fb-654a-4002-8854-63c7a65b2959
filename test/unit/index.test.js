import { beforeEach, describe, expect, it, vi } from "vitest";
import { handler, initDb, mockForUnitTest } from "../../src/index";
import { MongoClient } from "mongodb";
import * as handlers from "../../src/handler";

vi.mock('mongodb', () => {
  const MongoClient = vi.fn();
  MongoClient.prototype.connect = vi.fn();
  MongoClient.prototype.db = vi.fn();
  return { MongoClient };
});

describe("initDb unit test", () => {
  const uri = 'mock-uri';
  process.env.MONGO_URI = uri;

  beforeEach(() => {
    vi.resetAllMocks();
  });

  it("should initiate with AWS config in production", async () => {

    // Initiate test data
    const awsAccessKeyId = 'awsAccessKeyId';
    const awsSecretAccessKey = 'awsSecretAccessKey';
    process.env.AWS_ACCESS_KEY_ID = awsAccessKeyId;
    process.env.AWS_SECRET_ACCESS_KEY = awsSecretAccessKey;
    process.env.APP_ENV = 'production';

    // Expected options
    const options = {
      auth: {
        username: awsAccessKeyId,
        password: awsSecretAccessKey
      },
      authSource: '$external',
      authMechanism: 'MONGODB-AWS'
    };

    await initDb();

    expect(MongoClient).toHaveBeenCalledWith(uri, options);
  });

  it("should initiate with empty options in local", async () => {

    // Initiate test data
    process.env.APP_ENV = 'local';

    await initDb();

    expect(MongoClient).toHaveBeenCalledWith(uri, {});
  });
});

describe("handler unit test", () => {
  const clientMock = 'clientMock';

  beforeEach(() => {
    vi.resetAllMocks();

    // mock client variable
    mockForUnitTest(clientMock);
  });

  it("should call lambdaHandler when APP_ENV is set to 'production'", async () => {
    process.env.APP_ENV = 'production';

    const event = { "event": 1 };
    const context = { "context": 1 };

    const lambdaHandlerMock = vi.spyOn(handlers, 'lambdaHandler').mockResolvedValue('mockLambda');
    const localHandlerMock = vi.spyOn(handlers, 'localHandler').mockResolvedValue('mockLocal');

    const result = await handler(event, context);

    expect(lambdaHandlerMock).toHaveBeenCalledWith(event, clientMock);
    expect(localHandlerMock).not.toHaveBeenCalled();
    expect(result).toBe("Done");
  });

  it("should call localHandler when APP_ENV is not 'production'", async () => {
    process.env.APP_ENV = 'local';

    const event = { "event": 1 };
    const context = { "context": 1 };

    const lambdaHandlerMock = vi.spyOn(handlers, 'lambdaHandler').mockResolvedValue('mockLambda');
    const localHandlerMock = vi.spyOn(handlers, 'localHandler').mockResolvedValue('mockLocal');

    const result = await handler(event, context);

    expect(localHandlerMock).toHaveBeenCalledWith(context, clientMock);
    expect(lambdaHandlerMock).not.toHaveBeenCalled();
    expect(result).toBe("Done");
  });
});

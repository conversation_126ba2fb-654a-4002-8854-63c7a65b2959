volumes:
  helper: # Volume to store the built command
    external: true

services:
  # Runs a Watchtower instance
  watchtower:
    container_name: watchtower
    image: containrrr/watchtower
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ${HOME}/.docker/config.json:/config.json
      - helper:/go/bin
    environment:
      - WATCHTOWER_CLEANUP=${WATCHTOWER_CLEANUP} # clean up old image after auto update
      - WATCHTOWER_ROLLING_RESTART=${WATCHTOWER_ROLLING_RESTART} # specify perform restart one by one instead of all together
      - WATCHTOWER_LABEL_ENABLE=true # enable auto update with label (only those labeled will watch for update)
      - WATCHTOWER_HTTP_API_UPDATE=true # enable API webhook for auto update
      - WATCHTOWER_HTTP_API_TOKEN=${WATCHTOWER_HTTP_API_TOKEN} # API webhook token
      - HOME=/
      - PATH=$PATH:/go/bin
      - AWS_REGION=${AWS_REGION}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
    ports:
      - ${WATCHTOWER_PORT}:8080
    # command: --interval 3

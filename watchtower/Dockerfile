## Run this to create AWS credential helper
## Create a volume to store the command (once built)
#docker volume create helper

## Build the container
#docker build -t aws-ecr-dock-cred-helper .

## Build the command and store it in the new volume in the /go/bin directory.
#docker run  -d --rm --name aws-cred-helper --volume helper:/go/bin aws-ecr-dock-cred-helper

FROM golang:1.21

ENV GO111MODULE off
ENV CGO_ENABLED 0
ENV REPO github.com/awslabs/amazon-ecr-credential-helper/ecr-login/cli/docker-credential-ecr-login

RUN go get -u "$REPO"

RUN rm /go/bin/docker-credential-ecr-login

RUN go build \
 -o /go/bin/docker-credential-ecr-login \
 /go/src/"$REPO"

WORKDIR /go/bin/

# These are some examples of commonly ignored file patterns.
# You should customize this list as applicable to your project.
# Learn more about .gitignore:
#     https://www.atlassian.com/git/tutorials/saving-changes/gitignore

# Dependencies
node_modules/
/.pnp
.pnp.js
.yarn/
package-lock.json
yarn.lock
pnpm-lock.yaml

# Compiled Java class files
*.class

# Compiled Python bytecode
*.py[cod]

# Log files
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
debug.log

# Package files
*.jar

# Maven
target/
dist/

# IDE and editors
.idea/
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/launch.json
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.sublime-workspace
.project
.classpath
.settings/
.history/

# Testing
/coverage/
/cypress/videos/
/cypress/screenshots/
/test-results/
tsconfig.vitest-temp.json
.nyc_output

# Next.js
/.next/
/out/
/build/
next-env.d.ts

# Production
/dist/
/build/

# Debug & Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
debug.log
*.log

# Environment Variables
.env
.env.*
!.env.example

# TypeScript
*.tsbuildinfo
tsconfig.tsbuildinfo

# Cache & Temporary
.cache/
.temp/
*.tmp
*.temp
*.swp
*.swo

# Build tools & Dependencies
.eslintcache
.stylelintcache
.rollup.cache/
.webpack/
storybook-static/
.turbo

# Misc
*.pem
.DS_Store
Thumbs.db
.vercel
.scannerwork/
.husky/_

# PWA files
**/public/sw.js
**/public/workbox-*.js
**/public/worker-*.js
**/public/sw.js.map
**/public/workbox-*.js.map
**/public/worker-*.js.map

# Optional: Uncomment if using these features
# /public/sitemap*.xml
# /public/robots.txt
# /.storybook/static/
# /src/graphql/generated/
# /.terraform/
# *.tfstate
# *.tfstate.backup

*storybook.log

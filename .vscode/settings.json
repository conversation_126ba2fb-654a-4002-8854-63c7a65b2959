{"sonarlint.connectedMode.project": {"connectionId": "https-sonarqube-sgrts-com-", "projectKey": "aiodintech_wlsapi_0a1557ce-64fe-4c27-a074-9e25cb78421c"}, "eslint.useFlatConfig": true, "eslint.options": {"overrideConfigFile": "config/eslint.config.js"}, "editor.codeActionsOnSave": {"source.fixAll.eslint": "always"}, "prettier.configPath": "config/prettier.config.js", "editor.formatOnSave": true, "[javascript]": {"editor.formatOnSave": true}, "prettier.enableDebugLogs": true}
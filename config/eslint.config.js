import globals from 'globals';
import prettier from 'eslint-plugin-prettier';
import prettierConfig from './prettier.config.js';
import sonarjs from 'eslint-plugin-sonarjs';

export default [
  sonarjs.configs.recommended,
  {
    plugins: {
      prettier,
    },
    files: ['**/*.js'],
    languageOptions: {
      globals: {
        ...globals.node,
        ...globals.jest,
        Atomics: 'readonly',
        SharedArrayBuffer: 'readonly',
      },
      ecmaVersion: 'latest',
      sourceType: 'module',
    },
    rules: {
      'prettier/prettier': ['error', prettierConfig],
      curly: 'error',
      'no-console': 'warn',
      'sort-imports': [
        'error',
        {
          ignoreCase: false,
          ignoreDeclarationSort: false,
          ignoreMemberSort: false,
          memberSyntaxSortOrder: ['none', 'all', 'multiple', 'single'],
          allowSeparatedGroups: true,
        },
      ],
      'sonarjs/todo-tag': 'warn',
    },
    linterOptions: {
      reportUnusedDisableDirectives: 'off',
    },
  },
  {
    files: ['**/*.test.js', '**/*.test.ts'],
    rules: {
      'sonarjs/no-hardcoded-passwords': 'off',
    },
  },
  {
    files: ['**/config/**/*.js'],
    rules: {
      'sonarjs/no-hardcoded-passwords': 'off',
    },
  },
  { languageOptions: { globals: globals.browser } },
];

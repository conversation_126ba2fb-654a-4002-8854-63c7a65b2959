/** @type {import('prettier').Config} */
const config = {
  arrowParens: 'always',
  bracketSameLine: false,
  bracketSpacing: true,
  endOfLine: 'lf',
  htmlWhitespaceSensitivity: 'css',
  insertPragma: false,
  printWidth: 100,
  proseWrap: 'preserve',
  quoteProps: 'as-needed',
  requirePragma: false,
  semi: true,
  singleAttributePerLine: true,
  singleQuote: true,
  tabWidth: 2,
  trailingComma: 'es5',
  useTabs: false,
  importOrder: [
    '<BUILTIN_MODULES>', // Node.js built-in modules
    '<THIRD_PARTY_MODULES>', // Imports not matched by other special words or groups.
    '^[.]', // relative imports
  ],
  plugins: ['@ianvs/prettier-plugin-sort-imports'],
  overrides: [
    {
      files: ['**/*.json', '*.json'],
      options: {
        bracketSpacing: false,
        parser: 'json',
        printWidth: 120,
        semi: false,
        trailingComma: 'none',
        useTabs: true,
      },
    },
    {
      files: ['**/*.mdx', '*.mdx'],
      options: {
        parser: 'mdx',
        printWidth: 80,
        proseWrap: 'always',
        singleQuote: true,
      },
    },
  ],
};

export default config;

sonar.projectKey=aiodintech_uifort-bo_7201b7cf-28d9-49d6-9e2f-b38835d853b3
sonar.projectName=uifort-bo
sonar.projectVersion=0.0.1
sonar.projectDescription=Frontend repository for WLS Back Office.
sonar.sourceEncoding=UTF-8

# Limit in MB for files to be discarded from the analysis scope, default is 20 MB
sonar.filesize.limit=20

# Define separate root directories for sources and tests
sonar.sources=src
sonar.test.exclusions=src/**/*.test.ts,src/**/*.test.tsx,src/**/*.spec.ts,src/**/*.spec.tsx

# Set to true for DEBUG mode
sonar.verbose=false
sonar.scanner.keepReport=true

# Wait for the quality gate status in analysis step
sonar.qualitygate.wait=true

# The number of seconds that the scanner should wait for a report to be processed
sonar.qualitygate.timeout=300

# A piece of code is considered duplicated as soon as there are
# at least 100 (default) duplicated tokens in a row (override with sonar.cpd.${language}.minimumTokens)
# spread across at least 10 (default) lines of code (override with sonar.cpd.${language}.minimumLines).
sonar.cpd.typescript.minimumTokens=100
sonar.cpd.typescript.minimumLines=5

# Threshold for the maximum size of analyzed files (in kilobytes). Files that are larger are excluded from the analysis.
sonar.typescript.maxFileSize=1000

# To allow the analysis to use more memory (4096 or 8192)
sonar.typescript.node.maxspace=8192

# Paths (absolute or relative) to the files with LCOV data.
sonar.typescript.lcov.reportPaths=coverage/lcov.info

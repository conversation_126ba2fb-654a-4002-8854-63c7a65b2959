sonar.projectKey=aiodintech_fastify-boilerplate_7212e4af-a2de-425f-923e-8eeaa8aff487
sonar.projectName=fastify-boilerplate
sonar.projectVersion=0.0.4
sonar.projectDescription=This project was bootstrapped with Fastify-CLI.
sonar.sourceEncoding=UTF-8

# Limit in MB for files to be discarded from the analysis scope, default is 20 MB
sonar.filesize.limit=20

# Define separate root directories for sources and tests
sonar.sources=src
sonar.tests=./test/unit

# Set to true for DEBUG mode
sonar.verbose=false
sonar.scanner.keepReport=true

# Wait for the quality gate status in analysis step
sonar.qualitygate.wait=true

# The number of seconds that the scanner should wait for a report to be processed
sonar.qualitygate.timeout=300

# A piece of code is considered duplicated as soon as there are
# at least 100 (default) duplicated tokens in a row (override with sonar.cpd.${language}.minimumTokens)
# spread across at least 10 (default) lines of code (override with sonar.cpd.${language}.minimumLines).
sonar.cpd.javascript.minimumTokens=100
sonar.cpd.javascript.minimumLines=5

# Threshold for the maximum size of analyzed files (in kilobytes). Files that are larger are excluded from the analysis.
sonar.javascript.maxFileSize=1000

# To allow the analysis to use more memory (4096 or 8192)
sonar.javascript.node.maxspace=8192

# Paths (absolute or relative) to the files with LCOV data.
sonar.javascript.lcov.reportPaths=coverage/lcov.info

import pino from 'pino';

import { serializeError, serializeReply, serializeRequest } from '#src/utils/serializer.util.js';
import { getLogFilePath } from '#src/utils/file.util.js';

// Define stream configuration for PostgreSQL logs
const streamToPostgresFile = {
  level: process.env.LOG_LEVEL || 'trace',
  stream: pino.transport({
    target: 'pino-roll',
    options: {
      dateFormat: 'yyyy-MM-dd',
      extension: '.log',
      file: `${getLogFilePath()}-postgres`,
      frequency: 'daily',
      limit: { count: 30 },
      mkdir: true,
    },
  }),
};

// Define stream configuration for logging to console
const streamToConsole = {
  level: 'info',
  stream: pino.transport({
    target: 'pino-pretty',
    options: {
      colorize: true,
      ignore: 'pid,hostname,level-label',
    },
  }),
};

// Define stream configuration for logging to file
// Logs are stored in UTC format
// Files are rotated daily at 00:00 UTC
const streamToFile = {
  level: process.env.LOG_LEVEL || 'trace',
  stream: pino.transport({
    target: 'pino-roll',
    options: {
      dateFormat: 'yyyy-MM-dd',
      extension: '.log',
      file: getLogFilePath(),
      frequency: 'daily',
      limit: { count: 30 },
      mkdir: true,
    },
  }),
};

const streams = [streamToConsole, streamToFile];

const pinoOptions = {
  level: process.env.LOG_LEVEL || 'trace',
  base: {
    env: process.env.NODE_ENV || 'development',
    service: process.env.APP_NAME,
  },
  mixin(_context, level) {
    return {
      'level-label': pino.levels.labels[level],
    };
  },
  timestamp: pino.stdTimeFunctions.isoTime,
  redact: {
    paths: ['headers.authorization'],
    remove: false, // Set to true if you want to remove both the key and value
  },
  serializers: {
    error: (error) => serializeError(error),
    reply: (reply) => serializeReply(reply),
    request: (request) => serializeRequest(request),
  },
};

const postgresOptions = {
  ...pinoOptions,
  level: process.env.LOG_LEVEL || 'trace',
  base: {
    ...pinoOptions.base,
    module: 'postgres',
  },
};

export const pinoLogger = pino(
  pinoOptions,
  pino.multistream(streams), // Use multi-stream for logging
);

export const postgresLogger = pino(postgresOptions, pino.multistream([streamToPostgresFile]));

import dotenv from 'dotenv';
import { postgresLogger } from '#config/pino-logger.config.js';

dotenv.config();

const logger = postgresLogger;

const baseConfig = {
  benchmark: true,
  database: process.env.POSTGRES_DB ?? 'dev',
  dialect: process.env.DB_DIALECT ?? 'postgres',
  host: process.env.POSTGRES_HOST ?? 'localhost',
  logQueryParameters: true,
  password: process.env.POSTGRES_PASSWORD ?? 'password',
  pool: { max: 10, idle: 30000, acquire: 60000 },
  port: process.env.POSTGRES_PORT ?? '5432',
  username: process.env.POSTGRES_USER ?? 'postgres',
  logging: (sql, duration) => {
    logger.info(
      {
        query: sql,
        executionTimeMs: duration,
        parameters: sql.parameters,
      },
      'Query executed',
    );
  },
  replication: {
    read: [{}],
    write: {
      host: process.env.POSTGRES_HOST ?? 'localhost',
      username: process.env.POSTGRES_USER ?? 'postgres',
      password: process.env.POSTGRES_PASSWORD ?? 'password',
    },
  },
};

const development = {
  ...baseConfig,
  // Add any development-specific configurations here
};

const test = {
  ...baseConfig,
  // Add any test-specific configurations here
};

const production = {
  ...baseConfig,
  // Add any production-specific configurations here
};

const config = {
  development,
  test,
  production,
};

const currentEnv = process.env.NODE_ENV || 'development';

export default config[currentEnv];

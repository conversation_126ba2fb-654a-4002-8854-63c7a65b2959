import { getPackageJson } from '../src/utils/file.util.js';

const packageJson = getPackageJson();

export const swaggerObject = {
  openapi: '3.0.0',
  info: {
    title: packageJson.name,
    description: packageJson.description,
    version: packageJson.version,
  },
  servers: [
    {
      url: `http://localhost:${process.env.DOCKER_PORT || 3000}`,
      description: 'Development',
    },
    {
      url: process.env.SERVER_URL_STAGING || 'https://staging-api.example.com',
      description: 'Staging',
    },
    {
      url: process.env.SERVER_URL_PROD || 'https://api.example.com',
      description: 'Production',
    },
  ],
  tags: [
    { name: 'Example', description: 'Example API' },
    { name: 'Mongo', description: 'Example Mongo' },
    { name: 'BO / Users', description: 'User Management' },
  ],
  components: {
    securitySchemes: {
      apiKey: {
        type: 'apiKey',
        name: process.env.API_KEY_NAME || 'apiKey',
        in: 'header',
      },
    },
  },
  externalDocs: {
    url: process.env.EXTERNAL_DOCS_URL || 'https://swagger.io',
    description: process.env.EXTERNAL_DOCS_DESCRIPTION || 'Find more info here',
  },
};

export const swaggerConfig = {
  openapi: swaggerObject,
  exposeRoute: true,
  hideUntagged: true,
};

export const swaggerUiConfig = {
  routePrefix: process.env.SWAGGER_UI_PATH || '/api-docs',
  uiConfig: {
    docExpansion: 'none',
    deepLinking: true,
  },
  staticCSP: true,
  transformStaticCSP: (header) => header,
};

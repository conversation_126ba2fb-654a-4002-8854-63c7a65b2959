import { SwaggerTheme } from 'swagger-themes';
import { getPackageJson } from '#src/utils/file.util.js';
import path from 'path';
import { readFileSync } from 'fs';

const packageJson = getPackageJson();
const theme = new SwaggerTheme();

const availableThemes = ['muted', 'classic', 'dark', 'newspaper'];
const themeCssMap = {};
availableThemes.forEach((name) => {
  themeCssMap[name] = theme.getBuffer(name);
});

const defaultTheme = process.env.SWAGGER_THEME || availableThemes[0]; // fallback to first available;
const globalJs = `window.SWAGGER_CONFIG = {
    defaultTheme: '${defaultTheme}',
    availableThemes: ${JSON.stringify(availableThemes)}
  };`;

// Get custom theme CSS and JS for theme dropdown selector
const basePath = path.resolve('config/swagger');
const themeSelectorCss = readFileSync(path.join(basePath, 'swagger-theme.css'));
const themeSelectorJs = readFileSync(path.join(basePath, 'swagger-theme.js'));

export const swaggerObject = {
  openapi: '3.0.0',
  info: {
    title: packageJson.name,
    description: packageJson.description,
    version: packageJson.version,
  },
  servers: [
    {
      url: `http://localhost:${process.env.DOCKER_PORT || 3000}`,
      description: 'Development',
    },
    {
      url: process.env.SERVER_URL_STAGING || 'https://staging-api.example.com',
      description: 'Staging',
    },
    {
      url: process.env.SERVER_URL_PROD || 'https://api.example.com',
      description: 'Production',
    },
  ],
  tags: [
    // { name: 'BO / Settings ', description: 'Currency, Language, Regions' },
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
      },
      apiKey: {
        type: 'apiKey',
        name: process.env.API_KEY_NAME || 'apiKey',
        in: 'header',
      },
    },
  },
  security: [
    {
      bearerAuth: [],
    },
  ],
  externalDocs: {
    url: process.env.EXTERNAL_DOCS_URL || 'https://swagger.io',
    description: process.env.EXTERNAL_DOCS_DESCRIPTION || 'Find more info here',
  },
};

export const swaggerConfig = {
  openapi: swaggerObject,
  exposeRoute: true,
  hideUntagged: true,
};

export const swaggerUiConfig = {
  routePrefix: process.env.SWAGGER_UI_PATH || '/api-docs',
  uiConfig: {
    docExpansion: 'none',
    deepLinking: true,
  },
  staticCSP: true,
  transformStaticCSP: (header) => header,
  theme: {
    css: [
      ...availableThemes.map((name) => ({
        filename: `${name}.css`,
        content: themeCssMap[name],
      })),
      { filename: 'theme-selector.css', content: themeSelectorCss },
    ],
    js: [
      { filename: 'global.js', content: globalJs },
      { filename: 'swagger-theme.js', content: themeSelectorJs },
    ],
  },
};

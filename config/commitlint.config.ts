import type { UserConfig } from '@commitlint/types';

/**
 * Commitlint configuration for UIFort project
 *
 * Commit format: <type>(<clickup-id>): <subject>
 * Example: feat(CU-1234): add login functionality
 *
 * The scope must be a valid ClickUp ID in the format LETTERS-NUMBERS
 * Examples: CU-1234, TASK-5678
 */
const Configuration: UserConfig = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    // Allow mixed case for ClickUp IDs (e.g., CU-1234)
    'scope-case': [
      0,
      'always',
      [
        'lower-case',
        'upper-case',
        'camel-case',
        'kebab-case',
        'pascal-case',
        'sentence-case',
        'snake-case',
        'start-case',
      ],
    ],

    // Enforce subject case to be lowercase
    'subject-case': [2, 'always', ['lower-case']],

    // Enforce subject length
    'subject-max-length': [2, 'always', 150],

    // Enforce subject to not end with period
    'subject-full-stop': [2, 'never', '.'],

    // Enforce type case to be lowercase
    'type-case': [2, 'always', 'lower-case'],

    // Define allowed types
    'type-enum': [
      2,
      'always',
      [
        'build', // Changes that affect the build system or external dependencies
        'chore', // Routine tasks, maintenance, etc.
        'ci', // Changes to CI configuration files and scripts
        'docs', // Documentation only changes
        'feat', // A new feature
        'fix', // A bug fix
        'perf', // A code change that improves performance
        'refactor', // A code change that neither fixes a bug nor adds a feature
        'revert', // Reverts a previous commit
        'style', // Changes that do not affect the meaning of the code (white-space, formatting, etc)
        'test', // Adding missing tests or correcting existing tests
        'ui', // UI/UX improvements or changes
        'i18n', // Internationalization and localization
      ],
    ],

    // Allow any scope format that matches a ClickUp ID pattern
    // ClickUp IDs are typically in the format CU-xxxx or similar
    'scope-enum': [0, 'always', []],

    // Validate scope format using a custom rule
    'scope-empty': [2, 'never'],
  },
  prompt: {
    questions: {
      type: {
        description: "Select the type of change you're committing:",
        enum: {
          feat: {
            description: 'A new feature',
            title: 'Features',
            emoji: '✨',
          },
          fix: {
            description: 'A bug fix',
            title: 'Bug Fixes',
            emoji: '🐛',
          },
          docs: {
            description: 'Documentation only changes',
            title: 'Documentation',
            emoji: '📚',
          },
          style: {
            description: 'Changes that do not affect the meaning of the code',
            title: 'Styles',
            emoji: '💎',
          },
          refactor: {
            description: 'A code change that neither fixes a bug nor adds a feature',
            title: 'Code Refactoring',
            emoji: '📦',
          },
          perf: {
            description: 'A code change that improves performance',
            title: 'Performance Improvements',
            emoji: '🚀',
          },
          test: {
            description: 'Adding missing tests or correcting existing tests',
            title: 'Tests',
            emoji: '🚨',
          },
          build: {
            description: 'Changes that affect the build system or external dependencies',
            title: 'Builds',
            emoji: '🛠',
          },
          ci: {
            description: 'Changes to our CI configuration files and scripts',
            title: 'Continuous Integration',
            emoji: '⚙️',
          },
          chore: {
            description: "Other changes that don't modify src or test files",
            title: 'Chores',
            emoji: '♻️',
          },
          revert: {
            description: 'Reverts a previous commit',
            title: 'Reverts',
            emoji: '🗑',
          },
          ui: {
            description: 'UI/UX improvements or changes',
            title: 'UI/UX',
            emoji: '🎨',
          },
          i18n: {
            description: 'Internationalization and localization',
            title: 'Internationalization',
            emoji: '🌐',
          },
        },
      },
      scope: {
        description: 'Enter the ClickUp ID for this change (e.g. CU-1234)',
      },
      subject: {
        description:
          'Write a short, imperative tense description of the change (start with lowercase)',
      },
      body: {
        description: 'Provide a longer description of the change',
      },
      isBreaking: {
        description: 'Are there any breaking changes?',
      },
      breakingBody: {
        description:
          'A BREAKING CHANGE commit requires a body. Please enter a longer description of the commit itself',
      },
      breaking: {
        description: 'Describe the breaking changes',
      },
      isIssueAffected: {
        description: 'Does this change affect any open issues?',
      },
      issuesBody: {
        description:
          'If issues are closed, the commit requires a body. Please enter a longer description of the commit itself',
      },
      issues: {
        description: 'Add issue references (e.g. "fix #123", "re #123".)',
      },
    },
  },
};

export default Configuration;

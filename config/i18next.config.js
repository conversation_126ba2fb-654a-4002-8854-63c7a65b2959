const namespaces = ['api'];

function createI18nextOptions(fastify) {
  return {
    backend: {
      loadPath: `${fastify.config.LLS_CDN_BASE_URL}/{{lng}}/{{ns}}.json`,
      reloadInterval: 60000,
    },
    debug: false,
    defaultNS: 'api',
    detection: {
      order: ['header', 'querystring', 'cookie'],
      caches: false, // No caching on the server-side
    },
    fallbackLng: fastify.config.DEFAULT_LOCALE ? fastify.config.DEFAULT_LOCALE.split('-')[0] : 'en',
    interpolation: {
      escapeValue: false, // No need to escape on the server-side
    },
    logger: {
      type: 'logger',
      log: (args) => fastify.log.info('i18next log:', ...args),
      warn: (args) => fastify.log.warn('i18next warn:', ...args),
      error: (args) => fastify.log.error('i18next error:', ...args),
    },
    ns: namespaces,
    preload: ['en', 'zh'],
  };
}

const dateFormatOptions = {
  dateStyle: 'short',
  timeStyle: 'short',
};

const numberFormatOptions = {
  minimumFractionDigits: 0,
  maximumFractionDigits: 2,
};

export { createI18nextOptions, dateFormatOptions, numberFormatOptions };

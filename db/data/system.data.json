[{"themes": [{"field": "toastMessagePosition", "accessLevels": ["root"], "value": "bottom right"}, {"field": "alertBarPosition", "accessLevels": ["root"], "value": "top"}]}, {"safety": [{"field": "sessionLifetimeHours", "accessLevels": ["root"], "value": 24}, {"field": "passwordExpiryDays", "accessLevels": ["root"], "value": 3}, {"field": "passwordReuseCount", "accessLevels": ["root"], "value": 5}, {"field": "passwordMaximumAttempts", "accessLevels": ["root"], "value": 5}, {"field": "twoFactorSessionTimeoutDays", "accessLevels": ["root"], "value": 7}]}, {"personal": [{"field": "appearance", "accessLevels": ["root", "organisation", "merchant"], "value": "system"}, {"field": "recordPerPage", "accessLevels": ["root", "organisation", "merchant"], "value": 25}, {"field": "dateFormat", "accessLevels": ["root", "organisation", "merchant"], "value": "yyyy-mm-dd"}, {"field": "timeFormat", "accessLevels": ["root", "organisation", "merchant"], "value": "24"}, {"field": "defaultLanguage", "accessLevels": ["root", "organisation", "merchant"], "value": ""}, {"field": "defaultTimezone", "accessLevels": ["root", "organisation", "merchant"], "value": ""}]}]
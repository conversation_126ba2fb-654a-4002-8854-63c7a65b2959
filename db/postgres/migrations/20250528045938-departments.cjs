'use strict';

const path = require('path');
const { DataTypes } = require('sequelize');
const { addBaseColumns } = require(path.join(__dirname, '..', 'helpers', 'baseColumns.cjs'));

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await createTable(queryInterface, Sequelize);
    await addConstraints(queryInterface, Sequelize);
    await addIndexes(queryInterface);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('departments');
  },
};

const createTable = async (queryInterface, Sequelize) => {
  await queryInterface.createTable(
    'departments',
    addBaseColumns(
      {
        entity_id: {
          allowNull: true,
          type: DataTypes.UUID,
          // SC-01: wait for entities to be implemented
          // references: {
          //   model: 'entities',
          //   key: 'id',
          // },
          // onDelete: 'CASCADE',
          // onUpdate: 'CASCADE',
          comment: 'Reference to the entities',
        },
        name: {
          allowNull: false,
          type: Sequelize.STRING(50),
          comment: 'The name of the department',
        },
        hierarchy: {
          allowNull: false,
          type: 'enum_common_hierarchy',
          comment: 'The hierarchy of the department',
        },
        template: {
          allowNull: false,
          type: DataTypes.BOOLEAN,
          defaultValue: false,
          comment: 'The template of the department',
        },
        description: {
          allowNull: true,
          type: Sequelize.STRING(100),
          comment: 'The description of the department',
        },
        status: {
          allowNull: false,
          type: 'enum_common_status',
          defaultValue: 'active',
          comment: 'Current status of the department',
        },
      },
      {
        withParanoid: true,
      },
    ),
  );
};

const addConstraints = async (queryInterface, Sequelize) => {
  await queryInterface.addConstraint('departments', {
    fields: ['entity_id', 'name'],
    type: 'unique',
    name: 'unq_departments_entity_name',
    comment: 'Ensures each entity-name combination is unique',
  });
};

const addIndexes = async (queryInterface) => {
  await queryInterface.addIndex('departments', ['entity_id'], {
    name: 'idx_departments_entity',
    comment: 'Improves query performance when filtering by entity',
  });
  await queryInterface.addIndex('departments', ['entity_id', 'hierarchy'], {
    name: 'idx_departments_entity_hierarchy',
    comment: 'Improves query performance when filtering by entity and hierarchy',
  });
  await queryInterface.addIndex('departments', ['status'], {
    name: 'idx_departments_status',
    comment: 'Improves query performance when filtering by status',
  });
};

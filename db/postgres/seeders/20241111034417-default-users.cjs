'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const users = [
      { username: 'superadmin', name: 'superadmin' },
      { username: 'admin', name: 'admin' },
    ];

    await queryInterface.sequelize.query(
      `INSERT INTO "users" ("username", "name")
       VALUES
       ${users.map((user) => `('${user.username}', '${user.name}')`).join(', ')}
       ON CONFLICT ("username") DO UPDATE
       SET "name" = EXCLUDED."name";`,
    );

    console.log('Seed default user completed.');
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('users', null, {});

    console.log('Revert seed default user completed.');
  },
};

'use strict';

const DEFAULT_USER_SEEDER = require('./20241111034417-default-users.cjs');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    console.log('Seeding...');

    await DEFAULT_USER_SEEDER.up(queryInterface, Sequelize);

    console.log('All seeder completed.');
  },

  down: async (queryInterface, Sequelize) => {
    console.log('Revert seeding...');

    await DEFAULT_USER_SEEDER.down(queryInterface, Sequelize);

    console.log('All seeder completed!');
  },
};

'use strict';

const REDIS = require('ioredis');
const DATA = require('../../data/module-policy.data.json');

module.exports = {
  async up(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    let redis;

    try {
      console.log('Starting Module Seeder...');

      const { clearCacheWithPrefix } = await import('#src/utils/cache.util.js');

      const { default: ModuleModel } = await import(
        '#src/modules/core/models/postgres/module.model.js'
      );
      const { default: PolicySettingModel } = await import(
        '#src/modules/core/models/postgres/policy-setting.model.js'
      );

      const moduleModel = ModuleModel({ psql: { connection: queryInterface.sequelize } });
      const policySettingModel = PolicySettingModel({
        psql: { connection: queryInterface.sequelize },
      });

      // Initialize Redis connection
      redis = new REDIS({
        host: process.env.REDIS_HOST,
        port: process.env.REDIS_PORT,
        db: 2, // Use the second database for caching
      });

      const insertModule = async (node, index, parentIdMap = {}, level = 1) => {
        const moduleIdMap = {};

        const hierarchies = node.hierarchyPolicies
          ? node.hierarchyPolicies.map((hp) => hp.hierarchy)
          : deriveHierarchiesFromChildren(node);

        for (const hierarchyLevel of hierarchies) {
          const moduleData = {
            name: node.name,
            hierarchy: hierarchyLevel,
            parentId: parentIdMap[hierarchyLevel] || null,
            translationKey: node.translationKey || null,
            navigationPosition: index,
            navigationUrl: node.navigationUrl || null,
            level: level,
          };

          const [module] = await moduleModel.upsert(moduleData, { transaction });

          moduleIdMap[hierarchyLevel] = module.id;

          if (node.hierarchyPolicies) {
            const hierarchyPolicy = node.hierarchyPolicies.find(
              (hp) => hp.hierarchy === hierarchyLevel,
            );
            if (hierarchyPolicy) {
              await policySettingModel.upsert(
                {
                  parentId: module.id,
                  canView: hierarchyPolicy.policies.includes('canView'),
                  canCreate: hierarchyPolicy.policies.includes('canCreate'),
                  canEdit: hierarchyPolicy.policies.includes('canEdit'),
                  canImport: hierarchyPolicy.policies.includes('canImport'),
                  canExport: hierarchyPolicy.policies.includes('canExport'),
                  canManage: hierarchyPolicy.policies.includes('canManage'),
                  canMasking: hierarchyPolicy.policies.includes('canMasking'),
                  canOverwrite: hierarchyPolicy.policies.includes('canOverwrite'),
                  canVerify: hierarchyPolicy.policies.includes('canVerify'),
                },
                { transaction },
              );
            }
          }
        }

        if (node.children && node.children.length > 0) {
          for (let i = 0; i < node.children.length; i++) {
            await insertModule(node.children[i], i, moduleIdMap, level + 1);
          }
        }
      };

      const deriveHierarchiesFromChildren = (node) => {
        if (!node.children || node.children.length === 0) {
          return [];
        }

        const childHierarchies = node.children.flatMap((child) =>
          child.hierarchyPolicies
            ? child.hierarchyPolicies.map((hp) => hp.hierarchy)
            : deriveHierarchiesFromChildren(child),
        );

        return [...new Set(childHierarchies)]; // Remove duplicates
      };

      for (let i = 0; i < DATA.length; i++) {
        await insertModule(DATA[i], i);
      }

      await transaction.commit();

      // Clear Redis cache
      await clearCacheWithPrefix(redis, 'module_policy');
      console.log('module_policy cache cleared.');

      console.log('Module Seeder completed successfully.');
    } catch (error) {
      await transaction.rollback();
      console.error('Error in Module Seeder:', error);
      throw error;
    } finally {
      if (redis) {
        await redis.quit();
      }
    }
  },

  async down(queryInterface) {},
};

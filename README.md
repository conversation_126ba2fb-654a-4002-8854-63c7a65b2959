# Fastify Boilerplate 🚀

## Overview

This Fastify Boilerplate provides a lightweight and flexible starting point for building web applications with Fastify. Designed with simplicity and scalability in mind, it includes core configurations, a structured project setup, and essential tools to streamline development. Ideal for quickly bootstrapping projects, it supports easy customization and modularity to fit various application needs.


## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Tech Stack](#tech-stack)
- [Project Structure](#project-structure)
- [Installation](#installation)
- [Configuration](#configuration)
- [Database Migrations and Seeders](#database-migrations-and-seeders)
- [Available Scripts](#available-scripts)
- [API Documentation](#api-documentation)
- [Typesense Integration](#typesense-integration)
- [Troubleshooting](#troubleshooting)
- [Continuous Integration](#continuous-integration)
- [License](#license)
- [Learn More](#learn-more)
- [System Architecture](#system-architecture)

## Features

### 1. **Fastify Framework**
   - Built on Fastify, a high-performance web framework for Node.js, this boilerplate benefits from Fastify’s speed and low overhead.
   - Optimized for asynchronous operations, offering built-in JSON schema-based validation and serialization.

### 2. **Modular Folder Structure**
   - Organized folder structure for clear separation of concerns:
     - **Routes**: Each API route is modularized, allowing easy addition and management of endpoints.
     - **Plugins**: Custom or external plugins can be added here for Fastify, such as authentication, caching, or other middleware functions.
     - **Config**: Centralized configuration files to manage environment variables and application settings.
   - Ensures the project remains scalable as it grows, enabling smooth development and maintenance.

### 3. **Environment Configuration**
   - Uses `.env` files to securely manage environment variables.
   - Pre-configured to load environment settings through the `dotenv` package, making it simple to define application-specific configurations.

### 4. **Built-In Logging**
   - Leverages Fastify’s built-in logging system, using Pino for fast, structured logging.
   - Provides detailed logs for request/response cycles and error tracking, making debugging and monitoring efficient.

### 5. **Error Handling**
   - Centralized error handling mechanism ensures consistent responses across routes.
   - Customizable error responses with status codes and descriptive messages to improve API reliability.

### 6. **Basic Testing Setup**
   - Placeholder setup for adding testing libraries (e.g., Jest) to support unit and integration tests.
   - Provides a starting point for robust testing practices, ensuring code reliability and stability.

### 7. **Development and Production Ready**
   - Scripts for easy local development (`dev`), production builds, and deployment.
   - Configured to handle common development tasks, with options for expanding to staging and production environments.

### 8. **Lightweight and Extendable**
   - Minimal dependencies, keeping the boilerplate light and quick to set up.
   - Easily extendable to integrate databases, authentication, caching, or other necessary modules as the project requirements grow.

## Tech Stack

- **Backend Framework**: Fastify
- **Database**: PostgreSQL, MongoDB
- **Authentication**: Firebase Authentication with Identity Platform
- **Real-time Communication**: Socket.io
- **Storage**: AWS S3
- **Containerization**: Docker
- **Monitoring & Logging**: Pino, Sentry

## Project Structure

```bash
fastify-boilerplate/
├── .husky/
├── config/
│   ├── elk/
│   │   └── filebeat/
│   │   │   ├── Dockerfile.filebeat
│   │   │   └── filebeat.yml
│   ├── changelog-template.hbs
│   ├── commitlint.config.js
│   ├── eslint.config.js
│   ├── prettier.config.js
│   ├── sonar-project.properties
│   └── others
├── src/
│   ├── hooks/
│   ├── modules/
│   ├── migrations/
│   ├── models/
│   ├── plugins/
│   ├── schemas/
│   ├── seeders/
│   ├── utils/
│   ├── app.js
│   └── route.js
├── locales/
├── logs/
├── test/
├── .dockerignore
├── .editorconfig
├── .env.example
├── .gitignore
├── .nvmrc
├── .prettierignore
├── bitbucket-pipelines.yml
├── CHANGELOG.md
├── compose.yaml
├── Dockerfile
├── package-lock.json
├── package.json
└── README.md
```

## Usage
Clone the repository, install dependencies, and you’re ready to start building on top of a clean Fastify setup tailored for modern JavaScript applications.

## Installation

### Prerequisites

- [Node.js](https://nodejs.org/en/learn/getting-started/how-to-install-nodejs) (v20+)
- [Docker Desktop or Docker Engine](https://docs.docker.com/get-started/get-docker/) with [Docker Compose](https://docs.docker.com/get-started/workshop/08_using_compose/)

### Steps - Setting up the core services

1. Clone the docker repository [here](https://bitbucket.org/aiodintech/deployment-docker/src/main/) first to initiate all the core-services, such as postgres, mongo, redis etc.

   ```sh
   git clone https://bitbucket.org/aiodintech/deployment-docker.git
   ```

2. Navigate into the core services directory.

   ```sh
   cd deployment-docker/core_services
   ```

2. Create a `.env` file in the root directory and configure the environment variables accordingly.

   ```sh
   cp .env.example .env
   ```

3. Build and run the core services with Docker Compose:

   ```sh
   docker compose up -d
   ```

### Steps - Setting up the application

1. Clone the repository:

   ```sh
   git clone https://bitbucket.org/aiodintech/fastify-boilerplate.git
   cd fastify-boilerplate
   ```

2. Install the dependencies:

   ```sh
   npm install
   ```

3. Create a `.env` file in the root directory and configure the environment variables accordingly.

   ```sh
   cp .env.example .env
   ```

4. Build and run your application with Compose:

    ```sh
    docker compose up -d
    ```

    Learn more about setting up Docker [here](https://app.clickup.com/3712436/v/dc/3h9dm-61096/3h9dm-146876).


## Configuration

After copying the `.env.example` file to `.env`, configure the following environment variables:

| Variable                       | Description                                                                  |
| -------------------------------| ---------------------------------------------------------------------------- |
| `API_KEY_NAME`                 | Name of the API key for authentication in Swagger documentation.             |
| `APP_NAME`                     | Name of the application.                                                     |
| `AWS_ACCESS_KEY_ID`            | AWS Access Key ID for authentication.                                        |
| `AWS_BUCKET`                   | The name of the S3 bucket for file storage.                                  |
| `AWS_REGION`                   | AWS Region where the resources (e.g., S3 bucket) are located.                |
| `AWS_S3_PATH`                  | Path in the S3 bucket where files will be stored.                            |
| `AWS_SECRET_ACCESS_KEY`        | AWS Secret Access Key for authentication.                                    | 
| `BASE_URL`                     | Base URL of the application.                                                 |
| `DB_DIALECT`                   | Specifies the type of database using with Sequelize.                         |
| `DEFAULT_LOCALE`               | Default locale used in system                                                |
| `DOCKER_CONTAINER`             | True if you want to use Docker. Default is `false`.                          |
| `DOCKER_PORT`                  | The host bridge port to access to localhost port in docker container. Default is `3000`.|
| `ELASTIC_STACK_VERSION`        | Version of Elastic products (Elacticsearch, Kibana, etc.).                   |
| `EXTERNAL_DOCS_DESCRIPTION`    | Description for external documentation in Swagger.                           |
| `EXTERNAL_DOCS_URL`            | URL for external documentation in Swagger.                                   |
| `FASTIFY_ADDRESS`              | Host address Fastify will bind to. Use `0.0.0.0` for Docker.                 |
| `FASTIFY_PORT`                 | Port on which the Fastify server will run. Default is `3000`.                |
| `IMAGE_TAG`                    | Specify the image tag for the Docker images that are pushed to the Amazon Elastic Container Registry (ECR). |
| `LLS_CDN_BASE_URL`             | AWS CDN url to access the translation file exported from Language Localisation System to AWS. |
| `LOG_LEVEL`                    | Minimum logging level that system will log. Default is `trace`.              |
| `LOGSTASH_HOST`                | Hostname or IP address of the Logstash server.                               |
| `LOGSTASH_PORT`                | Port on which Logstash listens for incoming log data. Default is `5044`.     |
| `MONGO_DB`                     | Name of the MongoDB database.                                                |
| `MONGO_HOST`                   | Host address of the MongoDB instance.                                        |
| `MONGO_ROOT_PASSWORD`          | Password associated with the root user account for the MongoDB.              |
| `MONGO_ROOT_USERNAME`          | Initial root username for the MongoDB.                                       |
| `MONGO_PORT`                   | Port for MongoDB to accept connections. Default is 27017.                    |
| `NODE_ENV`                     | Specifies environment in which the app is running.                           |
| `POSTGRES_DB`                  | Database name in PostgreSQL.                                                 | 
| `POSTGRES_HOST`                | Host address of the PostgreSQL server. May use 'postgres' for Docker.        |
| `POSTGRES_PASSWORD`            | Password associated with the PostgreSQL user.                                |
| `POSTGRES_PORT`                | Port for PostgreSQL connections. Default is `5432`.                          |
| `POSTGRES_USER`                | Username for PostgreSQL authentication.                                      |
| `POSTGRES_READ_REPLICA_1_HOST`     | Read replica host in this format. POSTGRES_READ_REPLICA_{digit}_HOST     |
| `POSTGRES_READ_REPLICA_1_PORT`     | Read replica port in this format. POSTGRES_READ_REPLICA_{digit}_PORT     |
| `POSTGRES_READ_REPLICA_1_DB`       | Read replica db in this format. POSTGRES_READ_REPLICA_{digit}_DB         |
| `POSTGRES_READ_REPLICA_1_USER`     | Read replica user in this format. POSTGRES_READ_REPLICA_{digit}_USER     |
| `POSTGRES_READ_REPLICA_1_PASSWORD` | Read replica password in this format. POSTGRES_READ_REPLICA_{digit}_PASSWORD |
| `REDIS_HOST`                   | Host address of the Redis instance. May use 'redis' for Docker.              | 
| `REDIS_PASSWORD`               | Password used for authenticating with the Redis instance.                    |
| `REDIS_PORT`                   | Port for Redis connections. Default is `6379`.                               |
| `SENTRY_DSN`                   | DSN tells a Sentry SDK where to send events so the events are associated with the correct project. |
| `SERVER_URL_DEV`               | Development server URL for Swagger documentation.                            |
| `SERVER_URL_PROD`              | Production server URL for Swagger documentation.                             |
| `SERVER_URL_STAGING`           | Staging server URL for Swagger documentation.                                |
| `TYPESENSE_HOST`               | Hostname or IP address of the Typesense server.                              |
| `TYPESENSE_PORT`               | Port on which Typesense is running. Default is usually 8108.                 |
| `TYPESENSE_PROTOCOL`           | Protocol to use for Typesense connection (http or https).                    |
| `TYPESENSE_API_KEY`            | API key for authenticating with the Typesense server.                        |

Ensure that all these variables are correctly configured for both **development** and **production** environments.

## Database Migrations and Seeders

### Postgres Database

The project uses Sequelize CLI to run postgres migrations and seeders to manage the database schema and populate initial data.

⚠️ Attention: 
  1. Since Sequelize v6 does not yet support ESM, we need to manually change the **migration**, **seeder** files from .js to .cjs.
  2. The **model** file code needs to be updated to use ESM format.

#### Commands

- To create migration

  ```sh
  npm run migrate:create <migration name>
  ```
  - migration name: kebab-case, example: add-name-to-users
  - need to define the new column in the model file
  
- To apply migrations:

  ```sh
  npm run migrate
  ```

- To revert only the most recent migration:

  ```sh
  npm run migrate:rollback
  ```

- To create seeder:

  ```sh
  npm run seed:create <seeder name>
  ```
  - seeder name: kebab-case, example: default-users

- To run the project default seeders:
  - need to add the new default seeder into the _database-seeder.cjs

  ```sh
  npm run seed
  ```

- To run specific seeder:

  ```sh
  npm run seed:one <seeder name>
  ```
 - seeder name: file of seeder, example: 20241111034417-demo-user.cjs

### Mongo Database


## Available Scripts

In the project directory, you can run:

- `npm run dev`: Start the app in dev mode. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.
- `npm start`: For production mode.
- `npm run test`: Run the test cases.
- `npm run lint`: Lint the code.
- `npm run lint-fix`: Potentially fix the code linting issues.
- `npm run format`: Format the code.
- `npm run version`: Generate changelog for the current version.

## API Documentation

API documentation is auto-generated using Swagger. To view the interactive API docs, start the server and visit:

- [http://localhost:3000/api-docs](http://localhost:3000/api-docs)

The documentation includes the following endpoints:

- `/user`: Manage user accounts (CRUD operations)

You can test endpoints directly from the Swagger UI.

## Typesense Integration

This project includes integration with Typesense, a fast, typo-tolerant search engine. Typesense is used for efficient and accurate search functionality within the application.

### What is Typesense?

Typesense is an open-source, typo-tolerant search engine that is designed to be easy to use, fast, and accurate. It's a great alternative to more complex search solutions, offering:

- Typo tolerance: It can understand and correct user typos automatically.
- Fast searches: Optimized for speed, returning results in milliseconds.
- Simple setup: Easy to install and configure, with a clean API.
- Sorting and faceting: Supports sorting results and faceted search.

For developers new to Typesense, it provides a way to add powerful search capabilities to your application without the complexity of some other search engines.

### Configuration

Typesense is configured using environment variables. Ensure the following variables are set in your `.env` file:

- `TYPESENSE_HOST`: The hostname or IP address of your Typesense server.
- `TYPESENSE_PORT`: The port on which Typesense is running (default is usually 8108).
- `TYPESENSE_PROTOCOL`: The protocol to use for connecting to Typesense (http or https).
- `TYPESENSE_API_KEY`: The API key for authenticating with the Typesense server.

### Usage

The Typesense client is initialized in the `typesense.plugin.js` file. This plugin sets up the connection to the Typesense server and makes the client available throughout the application.

To use Typesense in your routes or services:

1. Access the Typesense client through the Fastify instance:

  ```javascript
  const typesenseClient = fastify.typesense;
  ```

## Troubleshooting

- **Server fails to start**:

  - Ensure the `.env` file is configured correctly and that all services (PostgreSQL, MongoDB, etc.) are running.
  - On your local, you may need to run `npm i` to install node_modules first.

- **Database connection issues**:
  - Check that the correct database credentials are being used.
  - Make sure the value is match with your docker setup value.
  - Ensure the database is running on the specified `DB_HOST` and `DB_PORT`.

## Continuous Integration

This project uses **GitHub Actions** for continuous integration. Each time a pull request is opened, tests and linting are run automatically. To set up the CI locally, ensure that you have the following dependencies:

- Docker with Docker Compose
- Node.js

The CI pipeline includes the following steps:

- Run tests (`npm run test`)
- Lint code (`npm run lint`)

## License

This project is licensed under the MIT License, which allows for commercial use, modification, distribution, and private use of the software. See the [LICENSE](LICENSE) file for full details.

## Learn More

- **Docker**: [Get Started with Docker](https://docs.docker.com/get-started/)
- **Fastify**: [Fastify Official Documentation](https://fastify.dev/docs/latest/)
- **PostgreSQL**: [PostgreSQL Performance Tuning Guide](https://www.postgresql.org/docs/current/performance-tips.html)
- **MongoDB**: [MongoDB Start with Guides](https://www.mongodb.com/docs/guides/)
- **Firebase Authentication**: [Getting Started with Firebase](https://firebase.google.com/docs/auth)
- **Elastic Stack**: [Getting started with the Elastic Stack and Docker Compose](https://www.elastic.co/blog/getting-started-with-the-elastic-stack-and-docker-compose)
- **Sequelize**: [Sequelize Migration & Seeder Guide](https://sequelize.org/docs/v6/other-topics/migrations/)
- **Typesense**: [Official Documentation](https://typesense.org/docs/)


## System Architecture

Here's a high-level overview of the system architecture:

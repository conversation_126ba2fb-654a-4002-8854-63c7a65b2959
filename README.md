# Log consumer

## Overview

This is a repo which contains code to process data stream from Kafka or AWS MSK event source by AWS Lambda.

## Table of contents
- [Overview](#overview)
- [Before start](#before-start)
- [Local environment](#local-environment)
- [Simulate production environment (MSK)](#simulate-production-environment-msk))
- [Project Structure](#project-structure)
- [Environment](#environment)

## Before start
- Duplicate .env.example and rename to .env.
- Update the .env value according to your local setup.
- Go to VS Code marketplace, search and install "REST Client"

## Local environment
- Open up test.http on root directory
- Click on "Send Request" on "Local environment" request
- If there are data in the steam, the data will be processed accordingly

## Simulate production environment (MSK)
- Open up test.http on root directory
- Click on "Send Request" on "Simulate production environment (MSK)" request
- The data will be processed depends on the values inside the "records" object

## Project Structure
```bash
log-consumer/
├── config/
│   └── vitest.config.js
├── src/
├── test/
├── .env.example
├── .gitignore
├── compose.yaml
├── Dockerfile
├── KakfaEvent.example
├── MSKEvent.example
├── package-lock.json
├── package.json
├── README.md
└── test.http
```

## Environment
| Variable                       | Description                                                                              |
| -------------------------------| ---------------------------------------------------------------------------------------- |
| `APP_ENV`                      | The application environment.                                                             |
| `FAIL_RETRY`                   | Number of retries if failed to insert to MongoDB                                         |
| `KAFKA_BROKERS`                | The Kafka brokers to connect. (Required if APP_ENV is "local")                           |
| `KAFKA_GROUP_ID`               | The group id used by consumer to communicate with Kafka (Required if APP_ENV is "local") |
| `KAFKA_TOPIC`                  | The data stream topic to subscribe to with Kafka. (Required if APP_ENV is "local")       |
| `MONGO_URI`                    | The connection string to connect to MongoDB                                              |
| `MONGO_DB`                     | The database name in MongoDB                                                             |

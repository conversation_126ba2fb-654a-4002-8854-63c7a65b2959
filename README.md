# Deployment Docker

## Overview

This project provides a set of Dockerized services that form the core of the application, along with a Watchtower service for automated updates. The core services handle the primary functionality of the application, while Watchtower ensures that Docker images are kept up to date in production.

This document explains how to set up and run these services using Docker Compose.

## Table of Contents

- [Overview](#overview)
- [Project Structure](#project-structure)
- [Installation](#installation)
- [Configuration](#configuration)

## Project Structure

```bash
deployment-docker/
├── core_services/
│   ├── elk-config
│   │   ├── elasticsearch
│   │   │   ├── templates
│   │   │   │   └── application-logs-template.json
│   │   ├── logstash
│   │   │   └── logstash.conf
│   ├── mongo
│   │   ├── create-user.js
│   │   └── Dockerfile
│   ├── postgres
│   │   ├── create-multiple-postgresql-databases.sh
│   │   └── Dockerfile
│   ├── .env.example
│   └── compose.yaml
├── game_aggregator/
│   ├── .env.example
│   └── compose.yaml
├── watchtower/
│   ├── .env.example
│   ├── compose.yaml
│   └── Dockerfile
├── wlsapi/
│   ├── .env.example
│   └── compose.yaml
├── .gitignore
└── README.md
```

## Installation

### How to run the Core Services with Docker

1. **Navigate to the project directory**
   - Open your terminal and navigate to the directory containing the project.
   ```bash
   cd core_services
   ```
2. **Set up the environment variables**
   - Duplicate the `.env.example` file to create a `.env` file:
   ```bash
   cp .env.example .env
   ```
   - Modify the necessary values to match your host configuration.
3. **Run the services**
   - For Local Development
     - Use the following command to run only the core services defined in `compose.yaml`:
     ```bash
     docker compose up -d
     ```
   - For Staging Environment (Server)
     - Navigate back to the main directory and go into wlsapi folder.
     - Duplicate the `.env.example` file to create a `.env` file:
     ```bash
     cp .env.example .env
     ```
     - Modify the necessary values to match your host configuration.
     - Run this command to start all services, including the `wlsapi` image pulled from Amazon ECR:
     ```bash
     docker compose up -d
     ```

### How to run the Watchtower service with Docker (Server)

1. **Navigate to the `watchtower` directory**
   - Open your terminal and navigate to the directory containing the Watchtower service:
   ```bash
   cd watchtower
   ```
2. **Set up the environment variables**
   - Duplicate the `.env.example` file to create a `.env` file:
   ```bash
   cp .env.example .env
   ```
   - Modify the necessary values to match your host configuration.
3. **Create aws-ecr-dock-cred-helper**
   ```bash
   docker volume create helper
   docker build -t aws-ecr-dock-cred-helper .
   docker run -d --rm --name aws-cred-helper --volume helper:/go/bin aws-ecr-dock-cred-helper
   ```
4. **Install AWS CLI**
   - Install the cli by following this [link](https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html).
   - After done install, run this to configure the credential.
   ```bash
   aws configure
   ```
5. **Install amazon-ecr-credential-helper**
   - Go to this [link](https://github.com/awslabs/amazon-ecr-credential-helper) and follow the install instruction depends on your operating system.
6. **Update docker config file**
   - Run this command to open up the file
   ```bash
   nano ~/.docker/config.json
   ```
   - Input this json data into the file. Note: Replace the AWS_ACCESS_ID and AWS_REGION
   ```bash
   {
       "auths": {
           "<AWS_ACCESS_ID>.dkr.ecr.<AWS_REGION>.amazonaws.com": {}
       },
       "credsStore": "ecr-login",
       "credHelpers": {
           "<AWS_ACCESS_ID>.dkr.ecr.<AWS_REGION>.amazonaws.com": "ecr-login",
           "public.ecr.aws": "ecr-login"
       }
   }
   ```
7. **Run the Watchtower service**
   - Start the Watchtower servic using Docker Compose:
   ```bash
   docker compose up -d
   ```

## Configuration

After copying the `.env.example` file to `.env`, configure the following environment variables:

### Core Services

| Variable                            | Description                                                                                                                          |
| ----------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------ |
| `ELASTIC_STACK_ENCRYPTION_KEY`      | Encryption key for its X-Pack security features, ensuring the security and confidentiality of sensitive data in the Kibana instance. |
| `ELASTIC_STACK_LICENSE`             | Type of Elastic Stack license, either basic or trial (30-day trial).                                                                 |
| `ELASTIC_STACK_VERSION`             | Version of Elastic products (Elacticsearch, Kibana, etc.).                                                                           |
| `ELASTICSEARCH_CLUSTER_NAME`        | Name for the Elastic Stack cluster, used for easier identification.                                                                  |
| `ELASTICSEARCH_HOST`                | Hostname or IP address of the Elasticsearch server.                                                                                  |
| `ELASTICSEARCH_MEM_LIMIT`           | Memory limit for Elasticsearch to avoid system overload, specified in bytes.                                                         |
| `ELASTICSEARCH_PASSWORD`            | Password for the 'elastic' user in Elasticsearch for administrative access.                                                          |
| `ELASTICSEARCH_PORT`                | Port to expose Elasticsearch HTTP API to the host. Default is `9200`.                                                                |
| `ELASTICSEARCH_USERNAME`            | Username for authenticating with Elasticsearch, typically 'elastic'.                                                                 |
| `KIBANA_HOST`                       | Hostname or IP address of the Kibana server.                                                                                         |
| `KIBANA_MEM_LIMIT`                  | Memory limit for Kibana, specified in bytes.                                                                                         |
| `KIBANA_PASSWORD`                   | Password for the 'kibana_system' user, which Kibana uses to authenticate to Elasticsearch.                                           |
| `KIBANA_PORT`                       | Port to expose Kibana to the host. Default is `5601`.                                                                                |
| `LOGSTASH_HOST`                     | Hostname or IP address of the Logstash server.                                                                                       |
| `LOGSTASH_MEM_LIMIT`                | Memory limit for Logstash (if used), specified in bytes.                                                                             |
| `LOGSTASH_PORT`                     | Port on which Logstash listens for incoming log data. Default is `5044`.                                                             |
| `POSTGRES_DB`                       | Database name in PostgreSQL.                                                                                                         |
| `POSTGRES_PASSWORD`                 | Password associated with the root PostgreSQL user.                                                                                   |
| `POSTGRES_PORT`                     | Host address of the PostgreSQL server.                                                                                               |
| `POSTGRES_USER`                     | Root username for PostgreSQL authentication.                                                                                         |
| `POSTGRES_PASSWORD_WLSAPI`          | PostgreSQL password for wlsapi app.                                                                                                  |
| `POSTGRES_USER_WLSAPI`              | PostgreSQL username for wlsapi app.                                                                                                  |
| `POSTGRES_PASSWORD_GAME_AGGREGATOR` | PostgreSQL password for game aggregator app.                                                                                         |
| `POSTGRES_USER_GAME_AGGREGATOR`     | PostgreSQL username for game aggregator app.                                                                                         |
| `MONGO_DB`                          | Name of the MongoDB database.                                                                                                        |
| `MONGO_PORT`                        | Port for MongoDB to accept connections. Default is 27017.                                                                            |
| `MONGO_ROOT_PASSWORD`               | Password associated with the root user account for the MongoDB.                                                                      |
| `MONGO_ROOT_USERNAME`               | Initial root username for the MongoDB.                                                                                               |
| `MONGO_USER_PASSWORD`               | Password associated with the normal user account for the MongoDB.                                                                      |
| `MONGO_USER_USERNAME`               | Initial normal username for the MongoDB.                                                                                               |
| `TYPESENSE_PORT`                    | Port for Typesense to accept connections. Default is 8108.                                                                           |
| `TYPESENSE_API_KEY`                 | Typesense server API key.                                                                                                            |

### Watchtower

| Variable                | Description                            |
| ----------------------- | -------------------------------------- |
| `AWS_ACCESS_KEY_ID`     | AWS access key id to access ECR image. |
| `AWS_SECRET_ACCESS_KEY` | AWS secret access key to access ECR.   |

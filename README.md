# Online Casino White-label Solution - Backend API

This repository contains the backend API for the **Online Casino White-label Solution**, built using **Fastify**.  
The API serves the functionality for managing casino games, member accounts, promotions, payments, and other core features of the casino platform.

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Tech Stack](#tech-stack)
- [Project Structure](#project-structure)
- [Installation](#installation)
- [Configuration](#configuration)
- [Database Migrations and Seeders](#database-migrations-and-seeders)
- [Available Scripts](#available-scripts)
- [API Documentation](#api-documentation)
- [Typesense Integration](#typesense-integration)
- [Troubleshooting](#troubleshooting)
- [Continuous Integration](#continuous-integration)
- [License](#license)
- [Learn More](#learn-more)
- [System Architecture](#system-architecture)

## Overview

This backend API supports the **Online Casino White-label Solution**, a customizable platform that enables multiple clients to launch and manage their branded online casino. The API provides robust management for games, users, transactions, promotions, and payments. With multi-tenant support, real-time communication, and integration with various external services (such as payment gateways, game providers, and marketing tools), the API is built for flexibility, scalability, and security. Its modular design ensures easy customization, making it suitable for startups and established businesses alike.

## Features

- **Member Account Management**:

  - Register, login, and manage user profiles.
  - Multi-factor authentication (MFA) with Firebase.
  - Role-based access control for admins and users.

- **Game Management**:

  - Manage game categories and providers.
  - Configure game settings (limits, access controls).
  - Add new games and monitor game performance.

- **Transaction and Payment Management**:

  - Support for multiple currencies and payment methods.
  - Secure handling of deposits, withdrawals, and balance transfers.
  - Integration with popular payment gateways.

- **Bonus and Promotion Management**:

  - Create custom promotions and bonuses.
  - Schedule promotions based on criteria (time, event triggers).
  - Monitor promotion effectiveness with real-time metrics.

- **Real-time Communication**:

  - WebSocket-based updates for real-time player interactions.
  - Admin notifications and player messaging systems.

- **SSO Integration**:

  - Multi-tenant support with Single Sign-On (SSO) for a seamless user experience across client platforms.

- **Logging and Monitoring**:

  - Detailed logging using Pino.
  - Error tracking with Sentry, including alerts and dashboards.

- **Performance**:
  - Optimized for high traffic loads, supporting thousands of concurrent players.
  - Integrated caching mechanism to minimize database load.

## Tech Stack

- **Backend Framework**: Fastify
- **Database**: PostgreSQL, MongoDB
- **Authentication**: Firebase Authentication with Identity Platform
- **Real-time Communication**: Socket.io
- **Storage**: AWS S3
- **Containerization**: Docker
- **Monitoring & Logging**: Pino, Sentry

## Project Structure

```bash
wlsapi/
├── .husky/
├── config/
│   ├── elk/
│   │   └── filebeat/
│   │   │   ├── Dockerfile.filebeat
│   │   │   └── filebeat.yml
│   ├── changelog-template.hbs
│   ├── commitlint.config.js
│   ├── eslint.config.js
│   ├── prettier.config.js
│   ├── sonar-project.properties
│   └── others
├── src/
│   ├── hooks/
│   ├── modules/
│   ├── migrations/
│   ├── models/
│   ├── plugins/
│   ├── schemas/
│   ├── seeders/
│   ├── utils/
│   ├── app.js
│   └── route.js
├── locales/
├── logs/
├── test/
├── .dockerignore
├── .editorconfig
├── .env.example
├── .gitignore
├── .nvmrc
├── .prettierignore
├── bitbucket-pipelines.yml
├── CHANGELOG.md
├── compose.yaml
├── Dockerfile
├── package-lock.json
├── package.json
└── README.md
```
## Installation



### Prerequisites

- [Node.js](https://nodejs.org/en/learn/getting-started/how-to-install-nodejs) (v20+)
- [Docker Desktop or Docker Engine](https://docs.docker.com/get-started/get-docker/) with [Docker Compose](https://docs.docker.com/get-started/workshop/08_using_compose/)

### Steps - Setting up the core services

1. Clone the docker repository [here](https://bitbucket.org/aiodintech/deployment-docker/src/main/) first to initiate all the core-services, such as postgres, mongo, redis etc.

   ```sh
   git clone https://bitbucket.org/aiodintech/deployment-docker.git
   ```

2. Navigate into the core services directory.

   ```sh
   cd deployment-docker/core_services
   ```

2. Create a `.env` file in the root directory and configure the environment variables accordingly.

   ```sh
   cp .env.example .env
   ```

3. Build and run the core services with Docker Compose:

   ```sh
   docker compose up -d
   ```
### Steps - Setting up the application

1. Clone the repository:

   ```sh
   git clone https://bitbucket.org/aiodintech/wlsapi.git
   cd wlsapi
   ```

2. Install the dependencies:

   ```sh
   npm install
   ```

3. Create a `.env` file in the root directory and configure the environment variables accordingly.

   ```sh
   cp .env.example .env
   ```

4. Build and run your application with Compose:

    ```sh
    docker compose up -d
    ```

    Learn more about setting up Docker [here](https://app.clickup.com/3712436/v/dc/3h9dm-61096/3h9dm-146876).


## Configuration

After copying the `.env.example` file to `.env`, configure the following environment variables:

| Variable                       | Description                                                                  |
| -------------------------------| ---------------------------------------------------------------------------- |
| `API_KEY_NAME`                 | Name of the API key for authentication in Swagger documentation.             |
| `APP_NAME`                     | Name of the application.                                                     |
| `AWS_ACCESS_KEY_ID`            | AWS Access Key ID for authentication.                                        |
| `AWS_BUCKET`                   | The name of the S3 bucket for file storage.                                  |
| `AWS_REGION`                   | AWS Region where the resources (e.g., S3 bucket) are located.                |
| `AWS_S3_PATH`                  | Path in the S3 bucket where files will be stored.                            |
| `AWS_SECRET_ACCESS_KEY`        | AWS Secret Access Key for authentication.                                    |
| `BASE_URL`                     | Base URL of the application.                                                 |
| `BASE_CURRENCY`                | Base currency of the system.                                                 |
| `DB_DIALECT`                   | Specifies the type of database using with Sequelize.                         |
| `DEFAULT_LOCALE`               | Default locale used in system                                                |
| `DOCKER_CONTAINER`             | True if you want to use Docker. Default is `false`.                          |
| `DOCKER_PORT`                  | The host bridge port to access to localhost port in docker container. Default is `3000`.|
| `ELASTIC_STACK_VERSION`        | Version of Elastic products (Elacticsearch, Kibana, etc.).                   |
| `EXTERNAL_DOCS_DESCRIPTION`    | Description for external documentation in Swagger.                           |
| `EXTERNAL_DOCS_URL`            | URL for external documentation in Swagger.                                   |
| `FASTIFY_ADDRESS`              | Host address Fastify will bind to. Use `0.0.0.0` for Docker.                 |
| `FASTIFY_PORT`                 | Port on which the Fastify server will run. Default is `3000`.                |
| `GEOIP_LICENSE_KEY`            | GeoIP license key for periodic updates of the data files for accurate and up-to-date geolocation. |
| `IMAGE_TAG`                    | Specify the image tag for the Docker images that are pushed to the Amazon Elastic Container Registry (ECR). |
| `LLS_CDN_BASE_URL`             | AWS CDN url to access the translation file exported from Language Localisation System to AWS. |
| `LOG_LEVEL`                    | Minimum logging level that system will log. Default is `trace`.              |
| `LOGSTASH_HOST`                | Hostname or IP address of the Logstash server.                               |
| `LOGSTASH_PORT`                | Port on which Logstash listens for incoming log data. Default is `5044`.     |
| `JWT_ALGORITHM`                | The signing algorithm used to digitally sign and verify JWTs.                |
| `JWT_AUDIENCE`                 | Identifies the recipients that the JWT is intended for.                      |
| `JWT_ISSUER`                   | Identifies the principal that issued the JWT.                                |
| `JWT_SECRET`                   | The secret key used to digitally sign and verify JWTs.                       |
| `MONGO_DB`                     | Name of the MongoDB database.                                                |
| `MONGO_HOST`                   | Host address of the MongoDB instance.                                        |
| `MONGO_ROOT_PASSWORD`          | Password associated with the root user account for the MongoDB.              |
| `MONGO_ROOT_USERNAME`          | Initial root username for the MongoDB.                                       |
| `MONGO_PORT`                   | Port for MongoDB to accept connections. Default is 27017.                    |
| `NODE_ENV`                     | Specifies environment in which the app is running.                           |
| `KAFKA`                        | Enables or disables Kafka integration. Set to `true` to enable.              |
| `KAFKA_BROKERS`                | Comma-separated list of Kafka broker addresses (host:port).                  |
| `POSTGRES_DB`                  | Database name in PostgreSQL.                                                 | 
| `POSTGRES_HOST`                | Host address of the PostgreSQL server. May use 'postgres' for Docker.        |
| `POSTGRES_PASSWORD`            | Password associated with the PostgreSQL user.                                |
| `POSTGRES_PORT`                | Port for PostgreSQL connections. Default is `5432`.                          |
| `POSTGRES_USER`                | Username for PostgreSQL authentication.                                      |
| `POSTGRES_READ_REPLICA_1_HOST`     | Read replica host in this format. POSTGRES_READ_REPLICA_{digit}_HOST     |
| `POSTGRES_READ_REPLICA_1_PORT`     | Read replica port in this format. POSTGRES_READ_REPLICA_{digit}_PORT     |
| `POSTGRES_READ_REPLICA_1_DB`       | Read replica db in this format. POSTGRES_READ_REPLICA_{digit}_DB         |
| `POSTGRES_READ_REPLICA_1_USER`     | Read replica user in this format. POSTGRES_READ_REPLICA_{digit}_USER     |
| `POSTGRES_READ_REPLICA_1_PASSWORD` | Read replica password in this format. POSTGRES_READ_REPLICA_{digit}_PASSWORD |
| `REDIS_HOST`                   | Host address of the Redis instance. May use 'redis' for Docker.              | 
| `REDIS_PASSWORD`               | Password used for authenticating with the Redis instance.                    |
| `REDIS_RATE_LIMIT_CONNECT_TIMEOUT` | The Redis connection timeout limit value for rate limit plugin.          | 
| `REDIS_PORT`                   | Port for Redis connections. Default is `6379`.                               |
| `SENTRY_DSN`                   | DSN tells a Sentry SDK where to send events so the events are associated with the correct project. |
| `SERVER_URL_DEV`               | Development server URL for Swagger documentation.                            |
| `SERVER_URL_PROD`              | Production server URL for Swagger documentation.                             |
| `SERVER_URL_STAGING`           | Staging server URL for Swagger documentation.                                |
| `SWAGGER_THEME`                | The UI theme of Swagger documentation. Default is `light`                  |
| `SWAGGER_UI_PATH`              | The UI URL path of Swagger documentation                                     |
| `TYPESENSE_HOST`               | Hostname or IP address of the Typesense server.                              |
| `TYPESENSE_PORT`               | Port on which Typesense is running. Default is usually 8108.                 |
| `TYPESENSE_PROTOCOL`           | Protocol to use for Typesense connection (http or https).                    |
| `TYPESENSE_API_KEY`            | API key for authenticating with the Typesense server.                        |

Ensure that all these variables are correctly configured for both **development** and **production** environments.

## Database Migrations and Seeders


### Postgres Database

The project uses Sequelize CLI to run postgres migrations and seeders to manage the database schema and populate initial data.

⚠️ Attention: 
  1. Since Sequelize v6 does not yet support ESM, we need to manually change the **migration**, **seeder** files from .js to .cjs.
  2. The **model** file code needs to be updated to use ESM format.

#### Commands

- To create migration

  ```sh
  npm run migrate:create <migration name>
  ```
  - migration name: kebab-case, example: add-name-to-users
  - need to define the new column in the model file
  
- To apply migrations:

  ```sh
  npm run migrate
  ```

- To revert only the most recent migration:

  ```sh
  npm run migrate:rollback
  ```

- To create seeder:

  ```sh
  npm run seed:create <seeder name>
  ```
  - seeder name: kebab-case, example: default-users

- To run the project default seeders:
  - need to add the new default seeder into the _database-seeder.cjs

  ```sh
  npm run seed
  ```

- To run specific seeder:

  ```sh
  npm run seed:one <seeder name>
  ```
 - seeder name: file of seeder, example: 20241111034417-demo-user.cjs

 ### Mongo Database


## Available Scripts

In the project directory, you can run:

- `npm run dev`: Start the app in dev mode. Open [http://localhost:3000](http://localhost:3000) to view it in the browser.
- `npm start`: For production mode.
- `npm run test`: Run the test cases.
- `npm run lint`: Lint the code.
- `npm run lint-fix`: Potentially fix the code linting issues.
- `npm run format`: Format the code.
- `npm run version`: Generate changelog for the current version.

## API Documentation

API documentation is auto-generated using Swagger. To view the interactive API docs, start the server and visit:

- [http://localhost:3000/api-docs](http://localhost:3000/api-docs)

The documentation includes the following endpoints:

- `/user`: Manage user accounts (CRUD operations)

You can test endpoints directly from the Swagger UI.

## Typesense Integration

This project includes integration with Typesense, a fast, typo-tolerant search engine. Typesense is used for efficient and accurate search functionality within the application.

### What is Typesense?

Typesense is an open-source, typo-tolerant search engine that is designed to be easy to use, fast, and accurate. It's a great alternative to more complex search solutions, offering:

- Typo tolerance: It can understand and correct user typos automatically.
- Fast searches: Optimized for speed, returning results in milliseconds.
- Simple setup: Easy to install and configure, with a clean API.
- Sorting and faceting: Supports sorting results and faceted search.

For developers new to Typesense, it provides a way to add powerful search capabilities to your application without the complexity of some other search engines.

### Configuration

Typesense is configured using environment variables. Ensure the following variables are set in your `.env` file:

- `TYPESENSE_HOST`: The hostname or IP address of your Typesense server.
- `TYPESENSE_PORT`: The port on which Typesense is running (default is usually 8108).
- `TYPESENSE_PROTOCOL`: The protocol to use for connecting to Typesense (http or https).
- `TYPESENSE_API_KEY`: The API key for authenticating with the Typesense server.

### Usage

The Typesense client is initialized in the `typesense.plugin.js` file. This plugin sets up the connection to the Typesense server and makes the client available throughout the application.

To use Typesense in your routes or services:

1. Access the Typesense client through the Fastify instance:

  ```javascript
  const typesenseClient = fastify.typesense;
  ```

## Advanced Filtering

The API supports advanced filtering capabilities that allow you to query data with complex conditions, including filtering by related models through associations.

### Filter Syntax

Filters use the following format in query parameters:

filter_`<field>`_`<operator>` = `<value>`

Where:
- `field`: The name of the field to filter on (can include dot notation for associations)
- `operator`: The comparison operator to use
- `value`: The value to compare against

### Available Operators

| Operator | Description | Example |
|----------|-------------|---------|
| `eq` | Equal to | `filter_status_eq=active` |
| `ne` | Not equal to | `filter_status_ne=inactive` |
| `gt` | Greater than | `filter_price_gt=100` |
| `gte` | Greater than or equal to | `filter_price_gte=100` |
| `lt` | Less than | `filter_price_lt=100` |
| `lte` | Less than or equal to | `filter_price_lte=100` |
| `like` | Contains (case sensitive) | `filter_name_like=%John%` |
| `notLike` | Does not contain (case sensitive) | `filter_name_notLike=%John%` |
| `iLike` | Contains (case insensitive) | `filter_name_iLike=%john%` |
| `notILike` | Does not contain (case insensitive) | `filter_name_notILike=%john%` |
| `in` | In array of values | `filter_status_in=active,pending` |
| `notIn` | Not in array of values | `filter_status_notIn=inactive,deleted` |
| `between` | Between two values | `filter_price_between=10,100` |
| `notBetween` | Not between two values | `filter_price_notBetween=10,100` |

### Filtering on Associations

You can filter on associated models using dot notation:
`filter_association.field_operator=value`

For example, to filter users by their wallet balance:
`filter_wallet.balance_gt=1000`

You can also filter on deeply nested associations:
`filter_businessPartner.category.subCategory.name_eq=Freespin`

### Examples

1. Get all active users:

    `GET /api/users?filter_status_eq=active`
2. Get users with balance greater than 1000:

    `GET /api/users?filter_wallet.balance_gt=1000`

3. Get transactions in a date range:

   `GET /api/transactions?filter_createdAt_between=2023-01-01,2023-12-31`

4. Combining multiple filters:

   `GET /api/users?filter_status_eq=active&filter_wallet.balance_gt=1000&filter_createdAt_gte=2023-01-01`

### Pagination and Sorting

Filtering can be combined with pagination and sorting parameters:

`GET /api/users?filter_status_eq=active&page=2&limit=10&sortBy=createdAt:desc`

This will return the second page of active users, 10 per page, sorted by creation date in descending order.

## Troubleshooting

- **Server fails to start**:

  - Ensure the `.env` file is configured correctly and that all services (PostgreSQL, MongoDB, etc.) are running.
  - On your local, you may need to run `npm i` to install node_modules first.

- **Database connection issues**:
  - Check that the correct database credentials are being used.
  - Make sure the value is match with your docker setup value.
  - Ensure the database is running on the specified `DB_HOST` and `DB_PORT`.

## Continuous Integration

This project uses **GitHub Actions** for continuous integration. Each time a pull request is opened, tests and linting are run automatically. To set up the CI locally, ensure that you have the following dependencies:

- Docker with Docker Compose
- Node.js

The CI pipeline includes the following steps:

- Run tests (`npm run test`)
- Lint code (`npm run lint`)

## License

This project is licensed under the MIT License, which allows for commercial use, modification, distribution, and private use of the software. See the [LICENSE](LICENSE) file for full details.

## Learn More

- **Docker**: [Get Started with Docker](https://docs.docker.com/get-started/)
- **Fastify**: [Fastify Official Documentation](https://fastify.dev/docs/latest/)
- **PostgreSQL**: [PostgreSQL Performance Tuning Guide](https://www.postgresql.org/docs/current/performance-tips.html)
- **MongoDB**: [MongoDB Start with Guides](https://www.mongodb.com/docs/guides/)
- **Firebase Authentication**: [Getting Started with Firebase](https://firebase.google.com/docs/auth)
- **Elastic Stack**: [Getting started with the Elastic Stack and Docker Compose](https://www.elastic.co/blog/getting-started-with-the-elastic-stack-and-docker-compose)
- **Sequelize**: [Sequelize Migration & Seeder Guide](https://sequelize.org/docs/v6/other-topics/migrations/)
- **Typesense**: [Official Documentation](https://typesense.org/docs/)


## System Architecture

Here's a high-level overview of the system architecture:


## Badges
[![Quality gate](https://sonarqube.sgrts.com/api/project_badges/quality_gate?project=aiodintech_wlsapi_70823b22-a2c6-4c8b-94ea-067d5b5cfbb6&token=sqb_244c8c8d6227170491578ace7c575aa8b081c6e0)](https://sonarqube.sgrts.com/dashboard?id=aiodintech_wlsapi_70823b22-a2c6-4c8b-94ea-067d5b5cfbb6)
